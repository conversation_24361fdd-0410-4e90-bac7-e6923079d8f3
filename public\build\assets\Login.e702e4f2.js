import{n as w,r as t,o as m,c as d,b as o,B as n,F as j,H as v,L as y,t as V,C as c,a as r,y as $,z as C,E as J,d as u}from"./app.5bf25e6f.js";import{J as L}from"./AuthenticationCard.d2e642d8.js";import{J as B}from"./AuthenticationCardLogo.c737ac3b.js";import{_ as E}from"./Button.de389ba7.js";import{_ as F}from"./Input.f95445aa.js";import{_ as H}from"./Checkbox.c93fed4a.js";import{_ as N}from"./Label.a34a8f2d.js";import{_ as q}from"./ValidationErrors.40b6029f.js";import{_ as x}from"./plugin-vue_export-helper.21dcd24c.js";const P=w({components:{Head:v,JetAuthenticationCard:L,JetAuthenticationCardLogo:B,JetButton:E,JetInput:F,JetCheckbox:H,JetLabel:N,JetValidationErrors:q,Link:y},props:{canResetPassword:Boolean,status:String},data(){return{form:this.$inertia.form({email:"",password:"",remember:!1})}},methods:{submit(){this.form.transform(e=>({...e,remember:this.form.remember?"on":""})).post(this.route("login"),{onFinish:()=>this.form.reset("password")})}}}),R={key:0,class:"mb-4 font-medium text-sm text-green-600"},S={class:"mt-4"},U={class:"block mt-4"},z={class:"flex items-center"},A=r("span",{class:"ml-2 text-sm text-gray-600"},"Remember me",-1),D={class:"flex items-center justify-end mt-4"},I=u(" Forgot your password? "),M=u(" Log in ");function T(e,s,G,K,O,Q){const p=t("Head"),f=t("jet-authentication-card-logo"),_=t("jet-validation-errors"),i=t("jet-label"),l=t("jet-input"),b=t("jet-checkbox"),h=t("Link"),g=t("jet-button"),k=t("jet-authentication-card");return m(),d(j,null,[o(p,{title:"Log in"}),o(k,null,{logo:n(()=>[o(f)]),default:n(()=>[o(_,{class:"mb-4"}),e.status?(m(),d("div",R,V(e.status),1)):c("",!0),r("form",{onSubmit:s[3]||(s[3]=J((...a)=>e.submit&&e.submit(...a),["prevent"]))},[r("div",null,[o(i,{for:"email",value:"Email"}),o(l,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e.form.email,"onUpdate:modelValue":s[0]||(s[0]=a=>e.form.email=a),required:"",autofocus:""},null,8,["modelValue"])]),r("div",S,[o(i,{for:"password",value:"Password"}),o(l,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:e.form.password,"onUpdate:modelValue":s[1]||(s[1]=a=>e.form.password=a),required:"",autocomplete:"current-password"},null,8,["modelValue"])]),r("div",U,[r("label",z,[o(b,{name:"remember",checked:e.form.remember,"onUpdate:checked":s[2]||(s[2]=a=>e.form.remember=a)},null,8,["checked"]),A])]),r("div",D,[e.canResetPassword?(m(),$(h,{key:0,href:e.route("password.request"),class:"underline text-sm text-gray-600 hover:text-gray-900"},{default:n(()=>[I]),_:1},8,["href"])):c("",!0),o(g,{class:C(["ml-4",{"opacity-25":e.form.processing}]),disabled:e.form.processing},{default:n(()=>[M]),_:1},8,["class","disabled"])])],32)]),_:1})],64)}var re=x(P,[["render",T]]);export{re as default};
