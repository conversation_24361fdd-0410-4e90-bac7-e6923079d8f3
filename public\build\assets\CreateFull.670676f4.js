import{v as M,Q as te,o as n,c as d,b as u,u as r,a as l,B as A,d as I,t as m,E as b,z as K,X as f,C as h,F as _,D as g,K as v,O as re,H as le,L as se,S as j,R as S}from"./app.5bf25e6f.js";import{A as ie,I as H}from"./AppLayout.14f8c8f6.js";import{_ as x}from"./TextInput.48e8e32c.js";import{D as C}from"./DropdownSearch.7a091d54.js";import{_ as O}from"./SelectInput.16ffd220.js";import{L as ne}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const de={class:"px-4 py-2"},ae={class:"mb-2 flex justify-start max-w-xl"},ue={class:"font-bold text-2xl"},me=l("span",{class:"text-indigo-400 font-medium"}," /",-1),pe={class:""},ce={class:"flex justify-start space-x-2"},be={class:"bg-orange-400 rounded-md shadow max-w-xs",style:{"max-width":"270px"}},fe=l("option",{value:null},null,-1),_e=l("option",{value:"1"},"Male",-1),xe=l("option",{value:"2"},"Female",-1),ye={value:3,selected:""},ge={value:2},ve={value:1},he={value:3},we={value:2},Ve={value:1},ke={key:0,class:"pr-3 pb-3 w-full ltr"},Le={key:1,class:"pr-3 pb-3 w-full ltr"},Se={class:"flex px-1 mx-auto justify-between py-2 max-w-full"},Ae={class:"bg-orange-400 rounded-md shadow w-full max-w-6xl px-3 py-4 flex flex-wrap"},Ce={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Fe={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Ee={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Ue={class:"min-h-full min-w-full"},De={key:0,class:"min-w-full px-1 lg:px-2"},Be=l("div",{class:"inline-flex w-2/4 items-center justify-start"},[l("span",{class:"shadow px-2 bg-indigo-600 text-white max-h-8"},"Requested Tests:")],-1),Me={class:"min-w-full mt-1 flex rounded-md shadow-sm"},Ie={class:"max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"30%",width:"35%"}},Ke=["onUpdate:modelValue"],Oe=["onUpdate:modelValue"],Te=["value"],Pe=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"4%"}}," Flag: ",-1),Re=["onUpdate:modelValue"],$e=["value"],je=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"6%"}}," Device: ",-1),He=["onUpdate:modelValue"],Ne=["value"],ze=["innerHTML"],qe={class:"max-h-8 inline-flex items-center text-xs px-px font-medium border border-r-0 border-gray-300 text-gray-700",style:{"background-color":"#B1F56C","border-color":"#6F6AE6",width:"7%"}},Ge=["onClick"],Qe={key:1,class:"min-w-full px-1 lg:px-2"},Xe=l("div",{class:"inline-flex w-2/4 items-center justify-start mt-1"},[l("span",{class:"shadow px-2 bg-indigo-600 text-white max-h-8"},"Requested Offers:")],-1),Ye={class:"inline-flex w-2/4 mt-1 items-center justify-start"},Je={class:"shadow px-2 py-1 bg-indigo-600 text-white max-h-8"},We={class:"shadow bg-yellow-300 px-2 py-1 text-gray-700 max-h-8"},Ze=["onClick"],eo={class:"min-w-full mt-1 flex rounded-md shadow-sm"},oo={class:"max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"30%",width:"30%"}},to=["onUpdate:modelValue"],ro=["onUpdate:modelValue"],lo=["value"],so=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"4%"}}," Flag: ",-1),io=["onUpdate:modelValue"],no=["value"],ao=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"6%"}}," Device: ",-1),uo=["onUpdate:modelValue"],mo=["value"],po=["innerHTML"],co={class:"flex items-center my-2 ml-2"},bo=l("label",{for:"calcLDLAndVLDL",class:"ml-2 block text-sm text-gray-50"}," Calculate LDL & VLDL ",-1),fo={id:"confirmBox",hidden:"",class:"relative z-50"},_o=l("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1),xo={class:"fixed z-10 inset-0 overflow-y-auto"},yo={class:"flex items-end sm:items-center justify-center min-h-full p-4 text-center sm:p-0"},go={class:"relative bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full rtl"},vo={class:"bg-white"},ho={class:"bg-orange-300 w-full"},wo=l("div",{class:"bg-gray-50 p-2"},[l("p",{class:"text-xl text-gray-800 sm:text-right"}," \u0627\u0644\u0645\u0628\u0644\u063A \u0627\u0644\u0645\u062F\u0641\u0648\u0639 \u0644\u0627 \u064A\u0633\u0627\u0648\u064A \u0627\u0644\u062A\u0643\u0644\u0641\u0629 \u0627\u0644\u0646\u0647\u0627\u0626\u064A\u0629! ")],-1),Vo={class:"flex p-3 mt-2 text-right rtl"},ko={class:"bg-gray-50 p-2 sm:flex sm:flex-row-reverse space-x-2"},Lo={layout:ie},Mo=Object.assign(Lo,{__name:"CreateFull",props:{locale:String,doctors:Array,sidelabs:Array,tests:Array,groups:Array,offers:Array,flags:Array},setup(F){const V=F,N=V.doctors,z=V.sidelabs;let c=M(V.tests.slice()),E=M(V.offers.slice());const U=V.groups,T=V.flags;let o=te({name:null,calcLDLAndVLDL:!1,gender:null,age:null,age_type:3,mobile:null,doctor_id:null,lab_id:null,referred_by:3,tests_cost:0,discount:null,final_cost:0,paid_amount:null,remaining_amount:0,visitTests:[],visitOffers:[]}),w=M([]),k=[],q=s=>{w.push({id:s.id,device_id:s.device_id,flag_id:s.flag_id,result_type:s.result_type,short_name:s.short_name,lab_to_lab_price:s.lab_to_lab_price,lab_to_patient_price:s.lab_to_patient_price,devices:s.devices,results:s.results,value:s.value}),c.splice(c.findIndex(e=>e.id===s.id),1),y()},G=(s,e)=>{c.push(s),w.splice(e,1),y()},Q=s=>{s.tests.forEach(e=>{let t=c[c.findIndex(p=>p.id===e.id)];t&&(w.push(t),c.splice(c.findIndex(p=>p.id===t.id),1))}),U.splice(U.findIndex(e=>e.id===s.id),1),y()},X=s=>{let e=!0;if(s.tests.forEach(t=>{c.some(p=>p.id===t.id)||(e=!1)}),e){let t=[];s.tests.forEach(p=>{let i=c[c.findIndex(L=>L.id===p.id)];t.push(i),c.splice(c.findIndex(L=>L.id===p.id),1)}),k.push({id:s.id,name:s.name,price:s.price,tests:t}),E.splice(E.findIndex(p=>p.id===s.id),1)}else return alert("Some tests already exist, Please delete them first.");y()},Y=(s,e)=>{s.tests.forEach(t=>{c.push(t)}),E.push(s),k.splice(e,1),y()},J=()=>{o.referred_by===1?o.doctor_id=null:(o.referred_by===2||(o.doctor_id=null),o.lab_id=null),y()},W=s=>{o.doctor_id=s.id},Z=s=>{o.lab_id=s.id},y=()=>{let s=0;k.forEach(e=>{s+=e.price}),w.forEach(e=>{if(o.referred_by===1)s+=e.lab_to_lab_price;else if(o.referred_by===2||o.referred_by===3)s+=e.lab_to_patient_price;else return alert("ReferredBy field is required")}),o.tests_cost=s,o.final_cost=o.tests_cost-o.discount,o.remaining_amount=o.final_cost-o.paid_amount},P=()=>{o.paid_amount=o.final_cost,o.remaining_amount=0},ee=()=>{R(),o.paid_amount=o.final_cost,o.remaining_amount=0,B()},oe=()=>{document.getElementById("confirmBox").hidden=!1},R=()=>{document.getElementById("confirmBox").hidden=!0},$=s=>{s&&B(),R()},D=()=>{o.paid_amount!=o.final_cost?oe():B()},B=()=>{(o.discount===null||o.discount=="")&&(o.discount=0),w.forEach(s=>{o.visitTests.push({id:s.id,device_id:s.device_id,flag_id:s.flag_id,value:s.value})}),k.forEach(s=>{let e=[];s.tests.forEach(t=>{e.push({id:t.id,device_id:t.device_id,flag_id:t.flag_id,value:t.value,offer_id:s.id})}),o.visitOffers.push({id:s.id,tests:e})}),o.post(route("patientsVisits.storeFull"),{preserveState:!0,onError:()=>{o.reset("visitTests","visitOffers")},onSuccess:()=>{o.reset()}})};return(s,e)=>(n(),d(_,null,[u(r(le),{title:"Create Patient"}),l("div",de,[l("div",ae,[l("h2",ue,[u(r(se),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("patients")},{default:A(()=>[I(m(s.trans("PatientsList")),1)]),_:1},8,["href"]),me,I(" "+m(s.trans("RegisterANewPatient")),1)])]),l("div",pe,[l("form",{onSubmit:e[27]||(e[27]=b((...t)=>r(D)&&r(D)(...t),["prevent"]))},[l("div",ce,[l("div",be,[l("div",{class:K(["p-3 -mr-3 -mb-5 flex flex-wrap",F.locale=="ar"?"rtl text-right":"ltr text-left"])},[u(x,{modelValue:r(o).name,"onUpdate:modelValue":e[0]||(e[0]=t=>r(o).name=t),onKeydown:e[1]||(e[1]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.name,direction:"rtl",class:"pr-3 pb-3 w-full",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),u(O,{modelValue:r(o).gender,"onUpdate:modelValue":e[2]||(e[2]=t=>r(o).gender=t),modelModifiers:{number:!0},onKeydown:e[3]||(e[3]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.gender,direction:"ltr",class:"pr-3 pb-3 w-1/3",label:"Gender"},{default:A(()=>[fe,_e,xe]),_:1},8,["modelValue","error"]),u(x,{modelValue:r(o).age,"onUpdate:modelValue":e[4]||(e[4]=t=>r(o).age=t),modelModifiers:{number:!0},onKeydown:e[5]||(e[5]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.age,direction:"ltr",class:"pr-3 pb-3 w-1/3",type:"number",label:"Age"},null,8,["modelValue","error"]),u(O,{modelValue:r(o).age_type,"onUpdate:modelValue":e[6]||(e[6]=t=>r(o).age_type=t),modelModifiers:{number:!0},onKeydown:e[7]||(e[7]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.age_type,class:"pr-3 pb-3 w-1/3",label:"Age Type"},{default:A(()=>[l("option",ye,m(s.trans("Year")),1),l("option",ge,m(s.trans("Month")),1),l("option",ve,m(s.trans("Day")),1)]),_:1},8,["modelValue","error"]),u(x,{modelValue:r(o).mobile,"onUpdate:modelValue":e[8]||(e[8]=t=>r(o).mobile=t),onKeydown:e[9]||(e[9]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.mobile,direction:"ltr",class:"pr-3 pb-3 w-1/2",type:"number",label:"Mobile"},null,8,["modelValue","error"]),u(O,{modelValue:r(o).referred_by,"onUpdate:modelValue":e[10]||(e[10]=t=>r(o).referred_by=t),modelModifiers:{number:!0},onKeydown:e[11]||(e[11]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.referred_by,onChange:r(J),class:"pr-3 pb-3 w-1/2",label:"ReferredBy"},{default:A(()=>[l("option",he,m(s.trans("OutPatient")),1),l("option",we,m(s.trans("Doctor")),1),l("option",Ve,m(s.trans("SideLab")),1)]),_:1},8,["modelValue","error","onChange"]),r(o).referred_by===2?(n(),d("div",ke,[l("label",{for:"doctor",class:K(["form-label",F.locale=="ar"?"rtl":"ltr"])},m(s.trans("Doctor"))+":",3),u(C,{id:"doctor",error:r(o).errors.doctor_id,direction:"rtl",onKeydown:e[12]||(e[12]=f(b(()=>{},["prevent"]),["enter"])),options:r(N),onSelect:r(W),CloseOnSelect:!0,fixedLabel:!1,label:"Select Doctor",searchBy:"name",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0627\u0637\u0628\u0627\u0621"},null,8,["error","options","onSelect"])])):h("",!0),r(o).referred_by===1?(n(),d("div",Le,[l("label",{for:"lab_id",class:K(["form-label",F.locale=="ar"?"rtl":"ltr"])},m(s.trans("SideLab"))+":",3),u(C,{id:"lab_id",error:r(o).errors.lab_id,direction:"rtl",onKeydown:e[13]||(e[13]=f(b(()=>{},["prevent"]),["enter"])),options:r(z),onSelect:r(Z),CloseOnSelect:!0,fixedLabel:!1,label:"Select Side Lab",searchBy:"name",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0645\u062E\u062A\u0628\u0631\u0627\u062A \u0627\u0644\u062C\u0627\u0646\u0628\u064A\u0629"},null,8,["error","options","onSelect"])])):h("",!0),u(x,{modelValue:r(o).tests_cost,"onUpdate:modelValue":e[14]||(e[14]=t=>r(o).tests_cost=t),modelModifiers:{number:!0},error:r(o).errors.tests_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"TestsCost"},null,8,["modelValue","error"]),u(x,{modelValue:r(o).discount,"onUpdate:modelValue":e[15]||(e[15]=t=>r(o).discount=t),modelModifiers:{number:!0},onKeydown:e[16]||(e[16]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.discount,direction:"ltr",onInput:e[17]||(e[17]=t=>r(y)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"Discount"},null,8,["modelValue","error"]),u(x,{modelValue:r(o).final_cost,"onUpdate:modelValue":e[18]||(e[18]=t=>r(o).final_cost=t),modelModifiers:{number:!0},error:r(o).errors.final_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"FinalCost"},null,8,["modelValue","error"]),u(x,{modelValue:r(o).paid_amount,"onUpdate:modelValue":e[19]||(e[19]=t=>r(o).paid_amount=t),modelModifiers:{number:!0},onKeydown:e[20]||(e[20]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.paid_amount,direction:"ltr",onInput:e[21]||(e[21]=t=>r(y)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"PaidAmount"},null,8,["modelValue","error"]),u(x,{modelValue:r(o).remaining_amount,"onUpdate:modelValue":e[22]||(e[22]=t=>r(o).remaining_amount=t),modelModifiers:{number:!0},error:r(o).errors.remaining_amount,direction:"ltr",class:"pr-3 pb-3 w-full",disabled:"disabled",type:"number",label:"RemainingAmount"},null,8,["modelValue","error"])],2),l("div",Se,[l("button",{type:"button",class:"btn-green",onClick:e[23]||(e[23]=(...t)=>r(P)&&r(P)(...t))},m(s.trans("FullyPaid")),1),u(ne,{loading:r(o).processing,class:"btn-indigo",onClick:r(D)},{default:A(()=>[I(m(s.trans("Save")+" & "+s.trans("Print")),1)]),_:1},8,["loading","onClick"])])]),l("div",Ae,[l("div",Ce,[u(C,{class:"bg-white rounded-md",options:r(c),onSelect:r(q),CloseOnSelect:!1,fixedLabel:!0,label:"Select Test",searchBy:"short_name",placeholder:"Search for tests"},null,8,["options","onSelect"])]),l("div",Fe,[u(C,{class:"bg-white rounded-md",options:r(U),onSelect:r(Q),CloseOnSelect:!1,fixedLabel:!1,label:"Select Group",searchBy:"name",placeholder:"Search for groups"},null,8,["options","onSelect"])]),l("div",Ee,[u(C,{class:"bg-white rounded-md",options:r(E),onSelect:r(X),CloseOnSelect:!0,fixedLabel:!1,label:"Select Offer",searchBy:"name",placeholder:"Search for Offers"},null,8,["options","onSelect"])]),l("div",Ue,[r(w).length>0?(n(),d("div",De,[Be,(n(!0),d(_,null,g(r(w),(t,p)=>(n(),d("div",{key:p,class:"flex justify-start items-start"},[l("div",Me,[l("span",Ie,m(p+1+" - "+t.short_name)+": ",1),t.result_type===0?v((n(),d("input",{key:0,onKeydown:e[24]||(e[24]=f(b(()=>{},["prevent"]),["enter"])),type:"text","onUpdate:modelValue":i=>t.value=i,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},null,40,Ke)),[[j,t.value]]):h("",!0),t.result_type===1?v((n(),d("select",{key:1,"onUpdate:modelValue":i=>t.value=i,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},[(n(!0),d(_,null,g(t.results,i=>(n(),d("option",{key:i.id,value:i.name},m(i.name),9,Te))),128))],8,Oe)),[[S,t.value]]):h("",!0),Pe,v(l("select",{"onUpdate:modelValue":i=>t.flag_id=i,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(_,null,g(r(T),i=>(n(),d("option",{class:"p-2",key:i.id,value:i.id},m(i.name),9,$e))),128))],8,Re),[[S,t.flag_id]]),je,v(l("select",{"onUpdate:modelValue":i=>t.device_id=i,class:"max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(_,null,g(t.devices,i=>(n(),d("option",{key:i.id,value:i.pivot.device_id},m(i.name),9,Ne))),128))],8,He),[[S,t.device_id]]),l("span",{class:"hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-r-0 border-gray-300 bg-white text-gray-500",style:{"border-color":"#6F6AE6",width:"18%"},innerHTML:t.devices[t.devices.findIndex(i=>i.pivot.device_id===t.device_id)]?t.devices[t.devices.findIndex(i=>i.pivot.device_id===t.device_id)].pivot.normal_range:""},null,8,ze),l("span",qe,m(r(o).referred_by===1?t.lab_to_lab_price:t.lab_to_patient_price)+" IQD ",1),l("span",{title:"Remove Test",onClick:i=>r(G)(t,p),class:"max-h-8 text-sm inline-flex items-center px-2 rounded-r-md border border-l-0 bg-red-600 hover:bg-red-500 cursor-pointer",style:{"border-color":"#6F6AE6",width:"3%"}},[u(H,{name:"x",class:"h-4 w-4"})],8,Ge)])]))),128))])):h("",!0),r(k).length>0?(n(),d("div",Qe,[Xe,(n(!0),d(_,null,g(r(k),(t,p)=>(n(),d("div",{key:p},[l("div",Ye,[l("span",Je,m(t.name),1),l("span",We,m(t.price+" IQD"),1),l("span",{title:"Remove Offer",onClick:i=>r(Y)(t,p),class:"shadow max-h-8 text-sm px-2 py-1 bg-red-600 hover:bg-red-500 cursor-pointer"},[u(H,{name:"x",class:"h-6 w-6"})],8,Ze)]),(n(!0),d(_,null,g(t.tests,(i,L)=>(n(),d("div",{key:L,class:"min-w-full flex items-start"},[l("div",eo,[l("span",oo,m(L+1+" - "+i.short_name)+": ",1),i.result_type===0?v((n(),d("input",{key:0,onKeydown:e[25]||(e[25]=f(b(()=>{},["prevent"]),["enter"])),type:"text","onUpdate:modelValue":a=>i.value=a,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},null,40,to)),[[j,i.value]]):h("",!0),i.result_type===1?v((n(),d("select",{key:1,"onUpdate:modelValue":a=>i.value=a,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},[(n(!0),d(_,null,g(i.results,a=>(n(),d("option",{key:a.id,value:a.name},m(a.name),9,lo))),128))],8,ro)),[[S,i.value]]):h("",!0),so,v(l("select",{"onUpdate:modelValue":a=>i.flag_id=a,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(_,null,g(r(T),a=>(n(),d("option",{class:"p-2",key:a.id,value:a.id},m(a.name),9,no))),128))],8,io),[[S,i.flag_id]]),ao,v(l("select",{"onUpdate:modelValue":a=>i.device_id=a,class:"max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(_,null,g(i.devices,a=>(n(),d("option",{key:a.id,value:a.pivot.device_id},m(a.name),9,mo))),128))],8,uo),[[S,i.device_id]]),l("span",{class:"hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-gray-300 bg-white text-gray-500",style:{"border-color":"#6F6AE6",width:"18%"},innerHTML:i.devices[i.devices.findIndex(a=>a.pivot.device_id===i.device_id)]?i.devices[i.devices.findIndex(a=>a.pivot.device_id===i.device_id)].pivot.normal_range:""},null,8,po)])]))),128))]))),128))])):h("",!0),l("div",co,[v(l("input",{id:"calcLDLAndVLDL","onUpdate:modelValue":e[26]||(e[26]=t=>r(o).calcLDLAndVLDL=t),type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[re,r(o).calcLDLAndVLDL]]),bo])])])])],32)]),l("div",fo,[_o,l("div",xo,[l("div",yo,[l("div",go,[l("div",vo,[l("div",ho,[wo,l("div",Vo,[u(x,{modelValue:r(o).final_cost,"onUpdate:modelValue":e[28]||(e[28]=t=>r(o).final_cost=t),modelModifiers:{number:!0},error:r(o).errors.final_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"\u0627\u0644\u062A\u0643\u0644\u0641\u0629 \u0627\u0644\u0646\u0647\u0627\u0626\u064A\u0629"},null,8,["modelValue","error"]),u(x,{modelValue:r(o).paid_amount,"onUpdate:modelValue":e[29]||(e[29]=t=>r(o).paid_amount=t),modelModifiers:{number:!0},onKeydown:e[30]||(e[30]=f(b(()=>{},["prevent"]),["enter"])),direction:"ltr",error:r(o).errors.paid_amount,onInput:e[31]||(e[31]=t=>r(y)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"\u0627\u0644\u0645\u0628\u0644\u063A \u0627\u0644\u0645\u062F\u0641\u0648\u0639 "},null,8,["modelValue","error"])])])]),l("div",ko,[l("button",{type:"button",onClick:e[32]||(e[32]=t=>r(ee)()),class:"btn-green2"},"\u0645\u0633\u0627\u0648\u0627\u0629 \u0648\u0627\u0633\u062A\u0645\u0631\u0627\u0631"),l("button",{type:"button",onClick:e[33]||(e[33]=t=>r($)(!0)),class:"btn-green2"},"\u0627\u0633\u062A\u0645\u0631\u0627\u0631"),l("button",{type:"button",onClick:e[34]||(e[34]=t=>r($)(!1)),class:"btn-indigo2"},"\u0627\u0644\u063A\u0627\u0621")])])])])])])],64))}});export{Mo as default};
