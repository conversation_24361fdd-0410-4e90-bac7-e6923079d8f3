import{n as g,r as n,o as s,c as a,b as o,B as l,F as c,H as b,a as d,E as f,z as j,d as i}from"./app.5bf25e6f.js";import{J as k}from"./AuthenticationCard.d2e642d8.js";import{J as $}from"./AuthenticationCardLogo.c737ac3b.js";import{_ as C}from"./Button.de389ba7.js";import{_ as V}from"./Input.f95445aa.js";import{_ as J}from"./Label.a34a8f2d.js";import{_ as w}from"./ValidationErrors.40b6029f.js";import{_ as B}from"./plugin-vue_export-helper.21dcd24c.js";const H=g({components:{Head:b,JetAuthenticationCard:k,JetAuthenticationCardLogo:$,JetButton:C,JetInput:V,JetLabel:J,JetValidationErrors:w},data(){return{recovery:!1,form:this.$inertia.form({code:"",recovery_code:""})}},methods:{toggleRecovery(){this.recovery^=!0,this.$nextTick(()=>{this.recovery?(this.$refs.recovery_code.focus(),this.form.code=""):(this.$refs.code.focus(),this.form.recovery_code="")})},submit(){this.form.post(this.route("two-factor.login"))}}}),R={class:"mb-4 text-sm text-gray-600"},T=i(" Please confirm access to your account by entering the authentication code provided by your authenticator application. "),U=i(" Please confirm access to your account by entering one of your emergency recovery codes. "),E={key:0},F={key:1},L={class:"flex items-center justify-end mt-4"},N=i(" Use a recovery code "),z=i(" Use an authentication code "),A=i(" Log in ");function P(e,t,x,I,M,S){const _=n("Head"),p=n("jet-authentication-card-logo"),y=n("jet-validation-errors"),m=n("jet-label"),u=n("jet-input"),v=n("jet-button"),h=n("jet-authentication-card");return s(),a(c,null,[o(_,{title:"Two-factor Confirmation"}),o(h,null,{logo:l(()=>[o(p)]),default:l(()=>[d("div",R,[e.recovery?(s(),a(c,{key:1},[U],64)):(s(),a(c,{key:0},[T],64))]),o(y,{class:"mb-4"}),d("form",{onSubmit:t[3]||(t[3]=f((...r)=>e.submit&&e.submit(...r),["prevent"]))},[e.recovery?(s(),a("div",F,[o(m,{for:"recovery_code",value:"Recovery Code"}),o(u,{ref:"recovery_code",id:"recovery_code",type:"text",class:"mt-1 block w-full",modelValue:e.form.recovery_code,"onUpdate:modelValue":t[1]||(t[1]=r=>e.form.recovery_code=r),autocomplete:"one-time-code"},null,8,["modelValue"])])):(s(),a("div",E,[o(m,{for:"code",value:"Code"}),o(u,{ref:"code",id:"code",type:"text",inputmode:"numeric",class:"mt-1 block w-full",modelValue:e.form.code,"onUpdate:modelValue":t[0]||(t[0]=r=>e.form.code=r),autofocus:"",autocomplete:"one-time-code"},null,8,["modelValue"])])),d("div",L,[d("button",{type:"button",class:"text-sm text-gray-600 hover:text-gray-900 underline cursor-pointer",onClick:t[2]||(t[2]=f((...r)=>e.toggleRecovery&&e.toggleRecovery(...r),["prevent"]))},[e.recovery?(s(),a(c,{key:1},[z],64)):(s(),a(c,{key:0},[N],64))]),o(v,{class:j(["ml-4",{"opacity-25":e.form.processing}]),disabled:e.form.processing},{default:l(()=>[A]),_:1},8,["class","disabled"])])],32)]),_:1})],64)}var Y=B(H,[["render",P]]);export{Y as default};
