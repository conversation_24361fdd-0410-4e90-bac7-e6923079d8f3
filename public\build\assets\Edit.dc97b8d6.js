import{Q as x,k as h,o as s,c as a,b as y,u as e,a as r,E as w,t as g,C as d,B as k,F as P,H as C,d as L}from"./app.5bf25e6f.js";import{A as U}from"./AppLayout.14f8c8f6.js";import{L as j}from"./LoadingButton.c8fb65b2.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const S={class:"p-2 sm:p-4 lg:p-6 max-w-4xl"},B={class:"bg-white rounded-md shadow max-w-xl"},F={class:"p-6"},O=r("label",{for:"headerPhoto2"},"Header Photo",-1),E={key:0,class:"font-bold text-red-600"},H=["src"],N=["src"],R=r("label",{for:"footerPhoto2"},"Footer Photo",-1),V={key:3,class:"font-bold text-red-600"},A=["src"],D=["src"],T={key:0,class:"px-4 py-2 text-red-600"},M={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-between items-center"},Q={layout:U},K=Object.assign(Q,{__name:"Edit",props:{settings:Object,errors:Object},setup(v){const p=v;let o=x({_method:"put",header_photo_path:p.settings.header_photo_path,footer_photo_path:p.settings.footer_photo_path}),u=h(null),c=h(null);const i=h(null),_=h(null);let m=l=>{const t=l.target.files[0];u.value=URL.createObjectURL(t)},b=l=>{const t=l.target.files[0];c.value=URL.createObjectURL(t)},f=()=>{console.log(i.value.files[0]),i.value&&(o.header_photo_path=i.value.files[0]),_.value&&(o.footer_photo_path=_.value.files[0]),o.post(route("settings.update",p.settings.id),{preserveState:!0,onError:()=>{},onSuccess:()=>{o.reset()}})};return(l,t)=>(s(),a(P,null,[y(e(C),{title:"Settings"}),r("div",S,[r("div",B,[r("form",{onSubmit:t[2]||(t[2]=w((...n)=>e(f)&&e(f)(...n),["prevent"]))},[r("div",F,[O,r("input",{id:"headerPhoto2",type:"file",onChange:t[0]||(t[0]=(...n)=>e(m)&&e(m)(...n)),ref_key:"headerPhoto",ref:i,class:"px-2 py-1 w-full mt-1 mb-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-600"},null,544),e(o).errors.header_photo_path?(s(),a("div",E,g(e(o).errors.header_photo_path),1)):d("",!0),e(u)?(s(),a("img",{key:1,src:e(u),class:"w-full h-1/2 mb-4 border border-gray-900"},null,8,H)):e(o).header_photo_path?(s(),a("img",{key:2,src:"/storage/"+e(o).header_photo_path,class:"w-full h-1/2 mb-4 border border-gray-900"},null,8,N)):d("",!0),R,r("input",{id:"footerPhoto2",type:"file",onChange:t[1]||(t[1]=(...n)=>e(b)&&e(b)(...n)),ref_key:"footerPhoto",ref:_,class:"px-4 py-2 w-full mt-1 mb-4 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-600"},null,544),e(o).errors.footer_photo_path?(s(),a("div",V,g(e(o).errors.footer_photo_path),1)):d("",!0),e(c)?(s(),a("img",{key:4,src:e(c),class:"w-full h-1/2 mb-4 border border-gray-900"},null,8,A)):e(o).footer_photo_path?(s(),a("img",{key:5,src:"/storage/"+e(o).footer_photo_path,class:"w-full h-1/2 mb-4 border border-gray-900"},null,8,D)):d("",!0)]),e(o).isDirty?(s(),a("div",T,"There are unsaved form changes.")):d("",!0),r("div",M,[y(j,{loading:e(o).processing,class:"mr-4 px-3 py-2 flex btn-indigo",onClick:e(f)},{default:k(()=>[L(g(l.trans("Update")+" "+l.trans("Settings")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{K as default};
