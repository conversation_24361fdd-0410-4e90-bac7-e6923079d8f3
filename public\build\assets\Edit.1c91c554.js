import{Q as y,o as m,c as p,b as d,u as t,a,B as u,d as g,t as n,y as h,C as k,E as x,z as T,F as w,H as v,L as B,U as _}from"./app.5bf25e6f.js";import{A as V}from"./AppLayout.14f8c8f6.js";import{_ as L}from"./TextInput.48e8e32c.js";import{L as C}from"./LoadingButton.c8fb65b2.js";import{T as N}from"./TrashedMessage.5487e7e2.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const j={class:"p-2 sm:p-4"},A={class:"mb-2 flex justify-start max-w-xl"},E={class:"font-bold text-2xl"},S=a("span",{class:"text-indigo-400 font-medium"}," /",-1),D={class:"bg-white rounded-md shadow-sm overflow-hidden max-w-xl"},F={class:"px-4 py-2 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},H={layout:V},$=Object.assign(H,{__name:"Edit",props:{locale:String,tracking_tag:Object},setup(i){const l=i;let r=y({_method:"put",name:l.tracking_tag.name}),c=()=>{r.post(route("tracking_tags.update",l.tracking_tag.id),{onSuccess:()=>r.reset("name")})},f=()=>{confirm("Are you sure you want to delete this tracking tag?")&&_.Inertia.delete(route("tracking_tags.destroy",l.tracking_tag.id))},b=()=>{confirm("Are you sure you want to restore this tracking tag?")&&_.Inertia.put(route("tracking_tags.restore",l.tracking_tag.id))};return(e,s)=>(m(),p(w,null,[d(t(v),{title:"Edit Tracking Tag"}),a("div",j,[a("div",A,[a("h2",E,[d(t(B),{class:"text-indigo-400 hover:text-indigo-600",href:e.route("tracking_tags")},{default:u(()=>[g(n(e.trans("TrackingTagsList")),1)]),_:1},8,["href"]),S,g(" "+n(t(r).name),1)])]),a("div",D,[i.tracking_tag.deleted_at?(m(),h(N,{key:0,class:"mb-6",onRestore:t(b)},{default:u(()=>[g(n(e.trans("This")+" "+e.trans("TrackingTag")+" "+e.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):k("",!0),a("form",{onSubmit:s[2]||(s[2]=x((...o)=>t(c)&&t(c)(...o),["prevent"]))},[a("div",{class:T(["p-8 -mr-6 -mb-8 flex flex-wrap",i.locale=="ar"?"rtl text-right":"ltr text-left"])},[d(L,{modelValue:t(r).name,"onUpdate:modelValue":s[0]||(s[0]=o=>t(r).name=o),error:t(r).errors.name,direction:"ltr",class:"pr-6 pb-8 w-full",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),a("div",F,[d(C,{loading:t(r).processing,class:"btn-green",type:"submit"},{default:u(()=>[g(n(e.trans("Update")+" "+e.trans("TrackingTag")),1)]),_:1},8,["loading"]),i.tracking_tag.deleted_at?k("",!0):(m(),p("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:s[1]||(s[1]=(...o)=>t(f)&&t(f)(...o))},n(e.trans("Delete")+" "+e.trans("TrackingTag")),1))])],32)])])],64))}});export{$ as default};
