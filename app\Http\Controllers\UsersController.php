<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Illuminate\Support\Facades\Hash;

class UsersController extends Controller
{
    public function index()
    {
        return Inertia::render('Users/Index', [
            'filters' => Request::all('search', 'role', 'trashed'),
            // 'canAddUsers' => Gate::allows('manage-users'),
            // 'can'           => Auth::user()->can('manage-users'),
            // 'can'           => Request::user()->can('manage-users'),
            'can' => [
                'createUser' => Auth::user()->can('create', User::class)
            ],
            'users' => User::orderBy('name')
                ->filter(Request::only('search', 'role', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($user) => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'photo' => $user->profile_photo_path,
                    'deleted_at' => $user->deleted_at,
                    'created_at' => $user->created_at->diffForHumans(),
                    'updated_at' => $user->updated_at->diffForHumans(),
                    'can' => [
                        'edit' => Auth::user()->can('edit', $user)
                    ],
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Users/Create');
    }

    public function store()
    {
        Request::validate([
            'name'     => ['required', 'min:8', 'max:50', Rule::unique('users')],
            'email'    => ['required', 'max:50', 'email', Rule::unique('users')],
            'password' => ['required'],
            'role'     => ['required'],
        ]);

        User::create([
            'name'     => Request::get('name'),
            'email'    => Request::get('email'),
            'password' => Hash::make(Request::get('password')),
            'role'     => Request::get('role'),
        ]);

        return Redirect::route('users')->with('success', 'User created.');
    }

    public function edit(User $user)
    {
        return Inertia::render('Users/Edit', [
            'userForEdit' => [
                'id'         => $user->id,
                'name'       => $user->name,
                'email'      => $user->email,
                'role'       => $user->role,
                'photo'      => $user->profile_photo_path,
                'deleted_at' => $user->deleted_at,
                'can' => [
                    'edit' => Auth::user()->can('edit', $user),
                    'delete' => Auth::user()->can('delete', $user),
                ],
            ],
        ]);
    }

    public function update(User $user)
    {
        if ($user->isAdmin) {
            return Redirect::back()->with('error', 'Updating the Admin user is not allowed.');
        }

        Request::validate([
            'name'     => ['required', 'max:50'],
            'email'    => ['required', 'max:50', 'email', Rule::unique('users')->ignore($user->id)],
            'password' => ['nullable'],
            'role'     => ['required'],
        ]);

        $user->update(Request::only('name', 'email', 'role'));

        if (Request::get('password')) {
            $user->update(['password' => Hash::make(Request::get('password'))]);
        }

        return Redirect::back()->with('success', 'User updated.');
    }

    public function destroy(User $user)
    {
        if ($user->isAdmin) {
            return Redirect::back()->with('error', 'Deleting the Admin user is not allowed.');
        } else {
            $user->delete();
            //$user->forceDelete();
            return Redirect::back()->with('success', 'User deleted.');
        }
    }

    public function restore(User $user)
    {
        $user->restore();
        return Redirect::back()->with('success', 'User restored.');
    }
}
