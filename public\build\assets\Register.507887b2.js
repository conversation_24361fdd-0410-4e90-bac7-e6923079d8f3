import{n as v,r,o as d,c,b as o,B as i,F as b,H as y,L as k,a,C as V,z as j,E as $,d as l}from"./app.5bf25e6f.js";import{J as C}from"./AuthenticationCard.d2e642d8.js";import{J}from"./AuthenticationCardLogo.c737ac3b.js";import{_ as L}from"./Button.de389ba7.js";import{_ as P}from"./Input.f95445aa.js";import{_ as B}from"./Checkbox.c93fed4a.js";import{_ as N}from"./Label.a34a8f2d.js";import{_ as U}from"./ValidationErrors.40b6029f.js";import{_ as q}from"./plugin-vue_export-helper.21dcd24c.js";const A=v({components:{Head:y,JetAuthenticationCard:C,JetAuthenticationCardLogo:J,JetButton:L,JetInput:P,JetCheckbox:B,JetLabel:N,JetValidationErrors:U,Link:k},data(){return{form:this.$inertia.form({name:"",email:"",password:"",password_confirmation:"",terms:!1})}},methods:{submit(){this.form.post(this.route("register"),{onFinish:()=>this.form.reset("password","password_confirmation")})}}}),E={class:"mt-4"},F={class:"mt-4"},H={class:"mt-4"},R={key:0,class:"mt-4"},T={class:"flex items-center"},x={class:"ml-2"},z=l(" I agree to the "),I=["href"],S=l(" and "),M=["href"],D={class:"flex items-center justify-end mt-4"},G=l(" Already registered? "),K=l(" Register ");function O(e,t,Q,W,X,Y){const u=r("Head"),f=r("jet-authentication-card-logo"),p=r("jet-validation-errors"),n=r("jet-label"),m=r("jet-input"),_=r("jet-checkbox"),h=r("Link"),w=r("jet-button"),g=r("jet-authentication-card");return d(),c(b,null,[o(u,{title:"Register"}),o(g,null,{logo:i(()=>[o(f)]),default:i(()=>[o(p,{class:"mb-4"}),a("form",{onSubmit:t[5]||(t[5]=$((...s)=>e.submit&&e.submit(...s),["prevent"]))},[a("div",null,[o(n,{for:"name",value:"Name"}),o(m,{id:"name",type:"text",class:"mt-1 block w-full",modelValue:e.form.name,"onUpdate:modelValue":t[0]||(t[0]=s=>e.form.name=s),required:"",autofocus:"",autocomplete:"name"},null,8,["modelValue"])]),a("div",E,[o(n,{for:"email",value:"Email"}),o(m,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e.form.email,"onUpdate:modelValue":t[1]||(t[1]=s=>e.form.email=s),required:""},null,8,["modelValue"])]),a("div",F,[o(n,{for:"password",value:"Password"}),o(m,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:e.form.password,"onUpdate:modelValue":t[2]||(t[2]=s=>e.form.password=s),required:"",autocomplete:"new-password"},null,8,["modelValue"])]),a("div",H,[o(n,{for:"password_confirmation",value:"Confirm Password"}),o(m,{id:"password_confirmation",type:"password",class:"mt-1 block w-full",modelValue:e.form.password_confirmation,"onUpdate:modelValue":t[3]||(t[3]=s=>e.form.password_confirmation=s),required:"",autocomplete:"new-password"},null,8,["modelValue"])]),e.$page.props.jetstream.hasTermsAndPrivacyPolicyFeature?(d(),c("div",R,[o(n,{for:"terms"},{default:i(()=>[a("div",T,[o(_,{name:"terms",id:"terms",checked:e.form.terms,"onUpdate:checked":t[4]||(t[4]=s=>e.form.terms=s)},null,8,["checked"]),a("div",x,[z,a("a",{target:"_blank",href:e.route("terms.show"),class:"underline text-sm text-gray-600 hover:text-gray-900"},"Terms of Service",8,I),S,a("a",{target:"_blank",href:e.route("policy.show"),class:"underline text-sm text-gray-600 hover:text-gray-900"},"Privacy Policy",8,M)])])]),_:1})])):V("",!0),a("div",D,[o(h,{href:e.route("login"),class:"underline text-sm text-gray-600 hover:text-gray-900"},{default:i(()=>[G]),_:1},8,["href"]),o(w,{class:j(["ml-4",{"opacity-25":e.form.processing}]),disabled:e.form.processing},{default:i(()=>[K]),_:1},8,["class","disabled"])])],32)]),_:1})],64)}var me=q(A,[["render",O]]);export{me as default};
