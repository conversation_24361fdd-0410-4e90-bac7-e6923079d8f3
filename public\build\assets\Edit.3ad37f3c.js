import{Q as V,o as p,c as y,b as d,u as e,a as s,B as u,d as m,t as n,y as h,C as g,E as _,z as v,F as D,H as k,L as B,U as w}from"./app.5bf25e6f.js";import{A as L}from"./AppLayout.14f8c8f6.js";import{_ as c}from"./TextInput.48e8e32c.js";import{_ as M}from"./SelectInput.16ffd220.js";import{L as U}from"./LoadingButton.c8fb65b2.js";import{T as C}from"./TrashedMessage.5487e7e2.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const N={class:"p-4"},S={class:"mb-2 flex justify-start max-w-xl"},j={class:"font-bold text-2xl"},A=s("span",{class:"text-indigo-400 font-medium"}," /",-1),E={class:"bg-white rounded-md shadow-sm overflow-hidden max-w-xl"},F=s("option",{value:null},null,-1),T=s("option",{value:"1"},"Male",-1),H=s("option",{value:"2"},"Female",-1),z={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},I={layout:L},P=Object.assign(I,{__name:"Edit",props:{locale:String,doctor:Object},setup(i){const a=i;let t=V({_method:"put",name:a.doctor.name,gender:a.doctor.gender,mobile:a.doctor.mobile,specialty:a.doctor.specialty}),f=()=>{t.post(route("doctors.update",a.doctor.id),{onSuccess:()=>t.reset("name")})},b=()=>{confirm("Are you sure you want to delete this doctor?")&&w.Inertia.delete(route("doctors.destroy",a.doctor.id))},x=()=>{confirm("Are you sure you want to restore this doctor?")&&w.Inertia.put(route("doctors.restore",a.doctor.id))};return(l,o)=>(p(),y(D,null,[d(e(k),{title:"Edit Doctor"}),s("div",N,[s("div",S,[s("h2",j,[d(e(B),{class:"text-indigo-400 hover:text-indigo-600",href:l.route("doctors")},{default:u(()=>[m(n(l.trans("DoctorsList")),1)]),_:1},8,["href"]),A,m(" "+n(e(t).name),1)])]),s("div",E,[i.doctor.deleted_at?(p(),h(C,{key:0,class:"mb-6",onRestore:e(x)},{default:u(()=>[m(n(l.trans("This")+" "+l.trans("Doctor")+" "+l.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):g("",!0),s("form",{onSubmit:o[5]||(o[5]=_((...r)=>e(f)&&e(f)(...r),["prevent"]))},[s("div",{class:v(["p-8 -mr-6 -mb-8 flex flex-wrap",i.locale=="ar"?"rtl text-right":"ltr text-left"])},[d(c,{modelValue:e(t).name,"onUpdate:modelValue":o[0]||(o[0]=r=>e(t).name=r),error:e(t).errors.name,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),d(M,{modelValue:e(t).gender,"onUpdate:modelValue":o[1]||(o[1]=r=>e(t).gender=r),modelModifiers:{number:!0},error:e(t).errors.gender,class:"pr-6 pb-8 w-full lg:w-1/2",label:"Gender"},{default:u(()=>[F,T,H]),_:1},8,["modelValue","error"]),d(c,{modelValue:e(t).mobile,"onUpdate:modelValue":o[2]||(o[2]=r=>e(t).mobile=r),modelModifiers:{number:!0},error:e(t).errors.mobile,class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Mobile"},null,8,["modelValue","error"]),d(c,{modelValue:e(t).specialty,"onUpdate:modelValue":o[3]||(o[3]=r=>e(t).specialty=r),error:e(t).errors.specialty,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Specialty"},null,8,["modelValue","error"])],2),s("div",z,[d(U,{loading:e(t).processing,class:"btn-green",type:"submit"},{default:u(()=>[m(n(l.trans("Update")+" "+l.trans("Doctor")),1)]),_:1},8,["loading"]),i.doctor.deleted_at?g("",!0):(p(),y("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:o[4]||(o[4]=(...r)=>e(b)&&e(b)(...r))},n(l.trans("Delete")+" "+l.trans("Doctor")),1))])],32)])])],64))}});export{P as default};
