import{Q as E,v as V,o as a,c as d,b as u,u as e,a as o,B as c,d as b,t as i,y as S,C as m,E as O,z as D,K as j,S as z,F as h,D as w,H as F,L as I,U as R}from"./app.5bf25e6f.js";import{A as M}from"./AppLayout.14f8c8f6.js";import{_ as y}from"./TextInput.48e8e32c.js";import{D as T}from"./DropdownSearch.7a091d54.js";import{_ as k}from"./SelectInput.16ffd220.js";import{L as C}from"./LoadingButton.c8fb65b2.js";import{T as $}from"./TrashedMessage.5487e7e2.js";import{Q as H}from"./vue-quill.esm-bundler.e18f44dc.js";/* empty css                       */import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const Q={class:"p-2 sm:p-4"},K={class:"mb-2 flex justify-start"},G={class:"font-bold text-2xl"},J=o("span",{class:"text-indigo-400 font-medium"}," / ",-1),W={class:"bg-white rounded-md shadow max-w-4xl"},X={value:"null",hidden:"",selected:"",disabled:""},Y=["value"],Z={class:"pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4"},ee={class:"form-label",for:"Sequence"},te={key:0,class:"form-error"},se={value:"null",hidden:"",selected:"",disabled:""},le=["value"],re={value:0},oe={value:1},ae={key:0,class:"ltr text-left pr-4 pb-0 w-full text-gray-700 font-semibold"},ne=o("span",{class:"p-1"},"Test Predefined Results:",-1),ie=[ne],de={key:1,class:"ltr text-left w-full flex flex-wrap bg-indigo-300 py-1 px-1 mr-6 rounded h-auto mb-2"},ue=["onClick"],me={key:0,class:"px-2"},ce={key:1,class:"form-error w-full"},pe=o("div",{class:"ltr text-left pr-4 pb-2 w-full text-green-600 font-semibold"},[o("span",{class:"p-1"},"Test Devices:")],-1),_e=["onClick"],fe={class:"w-full h-auto"},be={key:0,class:"ml-6 pb-1 text-red-500"},he={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-between items-center"},ve={layout:M},Le=Object.assign(ve,{__name:"Edit",props:{locale:String,test:Object,categories:Array,devices:Array,results:Array},setup(v){const n=v;var L=[["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}]];let t=E({createAnother:!1,short_name:n.test.short_name,full_name:n.test.full_name,category_id:n.test.category_id,default_device_id:n.test.default_device_id,result_type:n.test.result_type,sequence:n.test.sequence,lab_to_patient_price:n.test.lab_to_patient_price,lab_to_lab_price:n.test.lab_to_lab_price,user_id:n.test.user_id,testDevices:n.test.testDevices,testResults:n.test.testResults});const p=V(n.devices),_=V(n.results);let U=l=>{t.testDevices.push({id:l.id,name:l.name,normal_range:""}),p.splice(p.findIndex(r=>r.id===l.id),1)},q=(l,r,s)=>{t.default_device_id===l&&(t.default_device_id=null),p.push({id:l,name:r,normal_range:""}),t.testDevices.splice(s,1)},B=l=>{t.testResults.push({id:l.id,name:l.name}),_.splice(_.findIndex(r=>r.id===l.id),1)},N=(l,r,s)=>{_.push({id:l,name:r}),t.testResults.splice(s,1)},g=()=>{t.put(route("tests.update",n.test.id),{preserveState:!0,onSuccess:()=>{t.reset(),p.splice(0,p.length),_.splice(0,_.length)}})},A=()=>{confirm("Are you sure you want to permanently delete this test?")&&R.Inertia.delete(route("tests.forceDelete",n.test.id))},P=()=>{confirm("Are you sure you want to restore this test?")&&R.Inertia.put(route("tests.restore",n.test.id))};return(l,r)=>(a(),d(h,null,[u(e(F),{title:"Edit Test"}),o("div",Q,[o("div",K,[o("h2",G,[u(e(I),{class:"text-indigo-400 hover:text-indigo-600",href:l.route("tests")},{default:c(()=>[b(i(l.trans("TestsList")),1)]),_:1},8,["href"]),J,b(" "+i(e(t).short_name),1)])]),o("div",W,[v.test.deleted_at?(a(),S($,{key:0,class:"mb-6 min-w-full",onRestore:e(P)},{default:c(()=>[b(i(l.trans("This")+" "+l.trans("Test")+" "+l.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):m("",!0),o("form",{onSubmit:r[8]||(r[8]=O((...s)=>e(g)&&e(g)(...s),["prevent"]))},[o("div",{class:D(["p-6 -mr-6 -mb-5 flex flex-wrap",v.locale=="ar"?"rtl text-right":"ltr text-left"])},[u(y,{modelValue:e(t).short_name,"onUpdate:modelValue":r[0]||(r[0]=s=>e(t).short_name=s),error:e(t).errors.short_name,direction:"ltr",class:"pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"text",label:"ShortName",autofocus:!0},null,8,["modelValue","error"]),u(y,{modelValue:e(t).full_name,"onUpdate:modelValue":r[1]||(r[1]=s=>e(t).full_name=s),error:e(t).errors.full_name,direction:"ltr",class:"pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"text",label:"FullName"},null,8,["modelValue","error"]),u(k,{modelValue:e(t).category_id,"onUpdate:modelValue":r[2]||(r[2]=s=>e(t).category_id=s),error:e(t).errors.category_id,direction:"ltr",class:"pr-6 pb-6 w-full text-right sm:w-1/2 md:w-1/3 lg:w-1/4",label:"Category"},{default:c(()=>[o("option",X,i(l.trans("SelectCategory")),1),(a(!0),d(h,null,w(v.categories,s=>(a(),d("option",{key:s.id,value:s.id},i(s.name),9,Y))),128))]),_:1},8,["modelValue","error"]),o("div",Z,[o("label",ee,i(l.trans("Sequence"))+":",1),j(o("input",{id:"Sequence","onUpdate:modelValue":r[3]||(r[3]=s=>e(t).sequence=s),placeholder:"Sequence",class:D(["form-input ltr",{error:e(t).errors.sequence}]),type:"number"},null,2),[[z,e(t).sequence]]),e(t).errors.sequence?(a(),d("div",te,i(e(t).errors.sequence),1)):m("",!0)]),u(y,{modelValue:e(t).lab_to_patient_price,"onUpdate:modelValue":r[4]||(r[4]=s=>e(t).lab_to_patient_price=s),error:e(t).errors.lab_to_patient_price,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"number",label:"LabToPatientPrice"},null,8,["modelValue","error"]),u(y,{modelValue:e(t).lab_to_lab_price,"onUpdate:modelValue":r[5]||(r[5]=s=>e(t).lab_to_lab_price=s),error:e(t).errors.lab_to_lab_price,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"number",label:"LabToLabPrice"},null,8,["modelValue","error"]),u(k,{modelValue:e(t).default_device_id,"onUpdate:modelValue":r[6]||(r[6]=s=>e(t).default_device_id=s),error:e(t).errors.default_device_id,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",label:"DefaultDevice"},{default:c(()=>[o("option",se,i(l.trans("SelectDefaultDevice")),1),(a(!0),d(h,null,w(e(t).testDevices,s=>(a(),d("option",{key:s.id,value:s.id},i(s.name),9,le))),128))]),_:1},8,["modelValue","error"]),u(k,{modelValue:e(t).result_type,"onUpdate:modelValue":r[7]||(r[7]=s=>e(t).result_type=s),modelModifiers:{number:!0},error:e(t).errors.result_type,class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",label:"ResultsType"},{default:c(()=>[o("option",re,i(l.trans("NumericResults")),1),o("option",oe,i(l.trans("PredefinedResults")),1)]),_:1},8,["modelValue","error"]),e(t).result_type?(a(),d("div",ae,ie)):m("",!0),e(t).result_type?(a(),d("div",de,[(a(!0),d(h,null,w(e(t).testResults,(s,f)=>(a(),d("div",{title:"Remove Result",class:"pr-2 pt-1 pb-1 px-0",key:f},[o("span",{title:"Remove Result",onClick:x=>e(N)(s.id,s.name,f),class:"btn-remove pt-0.5"},i(s.name),9,ue)]))),128)),e(t).testResults.length<=0?(a(),d("span",me,"There are no attached results yet..!")):m("",!0),e(t).errors.testResults?(a(),d("div",ce,i(e(t).errors.testResults),1)):m("",!0)])):m("",!0),u(T,{class:"text-left z-40 pr-6 pb-2 w-full sm:w-1/2 lg:w-1/3",options:p,onSelect:e(U),CloseOnSelect:!1,fixedLabel:!0,label:"SelectDevice",searchBy:"name",direction:"ltr",placeholder:"Search for devices"},null,8,["options","onSelect"]),e(t).result_type?(a(),S(T,{key:2,class:"text-left pr-6 pb-2 w-full sm:w-1/2 lg:w-1/3",options:_,onSelect:e(B),CloseOnSelect:!1,fixedLabel:!0,label:"SelectResult",searchBy:"name",direction:"ltr",placeholder:"Search for Predefined Results"},null,8,["options","onSelect"])):m("",!0),pe,(a(!0),d(h,null,w(e(t).testDevices,(s,f)=>(a(),d("div",{class:"ltr text-left pr-6 pb-2 w-1/2",key:f},[o("span",{title:"Remove device",onClick:x=>e(q)(s.id,s.name,f),class:"btn-remove pt-0.5",for:"Normal"},i(s.name)+" Normal Range: ",9,_e),o("div",fe,[u(e(H),{class:"min-h-full",content:s.normal_range,"onUpdate:content":x=>s.normal_range=x,contentType:"html",toolbar:e(L),theme:"snow"},null,8,["content","onUpdate:content","toolbar"])])]))),128))],2),e(t).isDirty?(a(),d("div",be,"There are unsaved form changes, click update button to save them.")):m("",!0),o("div",he,[u(C,{loading:e(t).processing,class:"mr-4 px-3 py-2 flex btn-green",onClick:e(g)},{default:c(()=>[b(i(l.trans("Update")+" "+l.trans("Test")),1)]),_:1},8,["loading","onClick"]),u(C,{class:"mr-4 text-red-600 px-3 py-2 flex",onClick:e(A)},{default:c(()=>[b(i(l.trans("forceDelete")+" "+l.trans("Test")),1)]),_:1},8,["onClick"])])],32)])])],64))}});export{Le as default};
