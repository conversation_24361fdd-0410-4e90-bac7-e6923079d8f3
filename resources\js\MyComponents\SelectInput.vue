<template>
  <div>
    <label v-if="label" class="form-label" :for="id">{{trans(label) }}:</label>
    <select :id="id" ref="input" v-model="modelValue" @input="$emit('update:modelValue', $event.target.value)" class="form-select" 
    :class="{rtl: direction === 'rtl', ltr: direction === 'ltr', error: error }">
      <slot />
    </select>
    <div v-if="error" class="form-error">{{ error }}</div>
  </div>
</template>
<script setup>
  const props = defineProps({
      id: String,
      modelValue: [String, Number, Boolean],
      label: String,
      direction: String,
      error: String,
  });
  const emits =  defineEmits(['update:modelValue']);
</script>

