import{v as x,q as w,o as c,c as p,b as o,u as r,a as e,B as d,t as a,F as _,D as g,C as u,H as y,K as v,R as k,L as n,d as V,y as N,U as S}from"./app.5bf25e6f.js";import{A as j,I as m}from"./AppLayout.14f8c8f6.js";import{t as B,S as C,p as L}from"./SearchFilter.f110f3d1.js";import{P as O}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const I={class:"p-4 max-w-7xl"},T={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},U=e("label",{class:"block text-gray-700"},"Trashed:",-1),A=e("option",{value:null},null,-1),D={value:"with"},F={value:"only"},E={class:"inline sm:hidden"},H={class:"hidden sm:flex w-48 text-center"},M={class:"shadow overflow-x-auto rounded-lg bg-white"},P={class:"min-w-full divide-y divide-gray-200"},R={class:"bg-gray-50"},q={class:"bg-green-500"},K={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},G={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},X={class:"border-t p-0"},Y={class:"px-6 py-2 flex items-center"},Z={class:"ml-4"},$={class:"flex items-center text-sm font-medium text-gray-900"},ee={class:"border-t p-0"},te={class:"px-6 py-2"},se={class:"border-t p-0"},ae={class:"px-6 py-2"},oe={class:"border-t p-0"},re={class:"px-6 py-2"},de={class:"border-t p-0"},ie={class:"px-6 py-2"},le={class:"border-t p-0"},ne={class:"px-6 py-2"},ce={key:0},he=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No labs found.",-1),pe=[he],fe=e("hr",{class:"bg-gray-300 pt-px"},null,-1),_e={layout:j},ye=Object.assign(_e,{__name:"Index",props:{filters:Object,labs:Object},setup(i){const f=i,l=x({search:f.filters.search,trashed:f.filters.trashed});w(l,B(function(){S.Inertia.get(route("labs"),L(l),{preserveState:!0,replace:!0})},300),{deep:!0});function b(){Object.assign(l,{search:null,trashed:""})}return(t,h)=>(c(),p(_,null,[o(r(y),{title:"SideLabs"}),e("div",I,[e("div",T,[o(C,{modelValue:l.search,"onUpdate:modelValue":h[1]||(h[1]=s=>l.search=s),direction:"rtl",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0645\u062E\u062A\u0628\u0631\u0627\u062A \u0627\u0644\u062C\u0627\u0646\u0628\u064A\u0629",class:"mb-2 sm:mb-0 w-full",onReset:b},{default:d(()=>[U,v(e("select",{"onUpdate:modelValue":h[0]||(h[0]=s=>l.trashed=s),class:"mt-1 w-full form-select"},[A,e("option",D,a(t.trans("WithTrashed")),1),e("option",F,a(t.trans("OnlyTrashed")),1)],512),[[k,l.trashed]])]),_:1},8,["modelValue"]),o(r(n),{class:"w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium",href:t.route("labs.create")},{default:d(()=>[e("span",E,a(t.trans("Create")),1),e("span",H,a(t.trans("CreateANewSideLab")),1)]),_:1},8,["href"])]),e("div",M,[e("table",P,[e("thead",R,[e("tr",q,[e("th",K,a(t.trans("Name")),1),e("th",W,a(t.trans("Owner")),1),e("th",z,a(t.trans("Mobile")),1),e("th",G,a(t.trans("Created_at")),1),e("th",J,a(t.trans("Updated_at")),1),e("th",Q,a(t.trans("Edit")),1)])]),(c(!0),p(_,null,g(i.labs.data,s=>(c(),p("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",X,[o(r(n),{href:t.route("labs.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",Y,[e("div",Z,[e("div",$,[V(a(s.name)+" ",1),s.deleted_at?(c(),N(m,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):u("",!0)])])])]),_:2},1032,["href"])]),e("td",ee,[o(r(n),{href:t.route("labs.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",te,a(s.owner),1)]),_:2},1032,["href"])]),e("td",se,[o(r(n),{href:t.route("labs.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",ae,a(s.mobile),1)]),_:2},1032,["href"])]),e("td",oe,[o(r(n),{href:t.route("labs.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",re,a(s.created_at),1)]),_:2},1032,["href"])]),e("td",de,[o(r(n),{href:t.route("labs.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",ie,a(s.updated_at),1)]),_:2},1032,["href"])]),e("td",le,[o(r(n),{class:"flex items-center text-right text-sm font-medium",href:t.route("labs.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",ne,[o(m,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),i.labs.data.length===0?(c(),p("tr",ce,pe)):u("",!0)]),fe,o(O,{class:"px-6 py-2 bg-white border-none border-t p-0",links:i.labs.links,from:i.labs.from,to:i.labs.to,total:i.labs.total},null,8,["links","from","to","total"])])])],64))}});export{ye as default};
