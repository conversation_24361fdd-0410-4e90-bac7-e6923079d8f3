import{n as c,L as m,r as _,o as t,c as n,a as e,F as i,D as h,y as p,z as u,t as a,C as g,d as r}from"./app.5bf25e6f.js";import{_ as f}from"./plugin-vue_export-helper.21dcd24c.js";const b=c({components:{Link:m},props:{links:Array,from:Number,to:Number,total:Number}}),y={key:0},v={class:"flex -mb-1 items-center justify-start"},x=["innerHTML"],k={class:"ml-2"},L={class:"text-sm text-indigo-700 font-medium"},w=r(" Showing "),N={class:"font-medium"},C=r(" to "),T={class:"font-medium"},B=r(" of "),H={class:"font-medium"},M=r(" results ");function $(o,V,z,D,F,P){const d=_("Link");return o.links.length>3?(t(),n("div",y,[e("div",v,[(t(!0),n(i,null,h(o.links,(s,l)=>(t(),n(i,{key:l},[s.url===null?(t(),n("div",{key:0,class:"bg-white mr-1 mb-1 px-4 py-3 text-sm leading-4 text-gray-400 border rounded border-indigo-500 shadow-md hover:cursor-not-allowed",innerHTML:s.label},null,8,x)):(t(),p(d,{key:1,class:u(["bg-white mr-1 mb-1 px-4 py-3 text-sm leading-4 border rounded hover:bg-indigo-500 border-indigo-600 text-indigo-600 hover:border-gray-50 hover:text-gray-50 shadow-md",{"border-orange-600 text-orange-600":s.active}]),href:s.url,innerHTML:s.label},null,8,["class","href","innerHTML"]))],64))),128)),e("div",k,[e("p",L,[w,e("span",N,a(o.from),1),C,e("span",T,a(o.to),1),B,e("span",H,a(o.total),1),M])])])])):g("",!0)}var A=f(b,[["render",$]]);export{A as P};
