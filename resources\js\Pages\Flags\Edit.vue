<template>
<Head title="Edit Flag" />
<div class="p-2 sm:p-4">
    <div class="mb-2 flex justify-start max-w-xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('flags')">{{ trans('FlagsList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
        {{ form.name }}
      </h2>
    </div>
    
    
    <div class="bg-white rounded-md shadow-sm overflow-hidden max-w-xl">
      <trashed-message v-if="flag.deleted_at" class="mb-6" @restore="restore">
      {{ trans('This') + ' ' +  trans('Flag') + ' ' + trans('HasBeenDeleted')}}
    </trashed-message>
      <form @submit.prevent="update">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input   v-model="form.name" :error="form.errors.name" type="text" class="pr-6 pb-8 w-full" direction="ltr" label="Name"  :autofocus="true"/>
        </div>
        <div class="px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
          <loading-button :loading="form.processing" class="btn-green" type="submit">{{trans('Update') + ' ' + trans('Flag')}}</loading-button>
          <button v-if="!flag.deleted_at" class="text-red-600 hover:underline" tabindex="-1" type="button" @click="destroy">{{trans('Delete') + ' ' + trans('Flag')}}</button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
  locale: String,
  flag: Object,
});

let form = useForm({
    _method: 'put',
    name: props.flag.name,
});

let update = () => {
    form.post(route('flags.update', props.flag.id), {
    onSuccess: () => form.reset(),
    })
};

let destroy = () => {
    if (confirm('Are you sure you want to delete this flag?')) {
    Inertia.delete(route('flags.destroy', props.flag.id))
    }
};

let restore = () => {
    if (confirm('Are you sure you want to restore this flag?')) {
    Inertia.put(route('flags.restore', props.flag.id))
    }
};

</script>