import{m as r,G as l,u as o,o as e,c as s,a,F as d,D as m,t as u,C as _}from"./app.5bf25e6f.js";const p={key:0},h=a("div",{class:"font-medium text-red-600"}," Whoops! Something went wrong. ",-1),g={class:"mt-3 list-disc list-inside text-sm text-red-600"},y={__name:"ValidationErrors",setup(k){const t=r(()=>l().props.value.errors),n=r(()=>Object.keys(t.value).length>0);return(x,f)=>o(n)?(e(),s("div",p,[h,a("ul",g,[(e(!0),s(d,null,m(o(t),(c,i)=>(e(),s("li",{key:i},u(c),1))),128))])])):_("",!0)}};export{y as _};
