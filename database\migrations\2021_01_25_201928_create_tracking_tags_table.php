<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTrackingTagsTable extends Migration
{
    public function up()
    {
        Schema::create('tracking_tags', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();

            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null')->onUpdate('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('tracking_tags');
    }
}
