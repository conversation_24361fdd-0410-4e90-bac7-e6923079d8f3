<?php

namespace App\Http\Controllers;

use App\Models\Visit;
use App\Models\Patient;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Illuminate\Support\Facades\Gate;
use App\Models\TrackingTag;

class PatientController extends Controller
{
    public function index()
    {
        return Inertia::render('Patients/Index', [
            'filters' => Request::all('search', 'trashed'),
            'patients' =>  Patient::orderBy('updated_at', 'desc')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($patient) => [
                    'id'         => $patient->id,
                    'name'       => $patient->name,
                    'gender'     => $patient->gender,
                    'age'        => $patient->age,
                    'mobile'     => $patient->mobile,
                    'created_at' => $patient->created_at->diffForHumans(),
                    'updated_at' => $patient->updated_at->diffForHumans(),
                    'deleted_at' => $patient->deleted_at,
                ]),
        ]);
    }

    public function edit(Patient $patient)
    {
        $patient_tracking_tags = $patient->tracking_tags()->orderBy('id')->get()->map->only('id');
        $today = (date('Y-m-d') . ' 00:00:00');
        $todays_visit = Visit::withTrashed()->where('patient_id', '=', $patient->id)->where('created_at', '>=', $today)->exists();
        return Inertia::render('Patients/Edit', [
            'tracking_tags' => TrackingTag::whereNotIn('id', $patient_tracking_tags)->orderBy('created_at', 'desc')->get()->map->only('id', 'name'),
            'patientTrackingTags' => $patient->tracking_tags()->with('user')->get(),

            'todays_visit' => $todays_visit,
            'patient' => [
                'id' => $patient->id,
                'name' => $patient->name,
                'gender' => $patient->gender,
                'age' => $patient->age,
                'mobile' => $patient->mobile,
                'marital'           => $patient->marital ?? null,
                'smoking'           => $patient->smoking,
                'deleted_at' => $patient->deleted_at,
                // 'visits' => $patient->visits()->withTrashed()->with('tests')->orderBy('created_at')->get(),

                'visits'  => $patient->visits() ? $patient->visits()->withTrashed()->orderBy('created_at', 'desc')->get()->map(fn ($visit) => [
                    "id"                => $visit->id,
                    "tests_count"       => $visit->tests_count,
                    "offers_count"      => $visit->offers_count,
                    "tests_cost"        => $visit->tests_cost,
                    "discount"          => $visit->discount,
                    "final_cost"        => $visit->final_cost,
                    "paid_amount"       => $visit->paid_amount,
                    "remaining_amount"  => $visit->remaining_amount,
                    'created_at'        => $visit->created_at->diffForHumans(),
                    'updated_at'        => $visit->updated_at->diffForHumans(),
                    'deleted_at'        => $visit->deleted_at,
                ])
                    : null,
            ],
        ]);
    }

    public function update(Patient $patient)
    {
        $patient->update(
            Request::validate([
                'name'    => ['required', 'min:12', 'max:45',  Rule::unique('patients')->ignore($patient->id)],
                'gender'  => ['required'],
                'age'     => ['nullable', 'digits_between:0,3'],
                'mobile'  => ['nullable', 'min:11', 'max:11'],
                'marital' => ['nullable', 'digits_between:0,3'],
                'smoking' => ['nullable', 'digits_between:0,3'],
            ])
        );

        return Redirect::back()->with('success', 'Patient updated.');
    }

    public function destroy(Patient $patient)
    {
        $patient->delete();
        return Redirect::back()->with('success', 'Patient deleted.');
    }

    public function restore(Patient $patient)
    {
        $patient->restore();
        return Redirect::back()->with('success', 'Patient restored.');
    }

    public function forceDelete(Patient $patient)
    {
        Gate::authorize('manage-patients');
        $patient->forceDelete();
        return Redirect::route('patients')->with('success', 'The patient has been permanently deleted.');
    }
}
