import{n as y,r as a,o as r,y as v,B as t,c as i,F as k,D as b,a as s,t as c,d as n,C as B,b as l,X as C,z as j}from"./app.5bf25e6f.js";import{_ as S}from"./ActionMessage.eaf60617.js";import{a as $,b as L}from"./DialogModal.3b39e601.js";import{_ as M}from"./Button.de389ba7.js";import{_ as x}from"./Input.f95445aa.js";import{_ as O}from"./InputError.2a9befad.js";import{_ as V}from"./SecondaryButton.fcd49457.js";import{_ as J}from"./plugin-vue_export-helper.21dcd24c.js";const z=y({props:["sessions"],components:{JetActionMessage:S,JetActionSection:$,JetButton:M,JetDialogModal:L,JetInput:x,JetInputError:O,JetSecondaryButton:V},data(){return{confirmingLogout:!1,form:this.$inertia.form({password:""})}},methods:{confirmLogout(){this.confirmingLogout=!0,setTimeout(()=>this.$refs.password.focus(),250)},logoutOtherBrowserSessions(){this.form.delete(route("other-browser-sessions.destroy"),{preserveScroll:!0,onSuccess:()=>this.closeModal(),onError:()=>this.$refs.password.focus(),onFinish:()=>this.form.reset()})},closeModal(){this.confirmingLogout=!1,this.form.reset()}}}),D=n(" Browser Sessions "),F=n(" Manage and log out your active sessions on other browsers and devices. "),I=s("div",{class:"max-w-xl text-sm text-gray-600"}," If necessary, you may log out of all of your other browser sessions across all of your devices. Some of your recent sessions are listed below; however, this list may not be exhaustive. If you feel your account has been compromised, you should also update your password. ",-1),N={key:0,class:"mt-5 space-y-6"},E={key:0,fill:"none","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",viewBox:"0 0 24 24",stroke:"currentColor",class:"w-8 h-8 text-gray-500"},K=s("path",{d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"},null,-1),T=[K],U={key:1,xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",fill:"none","stroke-linecap":"round","stroke-linejoin":"round",class:"w-8 h-8 text-gray-500"},A=s("path",{d:"M0 0h24v24H0z",stroke:"none"},null,-1),H=s("rect",{x:"7",y:"4",width:"10",height:"16",rx:"1"},null,-1),P=s("path",{d:"M11 5h2M12 17v.01"},null,-1),X=[A,H,P],q={class:"ml-3"},G={class:"text-sm text-gray-600"},Q={class:"text-xs text-gray-500"},R={key:0,class:"text-green-500 font-semibold"},W={key:1},Y={class:"flex items-center mt-5"},Z=n(" Log Out Other Browser Sessions "),oo=n(" Done. "),so=n(" Log Out Other Browser Sessions "),eo=n(" Please enter your password to confirm you would like to log out of your other browser sessions across all of your devices. "),to={class:"mt-4"},no=n(" Cancel "),ro=n(" Log Out Other Browser Sessions ");function ao(o,d,io,lo,co,_o){const _=a("jet-button"),m=a("jet-action-message"),u=a("jet-input"),h=a("jet-input-error"),p=a("jet-secondary-button"),f=a("jet-dialog-modal"),g=a("jet-action-section");return r(),v(g,null,{title:t(()=>[D]),description:t(()=>[F]),content:t(()=>[I,o.sessions.length>0?(r(),i("div",N,[(r(!0),i(k,null,b(o.sessions,(e,w)=>(r(),i("div",{class:"flex items-center",key:w},[s("div",null,[e.agent.is_desktop?(r(),i("svg",E,T)):(r(),i("svg",U,X))]),s("div",q,[s("div",G,c(e.agent.platform?e.agent.platform:"Unknown")+" - "+c(e.agent.browser?e.agent.browser:"Unknown"),1),s("div",null,[s("div",Q,[n(c(e.ip_address)+", ",1),e.is_current_device?(r(),i("span",R,"This device")):(r(),i("span",W,"Last active "+c(e.last_active),1))])])])]))),128))])):B("",!0),s("div",Y,[l(_,{onClick:o.confirmLogout},{default:t(()=>[Z]),_:1},8,["onClick"]),l(m,{on:o.form.recentlySuccessful,class:"ml-3"},{default:t(()=>[oo]),_:1},8,["on"])]),l(f,{show:o.confirmingLogout,onClose:o.closeModal},{title:t(()=>[so]),content:t(()=>[eo,s("div",to,[l(u,{type:"password",class:"mt-1 block w-3/4",placeholder:"Password",ref:"password",modelValue:o.form.password,"onUpdate:modelValue":d[0]||(d[0]=e=>o.form.password=e),onKeyup:C(o.logoutOtherBrowserSessions,["enter"])},null,8,["modelValue","onKeyup"]),l(h,{message:o.form.errors.password,class:"mt-2"},null,8,["message"])])]),footer:t(()=>[l(p,{onClick:o.closeModal},{default:t(()=>[no]),_:1},8,["onClick"]),l(_,{class:j(["ml-3",{"opacity-25":o.form.processing}]),onClick:o.logoutOtherBrowserSessions,disabled:o.form.processing},{default:t(()=>[ro]),_:1},8,["onClick","class","disabled"])]),_:1},8,["show","onClose"])]),_:1})}var vo=J(z,[["render",ao]]);export{vo as default};
