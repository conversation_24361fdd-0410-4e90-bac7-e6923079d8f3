import{Q as g,o as w,c as x,b as a,u as t,a as s,B as d,d as n,t as i,E as h,z as y,F as C,H as V,L as _}from"./app.5bf25e6f.js";import{A as L}from"./AppLayout.14f8c8f6.js";import{_ as m}from"./TextInput.48e8e32c.js";import{L as p}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";const v={class:"p-4"},S={class:"mb-2 font-bold text-2xl"},k=s("span",{class:"text-indigo-400 font-medium"}," /",-1),A={class:"bg-white rounded-md shadow overflow-hidden max-w-xl"},B={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},N={layout:L},H=Object.assign(N,{__name:"Create",props:{locale:String},setup(b){let e=g({createAnother:!1,name:null,owner:null,mobile:null}),f=()=>{e.createAnother=!0,u()},c=()=>{e.createAnother=!1,u()},u=()=>{e.post(route("labs.store"),{preserveState:!0,onSuccess:()=>{e.reset()}})};return(r,l)=>(w(),x(C,null,[a(t(V),{title:"Create SideLab"}),s("div",v,[s("h2",S,[a(t(_),{class:"text-indigo-400 hover:text-indigo-600",href:r.route("labs")},{default:d(()=>[n(i(r.trans("SideLabsList")),1)]),_:1},8,["href"]),k,n(" "+i(r.trans("Create")),1)]),s("div",A,[s("form",{onSubmit:l[3]||(l[3]=h((...o)=>r.labs&&r.labs(...o),["prevent"]))},[s("div",{class:y(["p-8 -mr-6 -mb-8 flex flex-wrap",b.locale=="ar"?"rtl text-right":"ltr text-left"])},[a(m,{modelValue:t(e).name,"onUpdate:modelValue":l[0]||(l[0]=o=>t(e).name=o),error:t(e).errors.name,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),a(m,{modelValue:t(e).owner,"onUpdate:modelValue":l[1]||(l[1]=o=>t(e).owner=o),error:t(e).errors.owner,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Owner"},null,8,["modelValue","error"]),a(m,{modelValue:t(e).mobile,"onUpdate:modelValue":l[2]||(l[2]=o=>t(e).mobile=o),error:t(e).errors.mobile,direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Mobile"},null,8,["modelValue","error"])],2),s("div",B,[a(p,{loading:t(e).processing,class:"mr-4 px-3 py-2 btn-green",onClick:t(f)},{default:d(()=>[n(i(r.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),a(p,{loading:t(e).processing,class:"px-3 py-2 btn-green",onClick:t(c)},{default:d(()=>[n(i(r.trans("Create")+" "+r.trans("SideLab")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{H as default};
