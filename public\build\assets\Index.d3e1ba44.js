import{v as x,q as w,o as c,c as p,b as o,u as l,a as e,B as d,t as a,F as u,D as b,C as m,H as v,K as y,R as k,L as n,d as C,y as V,U as N}from"./app.5bf25e6f.js";import{A as B,I as _}from"./AppLayout.14f8c8f6.js";import{t as S,S as j,p as D}from"./SearchFilter.f110f3d1.js";import{P as O}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const I={class:"p-4 max-w-7xl"},L={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},T={class:"block text-gray-700"},U=e("option",{value:null},null,-1),A={value:"with"},F={value:"only"},E={class:"inline sm:hidden"},H={class:"hidden sm:flex w-full text-center"},P={class:"shadow overflow-x-auto rounded-lg bg-white"},R={class:"min-w-full divide-y divide-gray-200"},q={class:"bg-gray-50"},K={class:"bg-green-500"},M={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},G={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={class:"border-t p-0"},X={class:"px-6 py-2 flex items-center"},Y={class:"ml-4"},Z={class:"flex items-center text-sm font-medium text-gray-900"},$={class:"border-t p-0"},ee={class:"px-6 py-2"},te={class:"border-t p-0"},se={class:"px-6 py-2"},ae={class:"border-t p-0"},oe={class:"px-6 py-2"},ie={class:"border-t p-0"},re={class:"px-6 py-2"},le={key:0},de=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No categories found.",-1),ne=[de],ce=e("hr",{class:"bg-gray-300 pt-px"},null,-1),he={layout:B},xe=Object.assign(he,{__name:"Index",props:{filters:Object,categories:Object},setup(i){const f=i,r=x({search:f.filters.search,trashed:f.filters.trashed});w(r,S(function(){N.Inertia.get(route("categories"),D(r),{preserveState:!0,replace:!0})},300),{deep:!0});function g(){Object.assign(r,{search:null,trashed:""})}return(t,h)=>(c(),p(u,null,[o(l(v),{title:"Categories"}),e("div",I,[e("div",L,[o(j,{modelValue:r.search,"onUpdate:modelValue":h[1]||(h[1]=s=>r.search=s),direction:"ltr",placeholder:"Search Categories...",class:"mb-2 sm:mb-0 w-full",onReset:g},{default:d(()=>[e("label",T,a(t.trans("Trashed"))+":",1),y(e("select",{"onUpdate:modelValue":h[0]||(h[0]=s=>r.trashed=s),class:"mt-1 w-full form-select"},[U,e("option",A,a(t.trans("WithTrashed")),1),e("option",F,a(t.trans("OnlyTrashed")),1)],512),[[k,r.trashed]])]),_:1},8,["modelValue"]),o(l(n),{class:"w-full sm:w-64 bg-green-600 hover:bg-green-700 text-gray-50 py-2 sm:px-1 md:px-4 rounded-md font-medium",href:t.route("categories.create")},{default:d(()=>[e("span",E,a(t.trans("Create")),1),e("span",H,a(t.trans("CreateANewCategory")),1)]),_:1},8,["href"])]),e("div",P,[e("table",R,[e("thead",q,[e("tr",K,[e("th",M,a(t.trans("Name")),1),e("th",W,a(t.trans("DefaultDevice")),1),e("th",z,a(t.trans("Created_at")),1),e("th",G,a(t.trans("Updated_at")),1),e("th",J,a(t.trans("Edit")),1)])]),(c(!0),p(u,null,b(i.categories.data,s=>(c(),p("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",Q,[o(l(n),{href:t.route("categories.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",X,[e("div",Y,[e("div",Z,[C(a(s.name)+" ",1),s.deleted_at?(c(),V(_,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):m("",!0)])])])]),_:2},1032,["href"])]),e("td",$,[o(l(n),{href:t.route("categories.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",ee,a(s.default_device),1)]),_:2},1032,["href"])]),e("td",te,[o(l(n),{href:t.route("categories.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",se,a(s.created_at),1)]),_:2},1032,["href"])]),e("td",ae,[o(l(n),{href:t.route("categories.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",oe,a(s.updated_at),1)]),_:2},1032,["href"])]),e("td",ie,[o(l(n),{class:"flex items-center text-right text-sm font-medium",href:t.route("categories.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",re,[o(_,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),i.categories.data.length===0?(c(),p("tr",le,ne)):m("",!0)]),ce,o(O,{class:"px-6 py-2 bg-white border-none border-t p-0",links:i.categories.links,from:i.categories.from,to:i.categories.to,total:i.categories.total},null,8,["links","from","to","total"])])])],64))}});export{xe as default};
