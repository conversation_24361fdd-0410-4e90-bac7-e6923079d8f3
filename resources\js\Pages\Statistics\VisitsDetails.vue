<template>
  <div class="p-2 xl:p-3">
    <Head title="Visits Details" />
    <div class="flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4">
      <form @submit.prevent="submit">
        <div class="flex flex-wrap">
            <div class="flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2">
              <span class="inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"> {{trans('From')}}: </span>
              <input v-model="filters.start_date" type="date" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"/>
            </div>
            <div class="flex rounded-md shadow-sm  mr-0.5 md:mr-1 xl:mr-2">
              <span class="inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"> {{trans('To')}}: </span>
              <input v-model="filters.end_date" type="date" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"/>
            </div>
            <button disabled type="submit" 
            class="py-3 rounded bg-gray-300 text-gray-50 text-sm leading-4 font-bold whitespace-nowrap
             flex items-center group  mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3 cursor-not-allowed
            "
            
            >{{trans('Go')}}  </button>
            <div class="btn-indigo  mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3" @click="tabletoExcel('VisitsReport.xlsx')"> {{trans('ExportToExcel')}} </div> 
            <button type="button" class="btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2" @click="print">{{ trans("Print")}}</button> 
        </div>
      </form>
      <statistics-nav />
    </div>
    <section class="flex justify-between p-2 mb-4 bg-white shadow rounded items-center">
      <div class="flex justify-start items-center w-full">
        <search-filter v-model="filters.search" direction="rtl" placeholder="بحث عن المراجعين" class="mb-2 sm:mb-0 w-full" @reset="reset">
        <label class="block text-gray-700">Trashed:</label>
        <select v-model="filters.trashed" class="mt-1 w-full form-select">
          <option :value="null" />
          <option value="with">With Trashed</option>
          <option value="only">Only Trashed</option>
        </select>
        <label class="block text-gray-700">Visits date:</label>
        <select v-model="filters.sentToday" class="mt-1 w-full form-select">
          <option :value="null"></option>
          <option value="sentToday">Today's Visits</option>
        </select>
        <label class="block text-gray-700">Has Discount:</label>
        <select v-model="filters.HasDiscount" class="mt-1 w-full form-select">
          <option :value="null"></option>
          <option value="Has Discount">HasDiscount</option>
        </select>
      </search-filter>
      </div>
      <select v-model.number="filters.per_page" class="mr-1 block py-2 px-4 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm w-2/12">
          <option value="10">    10 Per Page </option>
          <option value="100">  100 Per Page </option>
          <option value="200">  200 Per Page </option>
          <option value="300">  300 Per Page </option>
          <option value="1000">1000 Per Page </option>
          <option value="2000">2000 Per Page </option>
          <option value="5000">5000 Per Page </option>
        </select>
      <div class="flex items-center whitespace-nowrap px-4 h-9 rounded border border-gray-300 shadow-sm
         focus:ring-blue-500 focus:border-blue-500">Total: {{ visits.total}}</div>
    </section>

    <div id="reportToPrint" class="shadow overflow-x-auto rounded-lg bg-white">
      <table id="table" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-indigo-600">
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"> {{ trans('Patient') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('TestsCount') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('OffersCount') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('TestsCost') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('Discount') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('FinalCost') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('PaidAmount') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('RemainingAmount') }} </th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('Created_at') }}</th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('Updated_at') }}</th>
            <th class="text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('User') }} </th>
          </tr>
        </thead>
        <tr v-for="(visit, index) in visits.data" :key="index" class="">
          <td class="border-t px-3 py-2">{{ visit.name }}<icon v-if="visit.deleted_at" name="trash" class="flex-shrink-0 w-3 h-3 fill-gray-400 ml-2" /></td>
          <td class="border-t px-3 py-2"> {{ visit.tests_count }} </td>
          <td class="border-t px-3 py-2"> {{ visit.offers_count }} </td>
          <td class="border-t px-3 py-2"> {{ visit.tests_cost }} </td>
          <td v-if="visit.discount > 0" class="border-t px-3 py-2">  <span class="w-16 flex items-center justify-center text-sm font-medium py-1 px-1 bg-red-400    text-gray-50">{{ visit.discount }} </span></td>
          <td v-else class="border-t px-3 py-2"> <span class="w-16 flex items-center justify-center text-sm font-medium py-1 px-1 bg-green-400    text-gray-50">{{ visit.discount }}  </span></td>
          <td class="border-t px-3 py-2"> <span class="w-16 flex items-center justify-center text-sm font-medium py-1 px-1 bg-indigo-600 text-gray-50">{{ visit.final_cost }}  </span></td>
          <td class="border-t px-3 py-2"> {{ visit.paid_amount }} </td>
          <td class="border-t px-3 py-2"> {{ visit.remaining_amount }} </td>
          <td class="border-t px-3 py-2">  {{moment( visit.created_at).format('YYYY-MM-DD h:m A')}}</td>
          <td class="border-t px-3 py-2"> {{moment( visit.updated_at).format('YYYY-MM-DD h:m A')}} </td>
          <td class="border-t px-3 py-2"> {{ visit.user }} </td>
        </tr>
        <tr v-if="visits.length === 0">
          <td class="border-t p-0 px-6 py-2" colspan="4">No visits found.</td>
        </tr>
      </table>
      <pagination class="mt-2 px-6 py-2 bg-white border-t p-0" :links="visits.links" :from="visits.from" :to="visits.to" :total="visits.total" />
    </div>

  </div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
  import StatisticsNav from '@/MyComponents/StatisticsNav.vue'
  import Icon from '@/MyComponents/Icon.vue'
  import pickBy from "lodash/pickBy"
  import AppLayout from '@/Layouts/AppLayout.vue'
  import throttle from 'lodash/throttle'
  import Pagination from '@/MyComponents/Pagination.vue'
  import SearchFilter from '@/MyComponents/SearchFilter.vue'
  import LoadingButton from '@/MyComponents/LoadingButton.vue'
  import moment from "moment";
  import { Head, Link } from '@inertiajs/inertia-vue3';
  import ResearchFilter from '@/MyComponents/ResearchFilter.vue';
  import DropdownSearch from '@/MyComponents/DropdownSearch.vue' 
  import { Inertia } from '@inertiajs/inertia';
  // import XLSX from 'xlsx';
import * as XLSX from 'xlsx/xlsx.mjs';
  import { reactive} from '@vue/reactivity'
  import { watch } from '@vue/runtime-core'

  const props = defineProps({
    errors: Object,
    locale: String,
    ReportDateAndTime: String,
    filters: Object,
    visits: Object,
  })

  const filters = reactive({
    search: props.filters.search,
    trashed: props.filters.trashed,
    start_date: props.filters.start_date,
    end_date: props.filters.end_date,
    sentToday: props.filters.sentToday,
    HasDiscount: props.filters.HasDiscount,
    gender: props.filters.gender,
    per_page: props.filters.per_page>10?props.filters.per_page:10,
  })

  watch(filters, throttle(function () {
    Inertia.get(route('visitsDetails'),  pickBy(filters), { preserveState: true, replace: true });
    }, 300),
    { deep: true }
  );

  function reset() {
    Object.assign(filters,{search: null, trashed: '', start_date: null, end_date: null, sentToday: null, HasDiscount: null});
  }

  // let destroy = (visit_id) => {
  //   if (confirm('Are you sure you want to delete this visit ?')) {
  //     Inertia.delete(route('visits.destroy', visit_id))
  //   }
  // };

  // let restore = (visit_id) => {
  //   if (confirm('Are you sure you want to restore this visit ?')) {
  //     Inertia.put(route('visits.restore', visit_id))
  //   }
  // };

  let print = () => {
  setTimeout(function(){
    window.print();
  } , 50);
};
  
let tabletoExcel = (fn, dl) => {
  var elt = document.getElementById('table');
  var wb = XLSX.utils.table_to_book(elt, { sheet: "sheet1" });
  return dl ? XLSX.write(wb, { bookType: type, bookSST: true, type: 'base64' }): XLSX.writeFile(wb, fn || ('MySheetName.' + (type || 'xlsx')));
}
</script>
