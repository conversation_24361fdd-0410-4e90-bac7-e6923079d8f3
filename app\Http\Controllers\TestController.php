<?php

namespace App\Http\Controllers;

use App\Models\Test;
use App\Models\Category;
use App\Models\Device;
use App\Models\Result;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;

class TestController extends Controller
{
    public function index()
    {
        request()->validate([
            'direction' => ['in:asc,desc'],
            'field' => ['in:short_name,created_at,sequence,category_id']
        ]);
        return Inertia::render('Tests/Index', [
            'categories' => Category::orderBy('name')->get()->map->only('id', 'name'),
            'filters' => Request::all('search', 'trashed', 'field', 'direction', 'category_id'),
            'tests' => Test::with('category')
                ->filter(Request::only('search', 'trashed', 'field', 'direction', 'category_id'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($test) => [
                    'id' => $test->id,
                    'short_name' => $test->short_name,
                    'full_name' => $test->full_name,
                    'category' => $test->category ? $test->category->only('name') : null,
                    'sequence' => $test->sequence,
                    'lab_to_patient_price' => $test->lab_to_patient_price,
                    'lab_to_lab_price' => $test->lab_to_lab_price,
                    'created_at' => $test->created_at->diffForHumans(),
                    'updated_at' => $test->updated_at->diffForHumans(),
                    'deleted_at' => $test->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Tests/Create', [
            'categories' => Category::orderBy('name')->get()->map->only('id', 'name'),
            'devices'    => Device::orderBy('id')->get()->map->only('id', 'name'),
            'results'    => Result::orderBy('id')->get()->map->only('id', 'name'),
        ]);
    }

    public function store()
    {
        DB::transaction(function () {
            $test = Auth::user()->tests()->create(
                Request::validate([
                    'short_name' => ['required', 'max:50', 'unique:tests'],
                    'full_name' => ['required', 'max:50', 'unique:tests'],
                    'category_id' => ['required', Rule::exists('categories', 'id')],
                    'default_device_id' => ['nullable', Rule::exists('devices', 'id')],
                    'result_type' => ['required', 'digits_between:0,1'],
                    'unit' => ['nullable'],
                    'sequence' => ['nullable'],
                    'lab_to_patient_price' => ['required', 'digits_between:1,6',
                        function ($attribute, $value, $fail) {
                            if ($value % 250 != 0) {
                                $fail('The price must be divisible by 250.');
                            }
                        },
                    ],
                    'lab_to_lab_price' => ['required', 'digits_between:1,6',
                        function ($attribute, $value, $fail) {
                            if ($value % 250 != 0) {
                                $fail('The price must be divisible by 250.');
                            }
                        },
                    ],
                ])
            );

            Request::validate([
                'testDevices.*.normal_range'     => ['required'],
            ]);

            if (Request::input('result_type')) {
                Request::validate(
                    [
                        'testResults'     => ['required'],
                    ],
                    [
                        'testResults.required' => 'At least one Predefined Result must be attached.!'
                    ]
                );

                foreach (Request::input('testResults') as $result) {
                    $test->results()->syncWithoutDetaching([$result['id']]);
                }
            }

            if(!Request::input('default_device_id') && Request::input('testDevices')){
                $test->update([
                    "default_device_id" => Request::input('testDevices')[0]['id']
                ]);
            }

            foreach (Request::input('testDevices') as $device) {
                $test->devices()->syncWithoutDetaching([
                    $device['id']  =>
                    [
                        'normal_range'  =>  $device['normal_range'],
                    ]
                ]);
            }
        });

        if (Request::input('createAnother')) {
            return Redirect::route('tests.create')->with('success', 'Test created.');
        } else {
            return Redirect::route('tests')->with('success', 'Test created.');
        }
    }

    public function edit(Test $test)
    {
        $tests_devices =  $test->devices()->orderBy('id')->get()->map->only('id');
        $tests_results =  $test->results()->orderBy('id')->get()->map->only('id');
        return Inertia::render('Tests/Edit', [
            'test' => [
                'id'                     => $test->id,
                'short_name'             => $test->short_name,
                'full_name'              => $test->full_name,
                'category_id'            => $test->category_id,
                'default_device_id'      => $test->default_device_id,
                'result_type'            => $test->result_type,
                'unit'                   => $test->unit,
                'sequence'               => $test->sequence,
                'lab_to_patient_price'   => $test->lab_to_patient_price,
                'lab_to_lab_price'       => $test->lab_to_lab_price,
                'deleted_at'             => $test->deleted_at,
                'user_id'                => Auth::user()->id,
                'testResults'            => $test->results() ? $test->results()->orderBy('created_at', 'desc')->get()->map->only('id', 'name') : null,

                'testDevices'            => $test->devices() ? $test->devices()->orderBy('created_at', 'desc')->get()->map(fn ($test) => [
                    "id"            => $test->id,
                    "name"          => $test->name,
                    "normal_range"  => $test->pivot->normal_range
                ])
                    : null,

            ],

            'categories' => Category::orderBy('name')->get()->map->only('id', 'name'),
            'devices'    => Device::whereNotIn('id', $tests_devices)->orderBy('id')->get()->map->only('id', 'name'),
            'results'    => Result::whereNotIn('id', $tests_results)->orderBy('id')->get()->map->only('id', 'name'),
        ]);
    }

    public function update(Test $test)
    {
        DB::transaction(function ()  use ($test) {
            $test->update(
                Request::validate([
                    'short_name'            => ['required', 'max:50',  Rule::unique('tests')->ignore($test->id)],
                    'full_name'             => ['required', 'max:50',  Rule::unique('tests')->ignore($test->id)],
                    'category_id'           => ['required', Rule::exists('categories', 'id')],
                    'default_device_id'     => ['nullable', Rule::exists('devices', 'id')],
                    'result_type'           => ['required', 'digits_between:0,1'],
                    'unit'                  => ['nullable'],
                    'sequence'              => ['nullable'],
                    'lab_to_patient_price'  => ['required', 'digits_between:1,6',
                        function ($attribute, $value, $fail) {
                            if ($value % 250 != 0) {
                                $fail('The price must be divisible by 250.');
                            }
                        },
                    ],
                    'lab_to_lab_price'      => ['required', 'digits_between:1,6',
                    function ($attribute, $value, $fail) {
                        if ($value % 250 != 0) {
                            $fail('The price must be divisible by 250.');
                        }
                    },
                ],
                    'user_id'               => ['required'],
                ])
            );


            Request::validate([
                'testDevices.*.normal_range'     => ['required'],
            ]);

            foreach ($test->devices as $test_device) {
                if (!in_array($test_device->id, Request::input('testDevices'))) {
                    $test->devices()->detach($test_device->id);
                }
            }
            foreach ($test->results as $test_result) {
                if (!in_array($test_result->id, Request::input('testResults'))) {
                    $test->results()->detach($test_result->id);
                }
            }
            if (Request::input('result_type')) {
                Request::validate(
                    [
                        'testResults'     => ['required'],
                    ],
                    [
                        'testResults.required' => 'At least one Predefined Result must be attached.!'
                    ]
                );
                foreach (Request::input('testResults') as $result) {
                    $test->results()->syncWithoutDetaching([$result['id']]);
                }
            }

            if(!Request::input('default_device_id') && Request::input('testDevices')){
                $test->update([
                    "default_device_id" => Request::input('testDevices')[0]['id']
                ]);
            }
            
            foreach (Request::input('testDevices') as $device) {
                $test->devices()->syncWithoutDetaching([
                    $device['id']  =>
                    [
                        'normal_range'  =>  $device['normal_range'],
                    ]
                ]);
            }
        });
        return Redirect::route('tests')->with('success', 'Test updated.');
    }

    public function destroy(Test $test)
    {
        $test->delete();
        return Redirect::route('tests')->with('success', 'Test deleted.');
    }

    public function restore(Test $test)
    {
        $test->restore();
        return Redirect::back()->with('success', 'Test restored.');
    }

    public function forceDelete(Test $test)
    {
        Gate::authorize('force-delete');
        $test->forceDelete();
        return Redirect::route('tests')->with('success', 'The Test has been permanently deleted.');
    }
}
