import{m as n,N as l,o as i,c as m,b as c,B as r,A as t,a as e,E as p,z as u,u as a,C as _}from"./app.5bf25e6f.js";import{J as g}from"./InputError.2a9befad.js";const h={class:"md:grid md:grid-cols-3 md:gap-6"},f={class:"mt-5 md:mt-0 md:col-span-2"},b={class:"grid grid-cols-6 gap-6"},v={key:0,class:"flex items-center justify-end px-4 py-3 bg-gray-50 text-right sm:px-6 shadow sm:rounded-bl-md sm:rounded-br-md"},B={__name:"FormSection",emits:["submitted"],setup($){const o=n(()=>!!l().actions);return(s,d)=>(i(),m("div",h,[c(g,null,{title:r(()=>[t(s.$slots,"title")]),description:r(()=>[t(s.$slots,"description")]),_:3}),e("div",f,[e("form",{onSubmit:d[0]||(d[0]=p(w=>s.$emit("submitted"),["prevent"]))},[e("div",{class:u(["px-4 py-5 bg-white sm:p-6 shadow",a(o)?"sm:rounded-tl-md sm:rounded-tr-md":"sm:rounded-md"])},[e("div",b,[t(s.$slots,"form")])],2),a(o)?(i(),m("div",v,[t(s.$slots,"actions")])):_("",!0)],32)])]))}};export{B as _};
