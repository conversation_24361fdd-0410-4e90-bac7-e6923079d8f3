<?php

namespace App\Http\Controllers;

use App\Models\Patient;
use App\Models\Visit;
use App\Models\Test;

use App\Models\Category;
use App\Models\Device;
use App\Models\Doctor;
use App\Models\Lab;
use App\Models\Flag;
use App\Models\Group;
use App\Models\Offer;
use App\Models\Result;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class TransferController extends Controller
{
    public function transferUsers()
    {
        $old_users = DB::connection('mysql_2')->table("users")->get();
        foreach ($old_users as $old_user) {
            // if ($old_user->id !== 1) {
                $user = User::create([
                    'id'                 => $old_user->id,
                    'name'               => $old_user->name,
                    'email'              => $old_user->email,
                    'password'           => $old_user->password,
                    'role'               => $old_user->role,
                    //'profile_photo_path' => $old_user->profile_photo_path,
                    'created_at'         => $old_user->created_at,
                    'updated_at'         => $old_user->updated_at,
                ]);
            // }
        }
        return 'Success';
    }

    public function transferDevices()
    {
        $old_items = DB::connection('mysql_2')->table("devices")->get();
        foreach ($old_items as $old_item) {
            $new_item = Device::create([
                'id'         => $old_item->id,
                'name'       => $old_item->name,
                'user_id'    => 1,
                'created_at' => $old_item->created_at,
                'updated_at' => $old_item->updated_at,
            ]);
        }

        return 'Success';
    }
    
    public function transferFlags()
    {
        $old_flags = DB::connection('mysql_2')->table("flags")->get();
        foreach ($old_flags as $old_flag) {
            $flag = Flag::create([
                'id'                 => $old_flag->id,
                'name'               => $old_flag->name,
                'user_id'            => 1,
                'created_at'         => $old_flag->created_at,
                'updated_at'         => $old_flag->updated_at,
            ]);
        }
        return 'Success';
    }

    public function transferResults()
    {
        $old_results = DB::connection('mysql_2')->table("results")->get();
        foreach ($old_results as $old_result) {
            $Result = Result::create([
                'id'                 => $old_result->id,
                'name'               => $old_result->name,
                'user_id'            => $old_result->user_id,
                'created_at'         => $old_result->created_at,
                'updated_at'         => $old_result->updated_at,
            ]);
        }
        return 'Success';
    }

    public function transferCategories()
    {
        $old_items = DB::connection('mysql_2')->table("categories")->get();
        foreach ($old_items as $old_item) {
            $new_item = Category::create([
                'id'         => $old_item->id,
                'name'       => $old_item->name,
                'user_id'    => 1,
                'created_at' => $old_item->created_at,
                'updated_at' => $old_item->updated_at,
            ]);
        }

        return 'Success';
    }

    public function transferTests()
    {
        $old_items = DB::connection('mysql_2')->table("tests")->get();
        foreach ($old_items as $old_item) {
            $new_item = Test::create([
                'id'                    => $old_item->id,
                'short_name'            => $old_item->short_name,
                'full_name'             => $old_item->full_name,
                'lab_to_patient_price'  => $old_item->lab_to_patient_price,
                'lab_to_lab_price'      => $old_item->lab_to_lab_price,
                'default_device_id'     => $old_item->default_device_id,
                'result_type'           => $old_item->result_type,
                'sequence'              => $old_item->sequence,
                'category_id'           => $old_item->category_id,
                'user_id'               => 1,
                'created_at'            => $old_item->created_at,
                'updated_at'            => $old_item->updated_at,
                'deleted_at'            => $old_item->deleted_at,
            ]);
        }

        return 'Success';
    }

    public function transferDeviceTest()
    {
        $devices_ids = DB::table("devices")->pluck('id')->toArray();
        $test_ids    = DB::table("tests")->pluck('id')->toArray();
        $old_items = DB::connection('mysql_2')->table("device_test")->get();
        foreach ($old_items as $old_item) {
            if (in_array($old_item->device_id, $devices_ids) && in_array($old_item->test_id, $test_ids)) {
                $new_item = DB::table('device_test')->insert([
                    'id'           => $old_item->id,
                    'device_id'    => $old_item->device_id,
                    'test_id'      => $old_item->test_id,
                    'normal_range' => $old_item->normal_range,
                ]);
            }
        }
        return 'Success';
    }

    public function transferResultTest()
    {
        $result_ids = DB::table("results")->pluck('id')->toArray();
        $test_ids   = DB::table("tests")->pluck('id')->toArray();

        $old_items = DB::connection('mysql_2')->table("result_test")->get();
        foreach ($old_items as $old_item) {
            if (in_array($old_item->result_id, $result_ids) && in_array($old_item->test_id, $test_ids)) {
                $new_item = DB::table('result_test')->insert([
                    'id'           => $old_item->id,
                    'result_id'    => $old_item->result_id,
                    'test_id'      => $old_item->test_id
                ]);
            }
        }

        return 'Success';
    }

    ////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    public function transferPatients()
    {
        $old_patients = DB::connection('mysql_2')->table("patients")->get();
        foreach ($old_patients as $old_patient) {
            $gender = $old_patient->gender ? $old_patient->gender : 4;
            $patient = Patient::create([
                'id'                => $old_patient->id,
                'name'              => $old_patient->name,
                'age'               => $old_patient->age,
                'gender'            => $gender,
                'mobile'            => $old_patient->mobile,
                'user_id'           => $old_patient->user_id,
                'created_at'        => $old_patient->created_at,
                'updated_at'        => $old_patient->updated_at,
                'deleted_at'        => $old_patient->deleted_at,
            ]);
        }
        return 'Success';
    }

    public function transferDoctors()
    {
        $old_doctors = DB::connection('mysql_2')->table("doctors")->get();
        foreach ($old_doctors as $old_doctor) {
            $Doctor = Doctor::create([
                'id'                 => $old_doctor->id,
                'name'               => $old_doctor->name,
                'specialty'          => $old_doctor->specialty,
                'mobile'             => $old_doctor->mobile,
                'gender'             => $old_doctor->gender,
                'user_id'            => $old_doctor->user_id,
                'created_at'         => $old_doctor->created_at,
                'updated_at'         => $old_doctor->updated_at,
                'deleted_at'         => $old_doctor->deleted_at,
            ]);
        }
        return 'Success';
    }

    public function transferLabs()
    {
        $old_labs = DB::connection('mysql_2')->table("labs")->get();
        foreach ($old_labs as $old_lab) {
            $Lab = Lab::create([
                'id'                 => $old_lab->id,
                'name'               => $old_lab->name,
                'owner'              => $old_lab->owner,
                'mobile'             => $old_lab->mobile,
                'user_id'            => $old_lab->user_id,
                'created_at'         => $old_lab->created_at,
                'updated_at'         => $old_lab->updated_at,
                'deleted_at'         => $old_lab->deleted_at,
            ]);
        }
        return 'Success';
    }

    public function transferVisits()
    {
        $patients_ids = DB::table("patients")->pluck('id')->toArray();
        $old_visits = DB::connection('mysql_2')->table("visits")->get();
        foreach ($old_visits as $old_visit) {
            if (in_array($old_visit->patient_id, $patients_ids)) {
                $visit = Visit::create([
                    'id'                => $old_visit->id,
                    'referred_by'       => $old_visit->referred_by,
                    'user_id'           => $old_visit->user_id,
                    'patient_id'        => $old_visit->patient_id,
                    'doctor_id'         => $old_visit->doctor_id,
                    'lab_id'            => $old_visit->lab_id,
                    'tests_count'       => $old_visit->tests_count,
                    'offers_count'      => $old_visit->offers_count,
                    'tests_cost'        => $old_visit->tests_cost,
                    'discount'          => $old_visit->discount,
                    'final_cost'        => $old_visit->final_cost,
                    'paid_amount'       => $old_visit->paid_amount,
                    'remaining_amount'  => $old_visit->remaining_amount,
                    'created_at'        => $old_visit->created_at,
                    'updated_at'        => $old_visit->updated_at,
                    'deleted_at'        => $old_visit->deleted_at,
                ]);
            }
        }

        return 'Success';
    }

    public function transferGroups()
    {
        $old_groups = DB::connection('mysql_2')->table("groups")->get();
        foreach ($old_groups as $old_group) {
            $Group = Group::create([
                'id'                => $old_group->id,
                'name'              => $old_group->name,
                'tests_count'       => $old_group->tests_count,
                'user_id'           => $old_group->user_id,
                'created_at'        => $old_group->created_at,
                'updated_at'        => $old_group->updated_at,
                'deleted_at'        => $old_group->deleted_at,
            ]);
        }

        return 'Success';
    }

    public function transferOffers()
    {
        $old_offers = DB::connection('mysql_2')->table("offers")->get();
        foreach ($old_offers as $old_offer) {
            $Offer = Offer::create([
                'id'                => $old_offer->id,
                'name'              => $old_offer->name,
                'price'             => $old_offer->price,
                'tests_count'       => $old_offer->tests_count,
                'user_id'           => $old_offer->user_id,
                'created_at'        => $old_offer->created_at,
                'updated_at'        => $old_offer->updated_at,
                'deleted_at'        => $old_offer->deleted_at,
            ]);
        }

        return 'Success';
    }

    public function transferGroupTest()
    {
        $groups_ids = DB::table("groups")->pluck('id')->toArray();
        $tests_ids  = DB::table("tests")->pluck('id')->toArray();

        $old_items = DB::connection('mysql_2')->table("group_test")->get();
        foreach ($old_items as $old_item) {
            if (in_array($old_item->group_id, $groups_ids) && in_array($old_item->test_id, $tests_ids)) {
                $new_items = DB::table('group_test')->insert([
                    'id'       => $old_item->id,
                    'group_id' => $old_item->group_id,
                    'test_id'  => $old_item->test_id,
                ]);
            }
        }

        return 'Success';
    }

    public function transferOfferTest()
    {
        $offers_ids = DB::table("offers")->pluck('id')->toArray();
        $tests_ids  = DB::table("tests")->pluck('id')->toArray();

        $old_items = DB::connection('mysql_2')->table("offer_test")->get();
        foreach ($old_items as $old_item) {
            if (in_array($old_item->offer_id, $offers_ids) && in_array($old_item->test_id, $tests_ids)) {
                $new_items = DB::table('offer_test')->insert([
                    'id'       => $old_item->id,
                    'offer_id' => $old_item->offer_id,
                    'test_id'  => $old_item->test_id,
                ]);
            }
        }

        return 'Success';
    }

    public function transferTestsVisits()
    {
        $visits_ids = DB::table("visits")->pluck('id')->toArray();
        $tests_ids  = DB::table("tests")->pluck('id')->toArray();

        $old_test_visits = DB::connection('mysql_2')->table("test_visit")->get();

        foreach ($old_test_visits as $old_test_visit) {
            if (in_array($old_test_visit->visit_id, $visits_ids) && in_array($old_test_visit->test_id, $tests_ids)) {
                DB::table('test_visit')->insert([
                    'id'          => $old_test_visit->id,
                    'test_id'     => $old_test_visit->test_id,
                    'visit_id'    => $old_test_visit->visit_id,
                    'user_id'     => $old_test_visit->user_id,
                    'offer_id'    => $old_test_visit->offer_id,
                    'device_id'   => $old_test_visit->device_id,
                    'flag_id'     => $old_test_visit->flag_id,
                    'result'      => $old_test_visit->value,
                    'value'       => is_numeric($old_test_visit->value) ? $old_test_visit->value : null,
                    'created_at'  => $old_test_visit->created_at,
                    'updated_at'  => $old_test_visit->updated_at,
                ]);
            }
        }
        return 'Success';
    }

    public function transferOfferVisit()
    {
        $offers_ids = DB::table("offers")->pluck('id')->toArray();
        $visits_ids  = DB::table("visits")->pluck('id')->toArray();
        $old_items = DB::connection('mysql_2')->table("offer_visit")->get();
        foreach ($old_items as $old_item) {
            if (in_array($old_item->offer_id, $offers_ids) && in_array($old_item->visit_id, $visits_ids)) {
                $new_items = DB::table('offer_visit')->insert([
                    'id'          => $old_item->id,
                    'offer_id'    => $old_item->offer_id,
                    'visit_id'    => $old_item->visit_id,
                    'user_id'     => $old_item->user_id,
                    'created_at'  => $old_item->created_at,
                    'updated_at'  => $old_item->updated_at,
                ]);
            }
        }
        return 'Success';
    }
}
