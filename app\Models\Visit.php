<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Request;

class Visit extends Model
{
    use HasFactory;
    use SoftDeletes;

    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? 'id', $value)->withTrashed()->firstOrFail();
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('id', '=', $search)
                        ->orWhereHas('patient', function ($query) use ($search) {
                            $query->where('name', 'like', '%' . $search . '%')
                                ->orWhere('id', '=', $search);
                        });
                });
            })

            // ->when($filters['trashed'] ?? null, function ($query, $trashed) {
            //     if ($trashed === 'with') {
            //         $query->withTrashed();
            //     } elseif ($trashed === 'only') {
            //         $query->onlyTrashed();
            //     }
            // })

            ->when($filters['trashed'] ?? null, function ($query, $trashed) {
                $query->whereHas('patient', function ($query) use ($trashed) {
                    if ($trashed === 'with') {
                        $query->withTrashed();
                    } elseif ($trashed === 'only') {
                        $query->onlyTrashed();
                    }
                });
            })

            ->when($filters['tracking_tag_id'] ?? null, function ($query, $tracking_tag_id) {
                $query->whereHas('patient', function ($query) use ($tracking_tag_id) {
                    $query->whereHas('tracking_tags', function ($query) use ($tracking_tag_id) {
                        $query->where('tracking_tags.id', '=', $tracking_tag_id);
                    });
                });
            })

            ->when($filters['test_id'] ?? null, function ($query, $test_id) {
                $query->whereHas('tests',  function ($query) use ($test_id) {
                    $query->where('test_id', '=', $test_id)->whereBetween('value', [Request::all('test_min_value'), Request::all('test_max_value')]);
                });
            })

            ->when($filters['gender'] ?? null, function ($query, $gender) {
                $query->whereHas('patient', function ($query) use ($gender) {
                    $query->where('gender', '=', $gender);
                });
            })

            ->when($filters['smoking'] ?? null, function ($query, $smoking) {
                $query->whereHas('patient', function ($query) use ($smoking) {
                    $query->where('smoking', '=', $smoking);
                });
            })

            ->when($filters['marital'] ?? null, function ($query, $marital) {
                $query->whereHas('patient', function ($query) use ($marital) {
                    $query->where('marital', '=', $marital);
                });
            })

            ->when($filters['age_min'] ?? null, function ($query, $age_min) {
                $query->whereHas('patient', function ($query) use ($age_min) {
                    $query->where('age', '>=', $age_min);
                });
            })

            ->when($filters['age_max'] ?? null, function ($query, $age_max) {
                $query->whereHas('patient', function ($query) use ($age_max) {
                    $query->where('age', '<=', $age_max);
                });
            })

            ->when($filters['start_date'] ?? null, function ($query, $start_date) {
                $query->whereDate('created_at', '>=', $start_date);
            })

            ->when($filters['end_date'] ?? null, function ($query, $end_date) {
                $query->whereDate('created_at', '<=', $end_date);
            })

            ->when($filters['HasDiscount'] ?? null, function ($query) {
                $query->where('discount', '>', 0);
            })

            ->when($filters['sentToday'] ?? null, function ($query) {
                $query->whereDate('created_at', date('Y-m-d'));
            });
    }

    protected $fillable = [
        'id', 'patient_id', 'user_id', 'doctor_id',
        'lab_id', 'referred_by', 'tests_count', 'offers_count', 'tests_cost', 'discount', 'final_cost', 'paid_amount', 'remaining_amount',  'notes',
        'created_at', 'updated_at', 'deleted_at'
    ];

    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }

    public function lab()
    {
        return $this->belongsTo(Lab::class);
    }

    public function tests()
    {
        return $this->belongsToMany(Test::class)->withTimestamps()->withPivot('result', 'value', 'flag_id', 'device_id', 'offer_id');
    }

    public function offers()
    {
        return $this->belongsToMany(Offer::class)->withTimestamps();
    }

    public function user()
    {
        return $this->BelongsTo(User::class);
    }
}
