import{Q as g,o as b,c as h,b as a,u as e,a as r,B as m,d as l,t as n,E as _,z as x,F as v,H as C,L as y}from"./app.5bf25e6f.js";import{A as w}from"./AppLayout.14f8c8f6.js";import{_ as k}from"./TextInput.48e8e32c.js";import{L as u}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";const A={class:"p-4"},L={class:"mb-2 font-bold text-2xl"},V=r("span",{class:"text-indigo-400 font-medium"}," / ",-1),B={class:"bg-white rounded-md shadow overflow-hidden max-w-xl"},S={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},D={layout:w},H=Object.assign(D,{__name:"Create",props:{locale:String},setup(p){let t=g({createAnother:!1,name:null}),i=()=>{t.createAnother=!0,c()},f=()=>{t.createAnother=!1,c()},c=()=>{t.post(route("devices.store"),{preserveState:!0,onSuccess:()=>{t.reset()}})};return(s,o)=>(b(),h(v,null,[a(e(C),{title:"Create Device"}),r("div",A,[r("h2",L,[a(e(y),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("devices")},{default:m(()=>[l(n(s.trans("DevicesList")),1)]),_:1},8,["href"]),V,l(" "+n(s.trans("Create")),1)]),r("div",B,[r("form",{onSubmit:o[1]||(o[1]=_((...d)=>e(i)&&e(i)(...d),["prevent"]))},[r("div",{class:x(["p-8 -mr-6 -mb-8 flex flex-wrap",p.locale=="ar"?"rtl text-right":"ltr text-left"])},[a(k,{modelValue:e(t).name,"onUpdate:modelValue":o[0]||(o[0]=d=>e(t).name=d),error:e(t).errors.name,class:"pr-6 pb-8 w-full",type:"text",direction:"ltr",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),r("div",S,[a(u,{loading:e(t).processing,class:"mr-4 px-3 py-2 btn-green",onClick:e(i)},{default:m(()=>[l(n(s.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),a(u,{loading:e(t).processing,class:"px-3 py-2 btn-green",onClick:e(f)},{default:m(()=>[l(n(s.trans("Create")+" "+s.trans("Device")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{H as default};
