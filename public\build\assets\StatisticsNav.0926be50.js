import{I as c}from"./AppLayout.14f8c8f6.js";import{o as s,c as i,F as u,D as p,y as f,B as d,b as _,z as o,d as m,t as b,u as a,L as h}from"./app.5bf25e6f.js";const x={class:"flex justify-start items-center"},y={__name:"StatisticsNav",props:{},setup(g){let l=[{route:"income_report/index",url:"/statistics/income_report",label:"Income",icon:"income"},{route:"doctors_report/index",url:"/statistics/doctors_report",label:"Doctors",icon:"physician"},{route:"side_labs_report/index",url:"/statistics/side_labs_report",label:"SideLabs",icon:"hospital"},{route:"tests_report/index",url:"/statistics/tests_report",label:"Tests",icon:"test"},{route:"offers_report/index",url:"/statistics/offers_report",label:"Offers",icon:"offers"},{route:"visits_report/index",url:"/statistics/visits_report",label:"Visits",icon:"client"},{route:"visitsDetails",url:"/statistics/visitsDetails",label:"VisitsDetails",icon:"client"}];return(e,r)=>(s(),i("nav",x,[(s(!0),i(u,null,p(a(l),(t,n)=>(s(),f(a(h),{onClick:r[0]||(r[0]=v=>e.sidebarOpened=!1),key:n,href:e.route(t.route),class:o(["flex items-center group py-3 mr-1 xl:mr-2",e.$page.url==t.url?"text-orange-300":"text-white group-hover:text-white"])},{default:d(()=>[_(c,{name:t.icon,class:o(["w-5 h-5 mx-1",e.$page.url==t.url?"fill-white":"fill-indigo-400 group-hover:fill-white"])},null,8,["name","class"]),m(b(e.trans(t.label)),1)]),_:2},1032,["href","class"]))),128))]))}};export{y as _};
