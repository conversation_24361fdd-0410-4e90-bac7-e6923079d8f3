<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Device;
use App\Models\Test;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;

class CategoryController extends Controller
{
    public function index()
    {
        return Inertia::render('Categories/Index', [
            'filters' => Request::all('search', 'trashed'),
            'categories' => Category::orderBy('default_device')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($category) => [
                    'id' => $category->id,
                    'name' => $category->name,
                    'default_device' => $category->default_device,
                    'created_at' => $category->created_at->diffForHumans(),
                    'updated_at' => $category->updated_at->diffForHumans(),
                    'deleted_at' => $category->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Categories/Create', [
            'devices'    => Device::orderBy('id')->get()->map->only('id', 'name'),
        ]);
    }

    public function store()
    {
        Auth::user()->categories()->create(
            Request::validate([
                'name' => ['required', 'max:55', 'unique:categories'],
                'default_device' => ['nullable', 'max:100']
            ])
        );
        return Redirect::route('categories')->with('success', 'Category created.');
    }

    public function edit(Category $category)
    {
        return Inertia::render('Categories/Edit', [
            'category' => [
                'id' => $category->id,
                'name' => $category->name,
                'default_device' => $category->default_device,
                'created_at' => $category->created_at,
                'deleted_at' => $category->deleted_at,
                'tests' => $category->tests()->orderBy('sequence')->get()->map->only('id', 'short_name', 'full_name', 'sequence'),
            ],
            'devices'    => Device::orderBy('id')->get()->map->only('id', 'name'),
        ]);
    }

    public function update(Category $category)
    {
        $category->update(
            Request::validate([
                'name' => ['required', 'max:55', Rule::unique('categories')->ignore($category->id)],
                'default_device' => ['nullable', 'max:100']
            ])
        );
        return Redirect::route('categories')->with('success', 'Category updated.');
    }

    public function updateTestSequence(Test $test)
    {
        $test->update(
            Request::validate([
                'sequence' => ['required', 'max:5000']
            ])
        );
        return Redirect::back()->with('success', 'Test updated.');
    }

    public function destroy(Category $category)
    {
        $category->delete();
        return Redirect::route('categories')->with('success', 'Category deleted.');
    }

    public function restore(Category $category)
    {
        $category->restore();
        return Redirect::back()->with('success', 'Category restored.');
    }
}
