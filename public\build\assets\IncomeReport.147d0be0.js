import{Q as f,o as m,c,b as u,u as n,a as t,E as b,t as e,K as x,S as h,B as y,z as w,d as l,F as v,D as S,H as T}from"./app.5bf25e6f.js";import{_ as k}from"./StatisticsNav.0926be50.js";import{A as D}from"./AppLayout.14f8c8f6.js";import{L as I}from"./LoadingButton.c8fb65b2.js";import{u as A,w as R,a as B}from"./xlsx.0799a57e.js";import{h as E}from"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const F={class:"p-2 xl:p-3"},M={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4"},N={class:"flex flex-wrap"},C={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},Q={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},V={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},L={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},Y={id:"reportToPrint",class:"flex overflow-hidden max-w-3xl max-h-fit"},j={class:"flex w-full"},U={id:"table",class:"w-full shadow-lg"},z={class:"ttr"},H={class:"tth text-center",colspan:"3"},O={class:"ttr"},P={class:"ttr"},$={colspan:"1",class:"tth text-center"},G={colspan:"1",class:"tth text-center"},K={colspan:"1",class:"tth text-center"},q={key:0},J={class:"text-yellow-500"},W={class:"text-yellow-500"},X=l(" )"),Z={key:1},tt=l("( "),et={class:"text-yellow-500"},st=l(" )"),ot={class:"ttd text-yellow-500"},rt={class:"ttd text-yellow-500"},nt={class:"ttd text-yellow-500"},at={class:"ttr"},lt={class:"ttd text-green-400 bg-gray-500"},dt={class:"ttd text-green-400 bg-gray-500"},it={class:"ttd text-green-400 bg-gray-500"},mt={layout:D},ft=Object.assign(mt,{__name:"IncomeReport",props:{errors:Object,locale:String,ReportDateAndTime:String,start_date:String,end_date:String,all_users_todays_income:Number|String,all_users_cumulative_income:Number|String,users_income:Array},setup(a){let d=f({start_date:null,end_date:null}),_=()=>{d.post(route("income_report/fetch"),{preserveState:!0,onSuccess:()=>{}})},p=()=>{setTimeout(function(){window.print()},50)},g=(s,r)=>{var o=document.getElementById("table"),i=A.table_to_book(o,{sheet:"sheet1"});return r?R(i,{bookType:type,bookSST:!0,type:"base64"}):B(i,s||"MySheetName."+(type||"xlsx"))};return(s,r)=>(m(),c("div",F,[u(n(T),{title:"IncomeReport"}),t("div",M,[t("form",{onSubmit:r[4]||(r[4]=b((...o)=>n(_)&&n(_)(...o),["prevent"]))},[t("div",N,[t("div",C,[t("span",Q,e(s.trans("From"))+": ",1),x(t("input",{"onUpdate:modelValue":r[0]||(r[0]=o=>n(d).start_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,n(d).start_date]])]),t("div",V,[t("span",L,e(s.trans("To"))+": ",1),x(t("input",{"onUpdate:modelValue":r[1]||(r[1]=o=>n(d).end_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,n(d).end_date]])]),u(I,{loading:n(d).processing,type:"submit",class:"btn-indigo flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3"},{default:y(()=>[l(e(s.trans("Go")),1)]),_:1},8,["loading"]),t("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:r[2]||(r[2]=o=>n(g)("IncomeReport.xlsx"))},e(s.trans("ExportToExcel")),1),t("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:r[3]||(r[3]=(...o)=>n(p)&&n(p)(...o))},e(s.trans("Print")),1)])],32),u(k)]),t("div",Y,[t("div",j,[t("table",U,[t("tr",z,[t("th",H,e(s.trans("IncomeReport")),1)]),t("tr",O,[t("th",{class:w(["tth",a.locale=="ar"?"rtl text-right":"ltr text-left"]),locale:"",colspan:"3"},e(s.trans("ReportDateAndTime"))+": "+e(n(E)(a.ReportDateAndTime).format("YYYY-MM-DD h:m A")),3)]),t("tr",P,[t("th",$,e(s.trans("Users")),1),t("th",G,e(s.trans("Today")),1),t("th",K,[l(e(s.trans("Cumulative"))+" - ",1),a.start_date&&a.end_date?(m(),c("span",q,[l("( "+e(s.trans("From"))+" ",1),t("span",J,e(a.start_date),1),l(" "+e(s.trans("To"))+" ",1),t("span",W,e(a.end_date),1),X])):(m(),c("span",Z,[tt,t("span",et,e(s.trans("ThisMonth")),1),st]))])]),(m(!0),c(v,null,S(a.users_income,(o,i)=>(m(),c("tr",{class:"ttr",key:i},[t("td",ot,e(i+1+" - "+o.user_name),1),t("td",rt,e(o.todays_income)+" IQD",1),t("td",nt,e(o.cumulative_income)+" IQD",1)]))),128)),t("tr",at,[t("td",lt,e(s.trans("Total")),1),t("td",dt,e(a.all_users_todays_income)+" IQD",1),t("td",it,e(a.all_users_cumulative_income)+" IQD",1)])])])])]))}});export{ft as default};
