<template>
  <Head title="Update Offer" />
  <div class="p-2 sm:p-4">
    <div class="mb-2 flex justify-start">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('offers')">{{ trans('OffersList') }}</Link>
        <span class="text-indigo-400 font-medium"> / </span>
        {{ form.name }}
      </h2>
    </div>
    <div class="bg-white rounded-md shadow max-w-2xl">
      <trashed-message v-if="offer.deleted_at" class="mb-6" @restore="restore">
      {{ trans('This') + ' ' +  trans('Offer') + ' ' + trans('HasBeenDeleted')}}
    </trashed-message>
      <form @submit.prevent="update">
        <div class="p-5 -mr-6 -mb-5 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" direction="ltr" class="pr-6 pb-4 w-full  sm:w-1/2" type="text" label="Name" :autofocus="true" />
          <text-input v-model.number="form.price" :error="form.errors.price" direction="ltr" class="pr-6 pb-4 w-full  sm:w-1/2" type="number" label="Price" />
          <div class="pr-4 pb-0 w-full text-blue-600 font-semibold">
            <span class="p-1">{{trans('Tests')}}:</span>
          </div>

          <div v-if="form.offerTests.length>0" class="ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2">
            <div class="w-full flex-row flex justify-start items-center flex-wrap">
                <div class="p-1 min-w-fit max-w-fit mt-1"  v-for="(test, index) in form.offerTests" :key="index">
                    <label title="Remove Test" @click="removeTest(test.id, test.short_name, index)"  class="pt-0.5 btn-remove border border-green-400" :for="test.short_name">{{index+1}} - {{test.short_name}}</label>
                </div>
            </div>
          </div>
          <div v-else class="ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2">
            <div class="flex flex-wrap">
                <div class="flex items-stretch p-1">There are no attached tests yet.</div>
            </div>
          </div>
          <div v-if="form.errors.tests_count" class="form-error">{{ form.errors.tests_count }}</div>

          <div class="ltr text-left w-full mt-2">
            <dropdown-search class="pr-6 pb-2 w-full sm:w-1/2" :options="ourTests" @select="addTest" 
            :CloseOnSelect="false" :fixedLabel="true" label="Select Test" searchBy="short_name" placeholder="Search for tests">
            </dropdown-search>
          </div>
        </div>
        <div class="ml-6 pb-2 text-red-600" v-if="form.isDirty">There are unsaved form changes.</div>
        <div class="flex px-4 py-2 bg-gray-100 border-t border-gray-100  justify-between items-center">
          <loading-button :loading="form.processing" class="mr-4 px-3 py-2 flex btn-green" @click="update">{{trans('Update') + ' ' + trans('Offer')}}</loading-button>
          <loading-button v-if="!offer.deleted_at" class="mr-4 text-red-600 px-3 py-2 flex" @click="destroy">{{trans('Delete') + ' ' + trans('Offer')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
 import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import DropdownSearch from '@/MyComponents/DropdownSearch.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { reactive, ref } from '@vue/reactivity'
import { Inertia } from '@inertiajs/inertia'

  const props = defineProps({
    locale: String,
    tests: Array,
    offer: Object,
  });

  let form = useForm({
    // _token: document.head.querySelector('meta[name="csrf_token"]').content,
    name: props.offer.name,
    price: props.offer.price,
    tests_count: props.offer.tests_count,
    offerTests: props.offer.offerTests
  });

  const ourTests = reactive(props.tests);

  let addTest = (selectedTest) => {
    form.offerTests.push({
      id:   selectedTest.id,
      short_name: selectedTest.short_name,
    });
    ourTests.splice(ourTests.findIndex(test => test.id === selectedTest.id), 1);
  };

  let removeTest = (id, short_name, index) => {
    ourTests.push({
        id:   id,
        short_name: short_name,
    });
    form.offerTests.splice(index, 1);
  };

  let update = () => {

    form.tests_count = form.offerTests.length;
    form.put(route('offers.update', props.offer.id),{
      preserveState: true,
    })
  };

  let destroy = () => {
    if (confirm('Are you sure you want to delete this offer?')) {
      Inertia.delete(route('offers.destroy', props.offer.id))
    }
  };

  let restore = () => {
    if (confirm('Are you sure you want to restore this offer?')) {
      Inertia.put(route('offers.restore', props.offer.id))
    }
  };

</script>
