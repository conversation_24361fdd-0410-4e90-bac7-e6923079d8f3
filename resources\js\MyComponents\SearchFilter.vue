<template>

  <div class="relative flex items-cente w-full rounded font-maven">
    <div class="backdrop" v-if="show" @click.self="closeDropdown"></div>
      <div class="flex sm:bg-white w-72 rounded shadow-sm">
        <button @click="show = !show" class="flex items-center px-3 rounded-l border border-gray-300 border-r-0">
            <span class="text-gray-700">Filter</span>
            <svg class="w-2 h-2 fill-gray-700 ml-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 961.243 599.998"> <path d="M239.998 239.999L0 0h961.243L721.246 240c-131.999 132-240.28 240-240.624 239.999-.345-.001-108.625-108.001-240.624-240z" /> </svg>         
        </button>
        <transition enter-active-class="transition duration-100 ease-out transform" enter-from-class="opacity-0 scale-50" enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-100 ease-in transform" leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-50">
          <div v-show="show" class="z-40 px-4 pt-2 pb-4 overflow-hidden absolute left-0 top-10 mt-1 w-72 bg-white rounded-md border">
            <slot />
          </div>
        </transition>
        <input :value="modelValue"  @input="$emit('update:modelValue', $event.target.value)" ref="myInput"
        :class="{rtl: direction === 'rtl'}" :placeholder="placeholder"
         class="px-2 py-2 w-full border border-gray-300 focus:ring-2 focus:ring-indigo-500 rounded-r focus:outline-none">
      </div>
      <button class="mx-2 text-sm text-gray-500 hover:text-gray-700 focus:text-indigo-500" @click="reset">Reset</button>
  </div>
</template>

<script>
import { defineComponent, onMounted, ref } from 'vue'
export default defineComponent({
  emits: ['reset', 'update:modelValue'],
  props: ['modelValue', 'direction', 'placeholder'],

  //via composition API
  setup(props, {emit}){
    
    const show = ref(false)
    const myInput = ref(null)
    
    const closeDropdown = () =>{
        show.value = false
        myInput.value.focus()
    }

    const reset = () => {
        emit('reset')
        myInput.value.focus()
    }


    onMounted(() => myInput.value.focus())
    return {show, closeDropdown, myInput, reset}
  
    // and can directly return
    // return {
    //   show,
    //   closeDropdown() {
    //     show.value = false
    //   }
    // }
  },
  // Via options API
    // data(){
    //   return {
    //     show: false,
    //   }
    // },
    // methods:{
    //   closeDropdown(){
    //     this.show = false;
    //   }
    // }
})
</script>