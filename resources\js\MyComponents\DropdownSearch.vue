<template> 
    <div>
        <div class="bg-white rounded-md relative m-0 p-0 z-20">
            <div class="backdrop cursor-pointer" v-if="isOpen" @click.self="closeDropdown"></div>
            <div @click="openDropdown" class="flex justify-between items-center px-4 py-2 text-gray-800 font-normal
            rounded-md border border-gray-300 shadow-sm max-h-10 cursor-pointer">
                <span v-if="selectedItem && !fixedLabel">{{selectedItem[key]}}</span>
                <span v-else>{{trans(label) }}</span>
                <svg :class="isOpen ? 'dropdown' : ''" class="dropdown-icon h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" /></svg>
            </div>

            <div :class="isOpen ? 'visible' : ''" class="bg-white transition-all ease-linear overflow-hidden max-h-0 invisible
            absolute top-11 left-0 right-0 border border-gray-200 max-w-full p-2 rounded-md shadow-md">
                <input ref="input" class="form-input py-1" type="text" v-model="searchQuery" 
                 :class="{rtl: direction === 'rtl', ltr: direction === 'ltr'}" :placeholder="placeholder">
                <span v-if="filteredItems.length === 0">No data Available</span>
                <div class="w-full mt-1" :class="{rtl: direction === 'rtl', ltr: direction === 'ltr', textRight: direction === 'rtl'}">
                    <ul class=" max-h-36 overflow-y-auto overflow-x-hidden">
                        <li class="bg-white w-full px-2 cursor-pointer border-b border-blue-200 hover:bg-blue-600 hover:text-white" 
                        @click="selectItem(item)" v-for="(item, index) in filteredItems" :key="index">
                            {{item[key]}}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <div v-if="error" class="form-error">{{ error }}</div>
    </div>
    
</template>

<script setup>
import { computed, ref, reactive } from "@vue/reactivity";
import { onMounted } from "@vue/runtime-core";

  const props = defineProps({
    options: Array,
    placeholder: String,
    CloseOnSelect: Boolean,
    modelValue: [String, Number, Boolean],
    direction: String,
    searchBy: String,
    label: String,
    fixedLabel: Boolean, 
    error: String,
  });
  const emits =  defineEmits(['select']);
  const input = ref(null);
  let searchQuery = ref('');
  let selectedItem = ref('');
  let isOpen = ref(false);
  const itemsArray = ref(props.options);

  const key = props.searchBy;

//   onMounted(() => {
//   itemsArray.value.filter((item) => {
//         console.log(item[key]) 
//   });
// });

  const filteredItems = computed(() => {
    return itemsArray.value.filter((item) => {
        return item[key].toLowerCase().includes(searchQuery.value.toLowerCase());
    })
  });

  let selectItem = ((item) => {
      selectedItem.value = item;
      if(props.CloseOnSelect){
        isOpen.value = false;
      }else{
          focus();
      }

       emits('select', item)
       searchQuery.value = '';
  })

  let openDropdown = (() => {
      isOpen.value = !isOpen.value;
      if(isOpen.value){
          setTimeout(() => { focus(); }, 50);
      }
  });

  let closeDropdown = (() => {
      isOpen.value = false;
      searchQuery.value = '';
  })

  let focus = () => {
  input.value.focus();
};

// const filteredItems = computed(() => {
//     const query = searchQuery.value.toLowerCase();
//     if(searchQuery.value === ''){
//         return itemsArray.value;
//     }
//     return itemsArray.value.filter((item) => {
//         return Object.values(item).some((word) =>
//             String(word).toLowerCase().includes(query)
//         );
//     })
//   });
</script>
<script>
  export default { inheritAttrs: true }
</script>
<style scoped>


    .visible{
        visibility: visible;
        max-height: 450px;
    }
 
   

    .dropdown-icon{
        transform: rotate(180deg);
        transition: all 0.2s ease;
    }
    .dropdown-icon.dropdown{
        transform: rotate(0deg);
    }
   .textRight{
        text-align: right;
   }
</style>