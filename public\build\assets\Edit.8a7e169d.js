import{Q as h,o as u,c as w,b as n,u as e,a as s,B as b,d as m,t as i,y as L,C as y,E as V,z as S,F as v,H as k,L as B,U as g}from"./app.5bf25e6f.js";import{A as _}from"./AppLayout.14f8c8f6.js";import{_ as p}from"./TextInput.48e8e32c.js";import{L as C}from"./LoadingButton.c8fb65b2.js";import{T as N}from"./TrashedMessage.5487e7e2.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const U={class:"p-4"},j={class:"mb-2 flex justify-start max-w-xl"},A={class:"font-bold text-2xl"},E=s("span",{class:"text-indigo-400 font-medium"}," /",-1),T={class:"bg-white rounded-md shadow-sm overflow-hidden max-w-xl"},D={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},F={layout:_},$=Object.assign(F,{__name:"Edit",props:{locale:String,lab:Object},setup(d){const l=d;let t=h({_method:"put",name:l.lab.name,owner:l.lab.owner,mobile:l.lab.mobile}),f=()=>{t.post(route("labs.update",l.lab.id),{onSuccess:()=>t.reset("name")})},c=()=>{confirm("Are you sure you want to delete this SideLab?")&&g.Inertia.delete(route("labs.destroy",l.lab.id))},x=()=>{confirm("Are you sure you want to restore this SideLab?")&&g.Inertia.put(route("labs.restore",l.lab.id))};return(o,r)=>(u(),w(v,null,[n(e(k),{title:"Edit SideLab"}),s("div",U,[s("div",j,[s("h2",A,[n(e(B),{class:"text-indigo-400 hover:text-indigo-600",href:o.route("labs")},{default:b(()=>[m(i(o.trans("SideLabsList")),1)]),_:1},8,["href"]),E,m(" "+i(e(t).name),1)])]),s("div",T,[d.lab.deleted_at?(u(),L(N,{key:0,class:"mb-6",onRestore:e(x)},{default:b(()=>[m(i(o.trans("This")+" "+o.trans("SideLab")+" "+o.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):y("",!0),s("form",{onSubmit:r[4]||(r[4]=V((...a)=>e(f)&&e(f)(...a),["prevent"]))},[s("div",{class:S(["p-8 -mr-6 -mb-8 flex flex-wrap",d.locale=="ar"?"rtl text-right":"ltr text-left"])},[n(p,{modelValue:e(t).name,"onUpdate:modelValue":r[0]||(r[0]=a=>e(t).name=a),error:e(t).errors.name,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),n(p,{modelValue:e(t).owner,"onUpdate:modelValue":r[1]||(r[1]=a=>e(t).owner=a),error:e(t).errors.owner,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Owner"},null,8,["modelValue","error"]),n(p,{modelValue:e(t).mobile,"onUpdate:modelValue":r[2]||(r[2]=a=>e(t).mobile=a),error:e(t).errors.mobile,direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Mobile"},null,8,["modelValue","error"])],2),s("div",D,[n(C,{loading:e(t).processing,class:"btn-green",type:"submit"},{default:b(()=>[m(i(o.trans("Update")+" "+o.trans("SideLab")),1)]),_:1},8,["loading"]),d.lab.deleted_at?y("",!0):(u(),w("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:r[3]||(r[3]=(...a)=>e(c)&&e(c)(...a))},i(o.trans("Delete")+" "+o.trans("SideLab")),1))])],32)])])],64))}});export{$ as default};
