<template>
  <Head title="Edit Test" />
  <div class="p-2 sm:p-4">
    <div class="mb-2 flex justify-start">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('tests')">{{ trans('TestsList') }}</Link>
        <span class="text-indigo-400 font-medium"> / </span>
        {{ form.short_name }}
      </h2>
    </div>
    
    <div class="bg-white rounded-md shadow max-w-4xl">
      <trashed-message v-if="test.deleted_at" class="mb-6 min-w-full" @restore="restore">
        {{ trans('This') + ' ' +  trans('Test') + ' ' + trans('HasBeenDeleted')}}
      </trashed-message>
      <form @submit.prevent="update">
        <div class="p-6 -mr-6 -mb-5 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.short_name" :error="form.errors.short_name" direction="ltr" class="pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4" type="text" label="ShortName" :autofocus="true" />
          <text-input v-model="form.full_name" :error="form.errors.full_name"   direction="ltr" class="pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4" type="text" label="FullName" />

          <select-input v-model="form.category_id" :error="form.errors.category_id" direction="ltr" class="pr-6 pb-6 w-full text-right sm:w-1/2 md:w-1/3 lg:w-1/4" label="Category">
            <option value="null" hidden selected disabled>{{trans("SelectCategory")}}</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">{{ category.name }}</option>
          </select-input>
          <div class="pr-6 pb-6 w-full  sm:w-1/2 md:w-1/3 lg:w-1/4">
            <label class="form-label" for="Sequence">{{trans('Sequence')}}:</label>
            <input id="Sequence" v-model="form.sequence" placeholder="Sequence" class="form-input ltr" :class="{ error: form.errors.sequence }" type="number" />
            <div v-if="form.errors.sequence" class="form-error">{{ form.errors.sequence }}</div>
          </div>
          
          <text-input v-model="form.lab_to_patient_price" :error="form.errors.lab_to_patient_price" direction="ltr" class="pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4" type="number" label="LabToPatientPrice" />
          <text-input v-model="form.lab_to_lab_price" :error="form.errors.lab_to_lab_price" direction="ltr" class="pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4" type="number" label="LabToLabPrice" />

          <select-input v-model="form.default_device_id " :error="form.errors.default_device_id" direction="ltr" class="pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4" label="DefaultDevice">
            <option value="null" hidden selected disabled>{{trans("SelectDefaultDevice")}}</option>
            <option v-for="device in form.testDevices" :key="device.id" :value="device.id">{{ device.name }}</option>
          </select-input>

          <select-input v-model.number="form.result_type" :error="form.errors.result_type" class="pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4" label="ResultsType">
            <option :value="0">{{trans("NumericResults")}}</option>
            <option :value="1">{{trans("PredefinedResults")}}</option>
          </select-input>

          <div v-if="form.result_type" class="ltr text-left pr-4 pb-0 w-full text-gray-700 font-semibold">
            <span class="p-1">Test Predefined Results:</span>
          </div>

          <div v-if="form.result_type" class="ltr text-left w-full flex flex-wrap bg-indigo-300 py-1 px-1 mr-6 rounded h-auto mb-2">
            <div title="Remove Result" class="pr-2 pt-1 pb-1 px-0" v-for="(result, index) in form.testResults" :key="index">
              <span title="Remove Result" @click="removeResult(result.id, result.name, index)" class="btn-remove pt-0.5">{{result.name }}</span>
            </div>
            <span class="px-2" v-if="form.testResults.length<=0">There are no attached results yet..!</span>
            <div v-if="form.errors.testResults" class="form-error w-full">{{ form.errors.testResults }}</div>
          </div>
          
          <dropdown-search class="text-left z-40 pr-6 pb-2 w-full sm:w-1/2 lg:w-1/3" :options="ourDevices" @select="addDevice" 
          :CloseOnSelect="false" :fixedLabel="true" label="SelectDevice" searchBy="name" direction="ltr" placeholder="Search for devices">
          </dropdown-search>

          <dropdown-search v-if="form.result_type" class="text-left pr-6 pb-2 w-full sm:w-1/2 lg:w-1/3" :options="ourResults" @select="addResult" 
          :CloseOnSelect="false" :fixedLabel="true" label="SelectResult" searchBy="name" direction="ltr" placeholder="Search for Predefined Results">
          </dropdown-search>

          <div class="ltr text-left pr-4 pb-2 w-full text-green-600 font-semibold">
            <span class="p-1">Test Devices:</span>
          </div>

          <div class="ltr text-left pr-6 pb-2 w-1/2" v-for="(device, index) in form.testDevices" :key="index">
              <span title="Remove device" @click="removeDevice(device.id, device.name, index)" class="btn-remove pt-0.5" for="Normal">{{device.name }} Normal Range: </span>
              <div class="w-full h-auto">
                  <QuillEditor class="min-h-full" v-model:content="device.normal_range"  contentType="html" :toolbar="toolbarOptions"  theme="snow" />
              </div>
          </div>
        
        </div>
        <div class="ml-6 pb-1 text-red-500" v-if="form.isDirty">There are unsaved form changes, click update button to save them.</div>
        <div class="flex px-4 py-2 bg-gray-100 border-t border-gray-100  justify-between items-center">
          <loading-button :loading="form.processing" class="mr-4 px-3 py-2 flex btn-green" @click="update">{{trans('Update') + ' ' + trans('Test')}}</loading-button>
          <loading-button class="mr-4 text-red-600 px-3 py-2 flex" @click="forceDelete">{{trans('forceDelete') + ' ' + trans('Test')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
 import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import DropdownSearch from '@/MyComponents/DropdownSearch.vue' 
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { reactive, ref } from '@vue/reactivity'
import { Inertia } from '@inertiajs/inertia'

import { QuillEditor} from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';


var toolbarOptions = [
  ['bold', 'italic', 'underline'],        // toggled buttons             // custom button values
  [{ 'list': 'ordered'}, { 'list': 'bullet' }],
  [{ 'direction': 'rtl' }],                         // text direction

  [{ 'size': ['small', false, 'large', 'huge'] }],  // custom dropdown
  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],

  [{ 'color': [] }, { 'background': [] }],          // dropdown with defaults from theme
  [{ 'font': [] }],
  [{ 'align': [] }],
];


  const props = defineProps({
    locale: String,
    test: Object,  
    categories: Array,
    devices: Array,
    results: Array,
  });

  let form = useForm({
    // _token: document.head.querySelector('meta[name="csrf_token"]').content,
    createAnother: false,
    short_name: props.test.short_name,
    full_name: props.test.full_name,
    category_id: props.test.category_id,
    default_device_id: props.test.default_device_id, 
    result_type: props.test.result_type, 
    sequence: props.test.sequence,
    lab_to_patient_price: props.test.lab_to_patient_price,
    lab_to_lab_price: props.test.lab_to_lab_price,
    user_id: props.test.user_id,
    testDevices: props.test.testDevices,
    testResults: props.test.testResults,
  });

  const ourDevices = reactive(props.devices);
  const ourResults = reactive(props.results);

  let addDevice = (selectedDevice) => {
    form.testDevices.push({
      id: selectedDevice.id,
      name: selectedDevice.name,
      normal_range: '',
    });
    ourDevices.splice(ourDevices.findIndex(device => device.id === selectedDevice.id), 1);
  };

  let removeDevice = (id, name, index) => {
      if(form.default_device_id === id){
          form.default_device_id = null;
      }
      ourDevices.push({
          id:                  id,
          name:                name,
          normal_range:        '',
      });
      form.testDevices.splice(index, 1);
  };

  let addResult = (selectedResult) => {
    form.testResults.push({
      id:   selectedResult.id,
      name: selectedResult.name,
    });
    ourResults.splice(ourResults.findIndex(result => result.id === selectedResult.id), 1);
  };

  let removeResult = (id, name, index) => {
    ourResults.push({
        id:   id,
        name: name,
    });
    form.testResults.splice(index, 1);
  };

  let update = () => {
    form.put(route('tests.update', props.test.id),{
      preserveState: true,
      onSuccess: () => { 
        form.reset();
        ourDevices.splice(0, ourDevices.length);
        ourResults.splice(0, ourResults.length);
      },
    })
  };

  let forceDelete = () => {
    if (confirm('Are you sure you want to permanently delete this test?')) {
      Inertia.delete(route('tests.forceDelete', props.test.id))
    }
  };

  let restore = () => {
    if (confirm('Are you sure you want to restore this test?')) {
      Inertia.put(route('tests.restore', props.test.id))
    }
  };
</script>
