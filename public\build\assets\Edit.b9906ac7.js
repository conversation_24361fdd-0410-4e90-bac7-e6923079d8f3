import{Q as V,v as B,o as l,c as f,b as d,u as e,a as r,B as p,d as m,t as n,y,C as h,E as L,z as O,F as x,D,H as j,L as A,U as g}from"./app.5bf25e6f.js";import{A as N}from"./AppLayout.14f8c8f6.js";import{_ as w}from"./TextInput.48e8e32c.js";import{D as U}from"./DropdownSearch.7a091d54.js";import{L as v}from"./LoadingButton.c8fb65b2.js";import{T as E}from"./TrashedMessage.5487e7e2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const F={class:"p-2 sm:p-4"},H={class:"mb-2 flex justify-start"},I={class:"font-bold text-2xl"},M=r("span",{class:"text-indigo-400 font-medium"}," / ",-1),R={class:"bg-white rounded-md shadow max-w-2xl"},z={class:"pr-4 pb-0 w-full text-blue-600 font-semibold"},$={class:"p-1"},P={key:0,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},Q={class:"w-full flex-row flex justify-start items-center flex-wrap"},q=["onClick","for"],G={key:1,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},J=r("div",{class:"flex flex-wrap"},[r("div",{class:"flex items-stretch p-1"},"There are no attached tests yet.")],-1),K=[J],W={key:2,class:"form-error"},X={class:"ltr text-left w-full mt-2"},Y={key:0,class:"ml-6 pb-2 text-red-600"},Z={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-between items-center"},ee={layout:N},de=Object.assign(ee,{__name:"Edit",props:{locale:String,tests:Array,offer:Object},setup(u){const i=u;let t=V({name:i.offer.name,price:i.offer.price,tests_count:i.offer.tests_count,offerTests:i.offer.offerTests});const c=B(i.tests);let k=s=>{t.offerTests.push({id:s.id,short_name:s.short_name}),c.splice(c.findIndex(a=>a.id===s.id),1)},T=(s,a,o)=>{c.push({id:s,short_name:a}),t.offerTests.splice(o,1)},_=()=>{t.tests_count=t.offerTests.length,t.put(route("offers.update",i.offer.id),{preserveState:!0})},C=()=>{confirm("Are you sure you want to delete this offer?")&&g.Inertia.delete(route("offers.destroy",i.offer.id))},S=()=>{confirm("Are you sure you want to restore this offer?")&&g.Inertia.put(route("offers.restore",i.offer.id))};return(s,a)=>(l(),f(x,null,[d(e(j),{title:"Update Offer"}),r("div",F,[r("div",H,[r("h2",I,[d(e(A),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("offers")},{default:p(()=>[m(n(s.trans("OffersList")),1)]),_:1},8,["href"]),M,m(" "+n(e(t).name),1)])]),r("div",R,[u.offer.deleted_at?(l(),y(E,{key:0,class:"mb-6",onRestore:e(S)},{default:p(()=>[m(n(s.trans("This")+" "+s.trans("Offer")+" "+s.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):h("",!0),r("form",{onSubmit:a[2]||(a[2]=L((...o)=>e(_)&&e(_)(...o),["prevent"]))},[r("div",{class:O(["p-5 -mr-6 -mb-5 flex flex-wrap",u.locale=="ar"?"rtl text-right":"ltr text-left"])},[d(w,{modelValue:e(t).name,"onUpdate:modelValue":a[0]||(a[0]=o=>e(t).name=o),error:e(t).errors.name,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),d(w,{modelValue:e(t).price,"onUpdate:modelValue":a[1]||(a[1]=o=>e(t).price=o),modelModifiers:{number:!0},error:e(t).errors.price,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2",type:"number",label:"Price"},null,8,["modelValue","error"]),r("div",z,[r("span",$,n(s.trans("Tests"))+":",1)]),e(t).offerTests.length>0?(l(),f("div",P,[r("div",Q,[(l(!0),f(x,null,D(e(t).offerTests,(o,b)=>(l(),f("div",{class:"p-1 min-w-fit max-w-fit mt-1",key:b},[r("label",{title:"Remove Test",onClick:te=>e(T)(o.id,o.short_name,b),class:"pt-0.5 btn-remove border border-green-400",for:o.short_name},n(b+1)+" - "+n(o.short_name),9,q)]))),128))])])):(l(),f("div",G,K)),e(t).errors.tests_count?(l(),f("div",W,n(e(t).errors.tests_count),1)):h("",!0),r("div",X,[d(U,{class:"pr-6 pb-2 w-full sm:w-1/2",options:c,onSelect:e(k),CloseOnSelect:!1,fixedLabel:!0,label:"Select Test",searchBy:"short_name",placeholder:"Search for tests"},null,8,["options","onSelect"])])],2),e(t).isDirty?(l(),f("div",Y,"There are unsaved form changes.")):h("",!0),r("div",Z,[d(v,{loading:e(t).processing,class:"mr-4 px-3 py-2 flex btn-green",onClick:e(_)},{default:p(()=>[m(n(s.trans("Update")+" "+s.trans("Offer")),1)]),_:1},8,["loading","onClick"]),u.offer.deleted_at?h("",!0):(l(),y(v,{key:0,class:"mr-4 text-red-600 px-3 py-2 flex",onClick:e(C)},{default:p(()=>[m(n(s.trans("Delete")+" "+s.trans("Offer")),1)]),_:1},8,["onClick"]))])],32)])])],64))}});export{de as default};
