import{o as r,c as o,A as n}from"./app.5bf25e6f.js";const s=["type"],i={__name:"DangerButton",props:{type:{type:String,default:"button"}},setup(e){return(t,a)=>(r(),o("button",{type:e.type,class:"inline-flex items-center justify-center px-4 py-2 bg-red-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-red-500 focus:outline-none focus:border-red-700 focus:ring focus:ring-red-200 active:bg-red-600 disabled:opacity-25 transition"},[n(t.$slots,"default")],8,s))}};export{i as _};
