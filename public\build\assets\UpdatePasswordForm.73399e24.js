import{_ as f}from"./ActionMessage.eaf60617.js";import{_}from"./Button.de389ba7.js";import{_ as w}from"./FormSection.3de6ac09.js";import{_ as g}from"./Input.f95445aa.js";import{_ as b}from"./InputError.2a9befad.js";import{_ as h}from"./Label.a34a8f2d.js";import{_ as $}from"./plugin-vue_export-helper.21dcd24c.js";import{n as j,r as e,o as V,y as v,B as t,a as i,b as o,z as y,d as n}from"./app.5bf25e6f.js";const P=j({components:{JetActionMessage:f,JetButton:_,JetFormSection:w,JetInput:g,JetInputError:b,JetLabel:h},data(){return{form:this.$inertia.form({current_password:"",password:"",password_confirmation:""})}},methods:{updatePassword(){this.form.put(route("user-password.update"),{errorBag:"updatePassword",preserveScroll:!0,onSuccess:()=>this.form.reset(),onError:()=>{this.form.errors.password&&(this.form.reset("password","password_confirmation"),this.$refs.password.focus()),this.form.errors.current_password&&(this.form.reset("current_password"),this.$refs.current_password.focus())}})}}}),S=n(" Update Password "),B=n(" Ensure your account is using a long, random password to stay secure. "),C={class:"col-span-6 sm:col-span-4"},J={class:"col-span-6 sm:col-span-4"},k={class:"col-span-6 sm:col-span-4"},U=n(" Saved. "),N=n(" Save ");function E(s,r,z,F,I,A){const m=e("jet-label"),d=e("jet-input"),p=e("jet-input-error"),l=e("jet-action-message"),c=e("jet-button"),u=e("jet-form-section");return V(),v(u,{onSubmitted:s.updatePassword},{title:t(()=>[S]),description:t(()=>[B]),form:t(()=>[i("div",C,[o(m,{for:"current_password",value:"Current Password"}),o(d,{id:"current_password",type:"password",class:"mt-1 block w-full",modelValue:s.form.current_password,"onUpdate:modelValue":r[0]||(r[0]=a=>s.form.current_password=a),ref:"current_password",autocomplete:"current-password"},null,8,["modelValue"]),o(p,{message:s.form.errors.current_password,class:"mt-2"},null,8,["message"])]),i("div",J,[o(m,{for:"password",value:"New Password"}),o(d,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:s.form.password,"onUpdate:modelValue":r[1]||(r[1]=a=>s.form.password=a),ref:"password",autocomplete:"new-password"},null,8,["modelValue"]),o(p,{message:s.form.errors.password,class:"mt-2"},null,8,["message"])]),i("div",k,[o(m,{for:"password_confirmation",value:"Confirm Password"}),o(d,{id:"password_confirmation",type:"password",class:"mt-1 block w-full",modelValue:s.form.password_confirmation,"onUpdate:modelValue":r[2]||(r[2]=a=>s.form.password_confirmation=a),autocomplete:"new-password"},null,8,["modelValue"]),o(p,{message:s.form.errors.password_confirmation,class:"mt-2"},null,8,["message"])])]),actions:t(()=>[o(l,{on:s.form.recentlySuccessful,class:"mr-3"},{default:t(()=>[U]),_:1},8,["on"]),o(c,{class:y({"opacity-25":s.form.processing}),disabled:s.form.processing},{default:t(()=>[N]),_:1},8,["class","disabled"])]),_:1},8,["onSubmitted"])}var O=$(P,[["render",E]]);export{O as default};
