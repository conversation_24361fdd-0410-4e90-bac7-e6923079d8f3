import{n as p,r as e,o as a,c as r,b as o,B as n,F as h,H as g,L as y,C as b,a as i,z as v,E as k,d}from"./app.5bf25e6f.js";import{J as j}from"./AuthenticationCard.d2e642d8.js";import{J as C}from"./AuthenticationCardLogo.c737ac3b.js";import{_ as L}from"./Button.de389ba7.js";import{_ as w}from"./plugin-vue_export-helper.21dcd24c.js";const x=p({components:{Head:g,JetAuthenticationCard:j,JetAuthenticationCardLogo:C,JetButton:L,Link:y},props:{status:String},data(){return{form:this.$inertia.form()}},methods:{submit(){this.form.post(this.route("verification.send"))}},computed:{verificationLinkSent(){return this.status==="verification-link-sent"}}}),V=i("div",{class:"mb-4 text-sm text-gray-600"}," Thanks for signing up! Before getting started, could you verify your email address by clicking on the link we just emailed to you? If you didn't receive the email, we will gladly send you another. ",-1),B={key:0,class:"mb-4 font-medium text-sm text-green-600"},$={class:"mt-4 flex items-center justify-between"},E=d(" Resend Verification Email "),J=d("Log Out");function H(t,s,N,S,A,z){const c=e("Head"),m=e("jet-authentication-card-logo"),u=e("jet-button"),l=e("Link"),f=e("jet-authentication-card");return a(),r(h,null,[o(c,{title:"Email Verification"}),o(f,null,{logo:n(()=>[o(m)]),default:n(()=>[V,t.verificationLinkSent?(a(),r("div",B," A new verification link has been sent to the email address you provided during registration. ")):b("",!0),i("form",{onSubmit:s[0]||(s[0]=k((..._)=>t.submit&&t.submit(..._),["prevent"]))},[i("div",$,[o(u,{class:v({"opacity-25":t.form.processing}),disabled:t.form.processing},{default:n(()=>[E]),_:1},8,["class","disabled"]),o(l,{href:t.route("logout"),method:"post",as:"button",class:"underline text-sm text-gray-600 hover:text-gray-900"},{default:n(()=>[J]),_:1},8,["href"])])],32)]),_:1})],64)}var R=w(x,[["render",H]]);export{R as default};
