<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitsTable extends Migration
{

    public function up()
    {
        Schema::enableForeignKeyConstraints();
        Schema::create('visits', function (Blueprint $table) {
            $table->id();

            // $table->string('referred_by');
            $table->tinyInteger('referred_by');
            $table->foreignId('user_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('doctor_id')->nullable()->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('lab_id')->nullable()->constrained()->onDelete('restrict')->onUpdate('cascade');

            $table->bigInteger('tests_count')->nullable();
            $table->bigInteger('offers_count')->nullable();
            $table->bigInteger('tests_cost')->nullable();
            $table->bigInteger('discount')->nullable();
            $table->bigInteger('final_cost')->nullable();
            $table->bigInteger('paid_amount')->nullable();
            $table->bigInteger('remaining_amount')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('visits');
    }
}
