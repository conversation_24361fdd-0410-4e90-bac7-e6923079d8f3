<template>
    <div v-if="$page.props.flash.success && show" class="z-50 fixed bottom-3 left-3 py-2 px-4 border border-green-600 bg-green-500 text-green-50 rounded-lg p-5 divide-y divide-solid divide-green-600">
        <div class="flex justify-between">
            <h2 class="pb-3 text-lg font-bold">Success!</h2>
            <button type="button" class="group pb-3 text-2xl font-bold" @click="show = false">
                <svg class="block w-2 h-2 group-hover:fill-green-800 fill-white" xmlns="http://www.w3.org/2000/svg" width="235.908" height="235.908" viewBox="278.046 126.846 235.908 235.908"><path d="M506.784 134.017c-9.56-9.56-25.06-9.56-34.62 0L396 210.18l-76.164-76.164c-9.56-9.56-25.06-9.56-34.62 0-9.56 9.56-9.56 25.06 0 34.62L361.38 244.8l-76.164 76.165c-9.56 9.56-9.56 25.06 0 34.62 9.56 9.56 25.06 9.56 34.62 0L396 279.42l76.164 76.165c9.56 9.56 25.06 9.56 34.62 0 9.56-9.56 9.56-25.06 0-34.62L430.62 244.8l76.164-76.163c9.56-9.56 9.56-25.06 0-34.62z" /></svg>
            </button>
        </div>
        <div class="pt-3" style="text-shadow: 2px 1px 5px green">
            <p>{{ $page.props.flash.success }}</p>
        </div>
    </div>

    <div v-if="$page.props.flash.warning && show" class="z-50 fixed bottom-3 left-3 py-2 px-4 border border-yellow-600 bg-yellow-500 text-yellow-50 rounded-lg p-5 divide-y divide-solid divide-yellow-600">
        <div class="flex justify-between">
            <h2 class="pb-3 text-lg font-bold">Warning!</h2>
            <button type="button" class="group pb-3 text-2xl font-bold" @click="show = false">
                <svg class="block w-2 h-2 group-hover:fill-yellow-800 fill-white" xmlns="http://www.w3.org/2000/svg" width="235.908" height="235.908" viewBox="278.046 126.846 235.908 235.908"><path d="M506.784 134.017c-9.56-9.56-25.06-9.56-34.62 0L396 210.18l-76.164-76.164c-9.56-9.56-25.06-9.56-34.62 0-9.56 9.56-9.56 25.06 0 34.62L361.38 244.8l-76.164 76.165c-9.56 9.56-9.56 25.06 0 34.62 9.56 9.56 25.06 9.56 34.62 0L396 279.42l76.164 76.165c9.56 9.56 25.06 9.56 34.62 0 9.56-9.56 9.56-25.06 0-34.62L430.62 244.8l76.164-76.163c9.56-9.56 9.56-25.06 0-34.62z" /></svg>
            </button>
        </div>
        <div class="pt-3" style="text-shadow: 2px 1px 5px red">
            <p>{{ $page.props.flash.warning }}</p>
        </div>
    </div>

    <div v-if="($page.props.flash.error || Object.keys($page.props.errors).length > 0) && show" class="z-50 fixed bottom-3 left-3 py-2 px-4 border border-red-600 bg-red-500 text-red-50 rounded-lg p-5 divide-y divide-solid divide-red-600">
        <div class="flex justify-between">
            <h2 class="pb-3 text-lg font-bold">Error!</h2>
            <button type="button" class="group pb-3 text-2xl font-bold" @click="show = false">
                <svg class="block w-2 h-2 group-hover:fill-red-800 fill-white" xmlns="http://www.w3.org/2000/svg" width="235.908" height="235.908" viewBox="278.046 126.846 235.908 235.908"><path d="M506.784 134.017c-9.56-9.56-25.06-9.56-34.62 0L396 210.18l-76.164-76.164c-9.56-9.56-25.06-9.56-34.62 0-9.56 9.56-9.56 25.06 0 34.62L361.38 244.8l-76.164 76.165c-9.56 9.56-9.56 25.06 0 34.62 9.56 9.56 25.06 9.56 34.62 0L396 279.42l76.164 76.165c9.56 9.56 25.06 9.56 34.62 0 9.56-9.56 9.56-25.06 0-34.62L430.62 244.8l76.164-76.163c9.56-9.56 9.56-25.06 0-34.62z" /></svg>
            </button>
        </div>

        <div v-if="$page.props.flash.error" class="pt-3" style="text-shadow: 2px 1px 5px red">
            <p>{{ $page.props.flash.error }}</p>
        </div>

        <div v-else class="pt-3">
          <ul v-for="(error, index) in $page.props.errors" :key="index">
              <ol style="text-shadow: 2px 1px 5px red">{{error}}</ol>
          </ul>
        </div>
    </div>

</template>


<script>
export default {
  data() {
    return {
      show: true,
    }
  },
  watch: {
    '$page.props.flash': {
      handler() {
        this.show = true
      },
      deep: true,
    },
  },
}
</script>