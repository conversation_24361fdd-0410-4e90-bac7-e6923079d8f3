<?php

namespace App\Http\Controllers;

use App\Models\Group;
use App\Models\Test;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;

class GroupController extends Controller
{
    public function index()
    {
        return Inertia::render('Groups/Index', [
            'filters' => Request::all('search', 'trashed'),
            'groups' => Group::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($group) => [
                    'id' => $group->id,
                    'name' => $group->name,
                    'tests_count' => $group->tests_count,
                    'created_at' => $group->created_at->diffForHumans(),
                    'updated_at' => $group->updated_at->diffForHumans(),
                    'deleted_at' => $group->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Groups/Create', [
            'tests'    => Test::orderBy('id')->get()->map->only('id', 'short_name'),
        ]);
    }

    public function store()
    {
        \DB::transaction(function () {
            $group = Auth::user()->groups()->create(
                Request::validate(
                    [
                        'name'        => ['required', 'max:100', 'unique:groups'],
                        'tests_count' => ['required', 'digits_between:1,3', 'numeric', 'min:1'],
                    ],
                    [
                        'tests_count.required' => 'At least one test must be attached.!'
                    ]
                )
            );
            foreach (Request::input('groupTests') as $test) {
                $group->tests()->syncWithoutDetaching($test['id']);
            }
        });

        if (Request::input('createAnother')) {
            return Redirect::route('groups.create')->with('success', 'Group created.');
        } else {
            return Redirect::route('groups')->with('success', 'Group created.');
        }
    }

    public function edit(Group $group)
    {
        $group_tests =  $group->tests()->orderBy('id')->get()->map->only('id');

        return Inertia::render('Groups/Edit', [
            // 'tests'    => Test::orderBy('id')->get()->map->only('id', 'short_name'),
            'tests'    => Test::whereNotIn('id', $group_tests)->orderBy('id')->get()->map->only('id', 'short_name'),
            'group' => [
                'id' => $group->id,
                'name' => $group->name,
                'created_at' => $group->created_at,
                'tests_count' => $group->tests_count,
                'deleted_at' => $group->deleted_at,
                'groupTests' => $group->tests() ? $group->tests()->orderBy('short_name')->get()->map->only('id', 'short_name') : null,
            ],
        ]);
    }

    public function update(Group $group)
    {
        \DB::transaction(function () use ($group) {
            $group->update(
                Request::validate(
                    [
                        'name' => ['required', 'max:100', Rule::unique('groups')->ignore($group->id)],
                        'tests_count' => ['required', 'digits_between:1,3', 'numeric', 'min:1'],
                    ],
                    [
                        'tests_count.required' => 'At least one test must be attached.!'
                    ]
                )
            );
            foreach ($group->tests as $group_test) {
                if (!in_array($group_test->id, Request::input('groupTests'))) {
                    $group->tests()->detach($group_test->id);
                }
            }
            foreach (Request::input('groupTests') as $test) {
                $group->tests()->syncWithoutDetaching($test['id']);
            }
        });
        return Redirect::route('groups')->with('success', 'Group updated.');
    }

    public function destroy(Group $group)
    {
        $group->delete();
        return Redirect::route('groups')->with('success', 'Group deleted.');
    }

    public function restore(Group $group)
    {
        $group->restore();
        return Redirect::route('groups')->with('success', 'Group restored.');
    }
}
