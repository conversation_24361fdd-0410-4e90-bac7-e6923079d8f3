import{Q as g,o as b,c as h,b as a,u as e,a as s,B as u,d as l,t as n,E as x,z as _,F as C,H as y,L as v}from"./app.5bf25e6f.js";import{A as w}from"./AppLayout.14f8c8f6.js";import{_ as k}from"./TextInput.48e8e32c.js";import{L as c}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";const A={class:"p-4"},L={class:"mb-2 font-bold text-2xl"},V=s("span",{class:"text-indigo-400 font-medium"}," /",-1),B={class:"bg-white rounded-md shadow overflow-hidden max-w-xl"},S={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},N={layout:w},H=Object.assign(N,{__name:"Create",props:{locale:String},setup(p){let t=g({createAnother:!1,name:null}),i=()=>{t.createAnother=!0,m()},f=()=>{t.createAnother=!1,m()},m=()=>{t.post(route("results.store"),{preserveState:!0,onSuccess:()=>{t.reset()}})};return(r,o)=>(b(),h(C,null,[a(e(y),{title:"Create Result"}),s("div",A,[s("h2",L,[a(e(v),{class:"text-indigo-400 hover:text-indigo-600",href:r.route("results")},{default:u(()=>[l(n(r.trans("ResultsList")),1)]),_:1},8,["href"]),V,l(" "+n(r.trans("Create")),1)]),s("div",B,[s("form",{onSubmit:o[1]||(o[1]=x((...d)=>e(i)&&e(i)(...d),["prevent"]))},[s("div",{class:_(["p-8 -mr-6 -mb-8 flex flex-wrap",p.locale=="ar"?"rtl text-right":"ltr text-left"])},[a(k,{modelValue:e(t).name,"onUpdate:modelValue":o[0]||(o[0]=d=>e(t).name=d),error:e(t).errors.name,class:"pr-6 pb-8 w-full",direction:"ltr",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),s("div",S,[a(c,{loading:e(t).processing,class:"mr-4 flex px-3 py-2 btn-green",onClick:e(i)},{default:u(()=>[l(n(r.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),a(c,{loading:e(t).processing,class:"px-3 py-2 flex btn-green",onClick:e(f)},{default:u(()=>[l(n(r.trans("Create")+" "+r.trans("Result")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{H as default};
