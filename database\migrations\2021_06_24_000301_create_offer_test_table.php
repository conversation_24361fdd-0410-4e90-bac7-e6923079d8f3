<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOfferTestTable extends Migration
{
    public function up()
    {
        Schema::create('offer_test', function (Blueprint $table) {
            $table->id();
            $table->foreignId('offer_id')->constrained()->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('test_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('offer_test');
    }
}
