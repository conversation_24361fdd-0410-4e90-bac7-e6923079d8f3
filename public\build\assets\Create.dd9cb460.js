import{Q as B,v as k,o as a,c as i,b as d,u as e,a as o,B as p,d as h,t as n,E as P,z as C,K as E,S as z,C as c,F as _,D as w,y as F,H as O,L as $}from"./app.5bf25e6f.js";import{A as j}from"./AppLayout.14f8c8f6.js";import{_ as g}from"./TextInput.48e8e32c.js";import{D as R}from"./DropdownSearch.7a091d54.js";import{_ as V}from"./SelectInput.16ffd220.js";import{L as D}from"./LoadingButton.c8fb65b2.js";import{Q as M}from"./vue-quill.esm-bundler.e18f44dc.js";/* empty css                       */import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const Q={class:"p-2 sm:p-4"},H={class:"mb-2 flex justify-start max-w-xl"},I={class:"font-bold text-2xl"},K=o("span",{class:"text-indigo-400 font-medium"}," /",-1),G={class:"bg-white rounded-md shadow max-w-4xl"},J={value:"null",hidden:"",selected:"",disabled:""},W=["value"],X={class:"pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4"},Y={class:"form-label",for:"Sequence"},Z={key:0,class:"form-error"},ee={value:"null",hidden:"",selected:"",disabled:""},te=["value"],le={value:0},se={value:1},re={key:0,class:"ltr text-left pr-4 pb-0 w-full text-gray-700 font-semibold"},oe=o("span",{class:"p-1"},"Test Predefined Results:",-1),ae=[oe],ne={key:1,class:"ltr text-left w-full flex flex-wrap bg-indigo-300 py-1 px-1 mr-6 rounded h-auto mb-2"},ie=["onClick"],de={key:0,class:"px-2"},ue={key:1,class:"form-error w-full"},me=o("div",{class:"ltr text-left pr-4 pb-2 w-full text-green-600 font-semibold"},[o("span",{class:"p-1"},"Test Devices:")],-1),pe=["onClick"],ce={class:"w-full h-auto"},fe={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},_e={layout:j},Ce=Object.assign(_e,{__name:"Create",props:{locale:String,categories:Array,devices:Array,results:Array},setup(v){const b=v;var L=[["bold","italic","underline"],[{list:"ordered"},{list:"bullet"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}]];let t=B({createAnother:!1,short_name:null,full_name:null,category_id:null,default_device_id:null,result_type:0,sequence:null,lab_to_patient_price:null,lab_to_lab_price:null,testDevices:[],testResults:[]});const u=k(b.devices),m=k(b.results);let T=s=>{t.testDevices.push({id:s.id,name:s.name,normal_range:""}),u.splice(u.findIndex(r=>r.id===s.id),1)},q=(s,r,l)=>{t.default_device_id===s&&(t.default_device_id=null),u.push({id:s,name:r,normal_range:""}),t.testDevices.splice(l,1)},A=s=>{t.testResults.push({id:s.id,name:s.name}),m.splice(m.findIndex(r=>r.id===s.id),1)},U=(s,r,l)=>{m.push({id:s,name:r}),t.testResults.splice(l,1)},y=()=>{t.createAnother=!0,S()},N=()=>{t.createAnother=!1,S()},S=()=>{t.post(route("tests.store"),{preserveState:!0,onSuccess:()=>{t.reset(),u.splice(0,u.length),b.devices.forEach(s=>{u.push({id:s.id,name:s.name})}),m.splice(0,m.length),b.results.forEach(s=>{m.push({id:s.id,name:s.name})})}})};return(s,r)=>(a(),i(_,null,[d(e(O),{title:"Create Test"}),o("div",Q,[o("div",H,[o("h2",I,[d(e($),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("tests")},{default:p(()=>[h(n(s.trans("TestsList")),1)]),_:1},8,["href"]),K,h(" "+n(s.trans("Create")),1)])]),o("div",G,[o("form",{onSubmit:r[8]||(r[8]=P((...l)=>e(y)&&e(y)(...l),["prevent"]))},[o("div",{class:C(["p-6 -mr-6 -mb-5 flex flex-wrap",v.locale=="ar"?"rtl text-right":"ltr text-left"])},[d(g,{modelValue:e(t).short_name,"onUpdate:modelValue":r[0]||(r[0]=l=>e(t).short_name=l),error:e(t).errors.short_name,direction:"ltr",class:"pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"text",label:"ShortName",autofocus:!0},null,8,["modelValue","error"]),d(g,{modelValue:e(t).full_name,"onUpdate:modelValue":r[1]||(r[1]=l=>e(t).full_name=l),error:e(t).errors.full_name,direction:"ltr",class:"pr-6 pb-6 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"text",label:"FullName"},null,8,["modelValue","error"]),d(V,{modelValue:e(t).category_id,"onUpdate:modelValue":r[2]||(r[2]=l=>e(t).category_id=l),error:e(t).errors.category_id,direction:"ltr",class:"pr-6 pb-6 w-full text-right sm:w-1/2 md:w-1/3 lg:w-1/4",label:"Category"},{default:p(()=>[o("option",J,n(s.trans("SelectCategory")),1),(a(!0),i(_,null,w(v.categories,l=>(a(),i("option",{key:l.id,value:l.id},n(l.name),9,W))),128))]),_:1},8,["modelValue","error"]),o("div",X,[o("label",Y,n(s.trans("Sequence"))+":",1),E(o("input",{id:"Sequence","onUpdate:modelValue":r[3]||(r[3]=l=>e(t).sequence=l),placeholder:"Sequence",class:C(["form-input ltr",{error:e(t).errors.sequence}]),type:"number"},null,2),[[z,e(t).sequence]]),e(t).errors.sequence?(a(),i("div",Z,n(e(t).errors.sequence),1)):c("",!0)]),d(g,{modelValue:e(t).lab_to_patient_price,"onUpdate:modelValue":r[4]||(r[4]=l=>e(t).lab_to_patient_price=l),error:e(t).errors.lab_to_patient_price,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"number",label:"LabToPatientPrice"},null,8,["modelValue","error"]),d(g,{modelValue:e(t).lab_to_lab_price,"onUpdate:modelValue":r[5]||(r[5]=l=>e(t).lab_to_lab_price=l),error:e(t).errors.lab_to_lab_price,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",type:"number",label:"LabToLabPrice"},null,8,["modelValue","error"]),d(V,{modelValue:e(t).default_device_id,"onUpdate:modelValue":r[6]||(r[6]=l=>e(t).default_device_id=l),error:e(t).errors.default_device_id,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",label:"DefaultDevice"},{default:p(()=>[o("option",ee,n(s.trans("SelectDefaultDevice")),1),(a(!0),i(_,null,w(e(t).testDevices,l=>(a(),i("option",{key:l.id,value:l.id},n(l.name),9,te))),128))]),_:1},8,["modelValue","error"]),d(V,{modelValue:e(t).result_type,"onUpdate:modelValue":r[7]||(r[7]=l=>e(t).result_type=l),modelModifiers:{number:!0},error:e(t).errors.result_type,class:"pr-6 pb-4 w-full sm:w-1/2 md:w-1/3 lg:w-1/4",label:"ResultsType"},{default:p(()=>[o("option",le,n(s.trans("NumericResults")),1),o("option",se,n(s.trans("PredefinedResults")),1)]),_:1},8,["modelValue","error"]),e(t).result_type?(a(),i("div",re,ae)):c("",!0),e(t).result_type?(a(),i("div",ne,[(a(!0),i(_,null,w(e(t).testResults,(l,f)=>(a(),i("div",{title:"Remove Result",class:"pr-2 pt-1 pb-1 px-0",key:f},[o("span",{title:"Remove Result",onClick:x=>e(U)(l.id,l.name,f),class:"btn-remove pt-0.5"},n(l.name),9,ie)]))),128)),e(t).testResults.length<=0?(a(),i("span",de,"There are no attached results yet..!")):c("",!0),e(t).errors.testResults?(a(),i("div",ue,n(e(t).errors.testResults),1)):c("",!0)])):c("",!0),d(R,{class:"text-left z-40 pr-6 pb-2 w-full sm:w-1/2 lg:w-1/3",options:u,onSelect:e(T),CloseOnSelect:!1,fixedLabel:!0,label:"SelectDevice",searchBy:"name",direction:"ltr",placeholder:"Search for devices"},null,8,["options","onSelect"]),e(t).result_type?(a(),F(R,{key:2,class:"ltr text-left pr-6 pb-2 w-full sm:w-1/2 lg:w-1/3",options:m,onSelect:e(A),CloseOnSelect:!1,fixedLabel:!0,label:"SelectResult",searchBy:"name",direction:"ltr",placeholder:"Search for Predefined Results"},null,8,["options","onSelect"])):c("",!0),me,(a(!0),i(_,null,w(e(t).testDevices,(l,f)=>(a(),i("div",{class:"ltr text-left pr-6 pb-2 w-1/2",key:f},[o("span",{title:"Remove device",onClick:x=>e(q)(l.id,l.name,f),class:"btn-remove pt-0.5",for:"Normal"},n(l.name)+" Normal Range: ",9,pe),o("div",ce,[d(e(M),{class:"min-h-full",content:l.normal_range,"onUpdate:content":x=>l.normal_range=x,contentType:"html",toolbar:e(L),theme:"snow"},null,8,["content","onUpdate:content","toolbar"])])]))),128))],2),o("div",fe,[d(D,{loading:e(t).processing,class:"mr-4 px-3 py-2 flex btn-green",onClick:e(N)},{default:p(()=>[h(n(s.trans("Create")+" "+s.trans("Test")),1)]),_:1},8,["loading","onClick"]),d(D,{loading:e(t).processing,class:"flex px-3 py-2 btn-green",onClick:e(y)},{default:p(()=>[h(n(s.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{Ce as default};
