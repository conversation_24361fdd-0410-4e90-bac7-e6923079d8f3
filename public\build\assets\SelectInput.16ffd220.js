import{o as a,c as o,t as i,C as n,K as s,R as d,a as m,A as u,P as c,z as f}from"./app.5bf25e6f.js";const S=["for"],V=["id"],g={key:1,class:"form-error"},k={__name:"SelectInput",props:{id:String,modelValue:[String,Number,Boolean],label:String,direction:String,error:String},emits:["update:modelValue"],setup(e,{emit:b}){return(l,t)=>(a(),o("div",null,[e.label?(a(),o("label",{key:0,class:"form-label",for:e.id},i(l.trans(e.label))+":",9,S)):n("",!0),s(m("select",{id:e.id,ref:"input","onUpdate:modelValue":t[0]||(t[0]=r=>c(modelValue)?modelValue.value=r:null),onInput:t[1]||(t[1]=r=>l.$emit("update:modelValue",r.target.value)),class:f(["form-select",{rtl:e.direction==="rtl",ltr:e.direction==="ltr",error:e.error}])},[u(l.$slots,"default")],42,V),[[d,e.modelValue]]),e.error?(a(),o("div",g,i(e.error),1)):n("",!0)]))}};export{k as _};
