/* .btn-indigo {
    @apply px-6 py-3 rounded bg-indigo-600 text-white text-sm leading-4 font-bold whitespace-nowrap hover:bg-orange-400 focus:bg-orange-400;
} */

.btn-indigo {
    @apply bg-indigo-600 whitespace-nowrap border border-transparent rounded-md py-2 px-3 flex items-center justify-center text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
}

.btn-green {
    @apply bg-green-600 whitespace-nowrap border border-transparent rounded-md py-2 px-3 flex items-center justify-center text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:bg-green-500;
}


.btn-green2 {
    @apply bg-green-600 whitespace-nowrap border border-transparent rounded-md py-2 px-6 flex items-center justify-center text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500;
}

.btn-indigo2 {
    @apply bg-indigo-600 whitespace-nowrap border border-transparent rounded-md py-2 px-6 flex items-center justify-center text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500;
}

.btn-indigo3 {
    @apply whitespace-nowrap border border-transparent rounded-md py-1 px-4 flex items-center justify-center text-base font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-remove {
    @apply cursor-pointer px-2  bg-green-600 text-gray-50 shadow text-sm leading-3 font-semibold whitespace-nowrap hover:bg-red-400 focus:bg-red-400;
}



.btn-add {
    @apply px-2 text-white bg-indigo-800 text-sm hover:bg-green-400 focus:bg-green-400;
}

.btn-spinner,
.btn-spinner:after {
    border-radius: 50%;
    width: 1.5em;
    height: 1.5em;
}

.btn-spinner {
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 0.2em solid white;
    border-right: 0.2em solid white;
    border-bottom: 0.2em solid white;
    border-left: 0.2em solid transparent;
    transform: translateZ(0);
    animation: spinning 1s infinite linear;
}

@keyframes spinning {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}
