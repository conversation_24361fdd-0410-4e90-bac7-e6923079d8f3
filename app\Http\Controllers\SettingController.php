<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Http\Request as HttpRequest;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;

class SettingController extends Controller
{
    public function edit()
    {
        // dd($setting);
        //$setting = Setting::latest()->get();
        $setting = Setting::select('id', 'lab_name', 'header_photo_path', 'footer_photo_path')->first();
        // dd($setting->id);
        return Inertia::render('Settings/Edit', [
            'settings' => [
                'id' => $setting->id,
                'lab_name' => $setting->lab_name,
                // 'report_header' => $setting->report_header,
                // 'report_footer' => $setting->report_footer,

                'header_photo_path' => $setting->header_photo_path ? $setting->header_photo_path : null,
                'footer_photo_path' => $setting->footer_photo_path ? $setting->footer_photo_path : null,


                // 'header_photo_path_croped' => $setting->header_photo_path ? URL::route('image', ['path' => $setting->header_photo_path, 'w' => 60, 'h' => 60, 'fit' => 'crop']) : null,
                // 'header_photo_path' => $setting->header_photo_path ? URL::route('image', ['path' => $setting->header_photo_path]) : null,

                // 'footer_photo_path_croped' => $setting->photo_path ? URL::route('image', ['path' => $setting->footer_photo_path, 'w' => 60, 'h' => 60, 'fit' => 'crop']) : null,
                // 'footer_photo_path' => $setting->photo_path ? URL::route('image', ['path' => $setting->footer_photo_path]) : null,

                // 'logo_photo_path_croped' => $setting->logo_photo_path ? URL::route('image', ['path' => $setting->logo_photo_path, 'w' => 60, 'h' => 60, 'fit' => 'crop']) : null,
                // 'logo_photo_path' => $setting->logo_photo_path ? URL::route('image', ['path' => $setting->logo_photo_path]) : null,
            ],
        ]);
    }


    public function update22(HttpRequest $request, Setting $setting)
    {
        // $image_path = '';

        // if ($request->hasFile('image')) {
        //     $image_path = $request->file('image')->store('image', 'public');
        // }

        // if ($request->hasFile('image')) {
        //     $image_path = $request->file('image')->store('image', 'public');
        // }

        // $data = Image::create([
        //     'image' => $image_path,
        // ]);


        return Redirect::back()->with('success', 'Settings updated.');
    }

    public function update(Setting $setting)
    {
        Request::validate([
            // 'lab_name'          => ['required', 'max:50'],
            'header_photo_path' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,svg', 'max:2048'],
            'footer_photo_path' => ['nullable', 'image', 'mimes:jpeg,jpg,png,gif,svg', 'max:2048'],
        ]);

        // $setting->update(Request::only('lab_name'));

        if (Request::file('header_photo_path')) {
            $setting->update(['header_photo_path' => Request::file('header_photo_path')->store('settings', 'public')]);
        }

        if (Request::file('footer_photo_path')) {
            $setting->update(['footer_photo_path' => Request::file('footer_photo_path')->store('settings', 'public')]);
        }

        return Redirect::back()->with('success', 'Settings updated.');
    }
}
