<template>
<Head title="Edit SideLab" />
<div class="p-4">
    <div class="mb-2 flex justify-start max-w-xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('labs')">{{ trans('SideLabsList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
        {{ form.name }}
      </h2>
    </div>
    
    
    <div class="bg-white rounded-md shadow-sm overflow-hidden max-w-xl">
      <trashed-message v-if="lab.deleted_at" class="mb-6" @restore="restore">
      {{ trans('This') + ' ' +  trans('SideLab') + ' ' + trans('HasBeenDeleted')}}
    </trashed-message>
      <form @submit.prevent="update">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" direction="rtl" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Name" :autofocus="true" />
          <text-input v-model="form.owner" :error="form.errors.owner" direction="rtl" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Owner" />
          <text-input v-model="form.mobile" :error="form.errors.mobile" direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Mobile" /> 
        </div>
        <div class="px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
          <loading-button :loading="form.processing" class="btn-green" type="submit">{{trans('Update') + ' ' + trans('SideLab')}}</loading-button>
          <button v-if="!lab.deleted_at" class="text-red-600 hover:underline" tabindex="-1" type="button" @click="destroy">{{trans('Delete') + ' ' + trans('SideLab')}}</button>
        </div>
      </form>
    </div>

    <!-- <div class="shadow overflow-x-auto rounded-lg bg-white max-w-4xl mt-4">
      <table class="w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-indigo-600">
            <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('RelatedVisits') }}
            </th>
            
            <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('Edit')}}	
            </th>
          </tr>
        </thead>

        <tr v-for="visit in lab.visits" :key="visit.id" class="hover:bg-gray-100 focus-within:bg-gray-100">
          <td class="border-t p-0">
            <div class="px-6 py-2 flex items-center">
                {{ moment(visit.created_at).format('YYYY-MM-DD') }}
            </div>
          </td>
          
          <td class="border-t p-0">
            <div class="px-6 py-2">
              <icon name="cheveron-right" class="block w-6 h-6 fill-gray-400" />
            </div>
          </td>
        </tr>
        <tr v-if="lab.visits.length === 0">
          <td class="border-t p-0 px-6 py-2" colspan="4">No visits found.</td>
        </tr>
      </table>
    </div> -->
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'
import moment from 'moment'

const props = defineProps({
  locale: String,
  lab: Object,
});

let form = useForm({
    _method: 'put',
    name: props.lab.name,
    owner: props.lab.owner,
    mobile: props.lab.mobile,
});

let update = () => {
    form.post(route('labs.update', props.lab.id), {
    onSuccess: () => form.reset('name'),
    })
};

let destroy = () => {
    if (confirm('Are you sure you want to delete this SideLab?')) {
    Inertia.delete(route('labs.destroy', props.lab.id))
    }
};

let restore = () => {
    if (confirm('Are you sure you want to restore this SideLab?')) {
    Inertia.put(route('labs.restore', props.lab.id))
    }
};
</script>