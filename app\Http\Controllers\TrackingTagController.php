<?php

namespace App\Http\Controllers;

use App\Models\TrackingTag;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;

use Illuminate\Http\Request as HttpRequest;
use App\Models\Patient;
use Illuminate\Support\Facades\DB;

class TrackingTagController extends Controller
{
    public function index()
    {
        return Inertia::render('TrackingTags/Index', [
            'filters' => Request::all('search', 'trashed'),
            'tracking_tags' => TrackingTag::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($tracking_tag) => [
                    'id'         => $tracking_tag->id,
                    'name'       => $tracking_tag->name,
                    'created_at' => $tracking_tag->created_at->diffForHumans(),
                    'updated_at' => $tracking_tag->updated_at->diffForHumans(),
                    'deleted_at' => $tracking_tag->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('TrackingTags/Create');
    }

    public function store()
    {
        Auth::user()->tracking_tags()->create(
            Request::validate([
                'name' => ['required', 'min:6', 'max:55', 'unique:tracking_tags'],
            ])
        );
        if (Request::input('createAnother')) {
            return Redirect::back()->with('success', 'TrackingTag created.');
        } else {
            return Redirect::route('tracking_tags')->with('success', 'TrackingTag created.');
        }
    }

    public function edit(TrackingTag $tracking_tag)
    {
        return Inertia::render('TrackingTags/Edit', [
            'tracking_tag' => [
                'id' => $tracking_tag->id,
                'name' => $tracking_tag->name,
                'deleted_at' => $tracking_tag->deleted_at,
                'patients' => $tracking_tag->patients()->orderBy('created_at')->get()->map->only('id', 'name', 'created_at'),
            ],
        ]);
    }

    public function update(TrackingTag $tracking_tag)
    {
        $tracking_tag->update(
            Request::validate([
                'name' => ['required', 'min:6', 'max:55', Rule::unique('tracking_tags')->ignore($tracking_tag->id)],
            ])
        );
        return Redirect::route('tracking_tags')->with('success', 'TrackingTag updated.');
    }

    public function destroy(TrackingTag $tracking_tag)
    {
        $tracking_tag->delete();
        return Redirect::route('tracking_tags')->with('success', 'TrackingTag deleted.');
    }

    public function restore(TrackingTag $tracking_tag)
    {
        $tracking_tag->restore();
        return Redirect::back()->with('success', 'TrackingTag restored.');
    }


    public function sync_tracking_tag(HttpRequest  $request)
    {
        $tracking_tag_id  =  $request->input('tracking_tag_id');
        $patient_id       =  $request->input('patient_id');
        $patient          =  Patient::where('id', '=', $patient_id)->with('tracking_tags')->first();

        $patient->tracking_tags()->syncWithoutDetaching([$tracking_tag_id  => ['user_id' =>  Auth::user()->id,]]);
    }

    public function detach_tracking_tag(HttpRequest  $request)
    {
        $msg = true;
        $tracking_tag_id      =  $request->input('tracking_tag_id');
        $patient_id           =  $request->input('patient_id');
        $tracking_tag_patient = DB::table('patient_tracking_tag')->where('tracking_tag_id', '=', $tracking_tag_id)->where('patient_id', '=', $patient_id)->select('user_id')->first();

        if (Auth::user()->id !== $tracking_tag_patient->user_id) {
            abort(403, "Sorry, you can't detach tracking tag that doesn't belongs to you.!");
        } else {
            $patient =  Patient::where('id', '=', $patient_id)->with('tracking_tags')->first();
            $patient->tracking_tags()->detach($tracking_tag_id);
            return 'tracking tag detached.';
        }
    }

    public function get_tracking_tag_info(HttpRequest  $request)
    {
        $msg                  = true;
        $tracking_tag_id      =  $request->input('tracking_tag_id');
        $patient_id           =  $request->input('patient_id');
        $tracking_tag_patient = DB::table('patient_tracking_tag')->where('tracking_tag_id', '=', $tracking_tag_id)->where('patient_id', '=', $patient_id)->select('user_id', 'created_at')->first();
        //$user                 = User::find($tracking_tag_patient->user_id);

        $user = DB::table('users')->where('id', '=', $tracking_tag_patient->user_id)->select('name')->first();

        return 'Added by ' . $user->name . ' on ' . $tracking_tag_patient->created_at;
    }
}
