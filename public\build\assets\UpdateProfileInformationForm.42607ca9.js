import{n as $,r,o as p,y as f,B as s,c as y,a,b as o,K as h,M as _,Y as j,E as v,C as P,z as k,d as n}from"./app.5bf25e6f.js";import{_ as S}from"./Button.de389ba7.js";import{_ as C}from"./FormSection.3de6ac09.js";import{_ as I}from"./Input.f95445aa.js";import{_ as V}from"./InputError.2a9befad.js";import{_ as B}from"./Label.a34a8f2d.js";import{_ as N}from"./ActionMessage.eaf60617.js";import{_ as J}from"./SecondaryButton.fcd49457.js";import{_ as F}from"./plugin-vue_export-helper.21dcd24c.js";const U=$({components:{JetActionMessage:N,JetButton:S,JetFormSection:C,JetInput:I,JetInputError:V,<PERSON><PERSON>abel:B,JetSecondaryButton:J},props:["user"],data(){return{form:this.$inertia.form({_method:"PUT",name:this.user.name,email:this.user.email,photo:null}),photoPreview:null}},methods:{updateProfileInformation(){this.$refs.photo&&(this.form.photo=this.$refs.photo.files[0]),this.form.post(route("user-profile-information.update"),{errorBag:"updateProfileInformation",preserveScroll:!0,onSuccess:()=>this.clearPhotoFileInput()})},selectNewPhoto(){this.$refs.photo.click()},updatePhotoPreview(){const e=this.$refs.photo.files[0];if(!e)return;const t=new FileReader;t.onload=c=>{this.photoPreview=c.target.result},t.readAsDataURL(e)},deletePhoto(){this.$inertia.delete(route("current-user-photo.destroy"),{preserveScroll:!0,onSuccess:()=>{this.photoPreview=null,this.clearPhotoFileInput()}})},clearPhotoFileInput(){var e;(e=this.$refs.photo)!=null&&e.value&&(this.$refs.photo.value=null)}}}),E=n(" Profile Information "),z=n(" Update your account's profile information and email address. "),A={key:0,class:"col-span-6 sm:col-span-4"},M={class:"mt-2"},R=["src","alt"],D={class:"mt-2"},L=n(" Select A New Photo "),T=n(" Remove Photo "),K={class:"col-span-6 sm:col-span-4"},Y={class:"col-span-6 sm:col-span-4"},q=n(" Saved. "),G=n(" Save ");function H(e,t,c,O,Q,W){const i=r("jet-label"),u=r("jet-secondary-button"),m=r("jet-input-error"),d=r("jet-input"),b=r("jet-action-message"),g=r("jet-button"),w=r("jet-form-section");return p(),f(w,{onSubmitted:e.updateProfileInformation},{title:s(()=>[E]),description:s(()=>[z]),form:s(()=>[e.$page.props.jetstream.managesProfilePhotos?(p(),y("div",A,[a("input",{type:"file",class:"hidden",ref:"photo",onChange:t[0]||(t[0]=(...l)=>e.updatePhotoPreview&&e.updatePhotoPreview(...l))},null,544),o(i,{for:"photo",value:"Photo"}),h(a("div",M,[a("img",{src:e.user.profile_photo_url,alt:e.user.name,class:"rounded-full h-20 w-20 object-cover"},null,8,R)],512),[[_,!e.photoPreview]]),h(a("div",D,[a("span",{class:"block rounded-full w-20 h-20 bg-cover bg-no-repeat bg-center",style:j("background-image: url('"+e.photoPreview+"');")},null,4)],512),[[_,e.photoPreview]]),o(u,{class:"mt-2 mr-2",type:"button",onClick:v(e.selectNewPhoto,["prevent"])},{default:s(()=>[L]),_:1},8,["onClick"]),e.user.profile_photo_path?(p(),f(u,{key:0,type:"button",class:"mt-2",onClick:v(e.deletePhoto,["prevent"])},{default:s(()=>[T]),_:1},8,["onClick"])):P("",!0),o(m,{message:e.form.errors.photo,class:"mt-2"},null,8,["message"])])):P("",!0),a("div",K,[o(i,{for:"name",value:"Name"}),o(d,{id:"name",type:"text",class:"mt-1 block w-full",modelValue:e.form.name,"onUpdate:modelValue":t[1]||(t[1]=l=>e.form.name=l),autocomplete:"name"},null,8,["modelValue"]),o(m,{message:e.form.errors.name,class:"mt-2"},null,8,["message"])]),a("div",Y,[o(i,{for:"email",value:"Email"}),o(d,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:e.form.email,"onUpdate:modelValue":t[2]||(t[2]=l=>e.form.email=l)},null,8,["modelValue"]),o(m,{message:e.form.errors.email,class:"mt-2"},null,8,["message"])])]),actions:s(()=>[o(b,{on:e.form.recentlySuccessful,class:"mr-3"},{default:s(()=>[q]),_:1},8,["on"]),o(g,{class:k({"opacity-25":e.form.processing}),disabled:e.form.processing},{default:s(()=>[G]),_:1},8,["class","disabled"])]),_:1},8,["onSubmitted"])}var ne=F(U,[["render",H]]);export{ne as default};
