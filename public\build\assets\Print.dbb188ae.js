import{k as y,v as m,o as e,c as s,b as N,u as a,a as t,C as i,t as r,F as g,D as v,K as u,O as k,P as Y,B as z,z as V,S as F,H as S,d as U,Y as L,R as B,L as E}from"./app.5bf25e6f.js";import{A as I}from"./AppLayout.14f8c8f6.js";/* empty css                                                                   */import{h as P}from"./moment.9709ab41.js";/* empty css                       */import"./plugin-vue_export-helper.21dcd24c.js";const W={key:0,id:"section-to-print",class:"w-full tracking-wide bg-white font-Scheherazade"},O={class:"z-30"},q=["src"],K={class:"grid grid-cols-12 items-center justify-between mt-1 text-xl font-semibold text-gray-800 py-1 rtl px-6"},G={key:0,class:"col-span-6 text-right"},J={key:0},Q={key:1},X={key:2},Z={key:3},$={key:1,class:"col-span-6 text-right"},tt=t("span",null," \u0627\u0644\u062F\u0643\u062A\u0648\u0631 : ",-1),et=[tt],st={class:"col-span-6 text-left"},ot={class:"col-span-5 text-right"},nt={class:"col-span-2 text-right"},it={key:0},lt={key:1},at={key:2},rt={key:3},dt={key:4},ct={class:"col-span-5 text-left"},pt={class:"min-w-full z-20 px-6 mt-3",style:{height:"fit-content"}},ut={key:0,class:"w-full text-center content-block"},ht={class:"whitespace-nowrap text-xl mt-1 p-0"},_t={colspan:"4",class:"px-0 text-left rounded-sm text-pink-700"},xt={key:0,class:"bg-yellow-100 w-auto max-w-fit px-2 py-0"},yt={key:1,class:"bg-yellow-100 w-auto max-w-fit px-2 py-0"},mt={colspan:"4",class:"rounded-sm text-indigo-700",style:{"background-color":"#8EECF0","min-width":"100%"}},gt=t("th",{colspan:"1",class:"w-4/12 pr-1 pl-4 py-1 text-left font-semibold"},"Test",-1),ft=t("th",{colspan:"1",class:"w-2/12 px-1 py-1 text-left font-semibold"},"Result",-1),bt={key:0,colspan:"1",class:"w-2/12 px-1 py-1 text-left font-semibold"},wt=t("span",null,null,-1),vt=[wt],kt=t("th",{colspan:"1",class:"w-6/12 px-1 py-1 text-left font-semibold"}," Normal Range",-1),Vt=t("span",{class:""},"\u27A4",-1),Tt={key:1,colspan:"1",class:"w-2/12 px-1 py-3 text-left text-xl font-semibold"},Mt={key:2,colspan:"1",class:"w-2/12 px-1 py-3 text-left text-xl font-semibold"},Yt={key:0,class:"border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap"},Pt={key:3,colspan:"1",class:"w-6/12 px-1 py-1 text-left text-sm font-semibold ql-editor"},Dt=["innerHTML"],Ut=t("hr",{class:"border border-indigo-300"},null,-1),Ct=["innerHTML"],Nt={class:"z-10"},Ft=["src"],Lt={key:1,class:"px-2 py-2 max-w-4xl ml-4 mt-4 tracking-wide bg-white"},Rt={class:"flex items-center justify-between pb-1 w-full"},At={key:0,class:"bg-purple-500 text-gray-50 px-1 py-px rounded-sm text-lg font-normal"},Ht={key:1,class:"bg-red-500 text-yellow-200 px-1 py-0.5 rounded-sm"},jt={key:2,class:"text-orange-500 px-2 py-0.5"},zt={class:"bg-purple-500 text-gray-50 px-1 py-px rounded-sm text-lg font-normal"},St={class:"bg-green-500 w-full p-px"},Bt={class:"min-w-max flex text-xl justify-start items-center text-orange-600"},Et=["onUpdate:modelValue","checked"],It={class:"p-2"},Wt=["onUpdate:modelValue"],Ot=t("option",{value:null},null,-1),qt=["value"],Kt={key:0,class:"w-full"},Gt={class:"text-xl bg-cyan-300 rounded-sm text-black font-extrabold"},Jt=t("th",{class:"w-4/12 px-1 py-1 text-left font-semibold"},"Test",-1),Qt=t("th",{class:"w-2/12 px-1 py-1 text-left font-semibold"},"Result",-1),Xt=t("th",{class:"w-36 px-1 py-1 text-left font-semibold"},"Color",-1),Zt={key:0,class:"w-2/12 px-1 py-1 text-left font-semibold"},$t=t("span",null,null,-1),te=[$t],ee=t("th",{class:"w-6/12 px-1 py-1 text-left font-semibold"}," Normal Range",-1),se=["onUpdate:modelValue","checked"],oe=t("span",{class:"text-xl"},"\u27A4",-1),ne={colspan:"1",class:"w-2/12 px-1 py-3 text-left text-xl font-semibold"},ie={class:"w-1/12 p-0.5 text-left text-sm font-semibold"},le=["onUpdate:modelValue"],ae={key:0,colspan:"1",class:"w-2/12 px-1 py-3 text-left text-xl font-semibold"},re={key:0,class:"border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap"},de={colspan:"1",class:"w-6/12 px-1 py-1 text-left text-sm font-semibold ql-editor"},ce=["innerHTML"],pe={class:"flex justify-start items-center space-x-3 px-4 py-2 font-Roboto bg-green-500"},ue={for:"flag",class:"text-sm text-gray-900"},he={for:"Notes",class:"text-sm text-gray-900"},_e={for:"TextWrap",class:"text-sm text-gray-900"},xe=U(" Edit Visit Tests "),ye={key:0,class:"flex justify-start items-center space-x-4 px-4 py-2 font-Roboto bg-indigo-500"},me={layout:I},Te=Object.assign(me,{__name:"Print",props:{categories:Array,devices:Array,visit:Object,visitTests:Array,header_photo_path:String,footer_photo_path:String,flags:Array},setup(l){const f=l;let b=y("");const R=m(f.visit),A=m(f.devices),H=m(f.visitTests),j=m(f.categories),T=m(f.flags);let M=y(!1),p=y(!1),_=y(!1),x=y(!1);y("");let D=m([]);(()=>{j.forEach(c=>{let d=H.filter(o=>o.category_id===c.id);d.length>0&&D.push({id:c.id,category_name:c.name,category_tests:d,printable:!0,device:c.default_device})})})();let C=()=>{M.value=!0,setTimeout(function(){window.print(),M.value=!1},100)};return(c,d)=>(e(),s(g,null,[N(a(S),{title:"Print Result"}),a(M)?(e(),s("div",W,[t("header",O,[t("img",{src:"/storage/"+l.header_photo_path,class:"w-full h-full border-b-2 border-blue-600"},null,8,q),t("div",K,[l.visit.doctor?(e(),s("div",G,[l.visit.doctor.gender==1?(e(),s("span",J," \u0627\u0644\u062F\u0643\u062A\u0648\u0631 : ")):i("",!0),l.visit.doctor.gender==2?(e(),s("span",Q," \u0627\u0644\u062F\u0643\u062A\u0648\u0631\u0629 : ")):i("",!0),t("span",null,r(l.visit.doctor.name),1),l.visit.doctor.gender==1?(e(),s("span",X," \u0627\u0644\u0645\u062D\u062A\u0631\u0645 ")):i("",!0),l.visit.doctor.gender==2?(e(),s("span",Z," \u0627\u0644\u0645\u062D\u062A\u0631\u0645\u0629 ")):i("",!0)])):(e(),s("div",$,et)),t("div",st," Visit Date: "+r(a(P)(l.visit.created_at).format("YYYY/MM/DD h:mm a")),1),t("div",ot," \u0627\u0633\u0645 \u0627\u0644\u0645\u0631\u0627\u062C\u0639 : "+r(l.visit.patient.name),1),t("div",nt,[l.visit.patient.age>0?(e(),s("span",it,"\u0627\u0644\u0639\u0645\u0631 : ")):i("",!0),l.visit.patient.age>0?(e(),s("span",lt,r(l.visit.patient.age),1)):i("",!0),l.visit.patient.age>0&&l.visit.patient.age_type==3?(e(),s("span",at," \u0633\u0646\u0629 ")):i("",!0),l.visit.patient.age>0&&l.visit.patient.age_type==2?(e(),s("span",rt," \u0634\u0647\u0631 ")):i("",!0),l.visit.patient.age>0&&l.visit.patient.age_type==1?(e(),s("span",dt," \u064A\u0648\u0645 ")):i("",!0)]),t("div",ct," Print Date: "+r(a(P)().format("YYYY/MM/DD h:mm a")),1)])]),t("div",pt,[(e(!0),s(g,null,v(a(D),o=>(e(),s("div",{class:"text-xl m-0",key:o.category_name},[o.printable?(e(),s("table",ut,[t("tr",ht,[t("th",_t,[o.device?(e(),s("div",xt,r(o.category_name)+" By - "+r(o.device),1)):(e(),s("div",yt,r(o.category_name),1))])]),t("tr",mt,[gt,ft,a(p)?(e(),s("th",bt,vt)):i("",!0),kt]),(e(!0),s(g,null,v(o.category_tests,(n,w)=>(e(),s("tr",{colspan:"4",class:V(["content-block text-2xl",{"border-b border-indigo-200":w!=o.category_tests.length-1}]),key:w},[n.isPrintable?(e(),s("td",{key:0,colspan:"1",class:V(["w-4/12 min-w-fit px-1 py-1 text-left text-xl",a(x)?"":"whitespace-nowrap"])},[Vt,U(" "+r(n.short_name),1)],2)):i("",!0),n.isPrintable?(e(),s("td",Tt,[n.result?(e(),s("span",{key:0,class:"border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap",style:L({color:`${n.color}`})},r(n.result),5)):i("",!0)])):i("",!0),a(p)&&n.isPrintable?(e(),s("td",Mt,[n.flag_id?(e(),s("span",Yt,r(T[T.findIndex(h=>h.id===n.flag_id)].name),1)):i("",!0)])):i("",!0),n.isPrintable?(e(),s("td",Pt,[n.normal_range?(e(),s("span",{key:0,innerHTML:n.normal_range.pivot.normal_range},null,8,Dt)):i("",!0)])):i("",!0)],2))),128))])):i("",!0)]))),128)),Ut,a(_)?(e(),s("p",{key:0,class:"w-full font-Amiri py-1 px-3 bg-orange-100 mt-2 text-gray-900 shadow-md",innerHTML:a(b)},null,8,Ct)):i("",!0)]),t("footer",Nt,[t("img",{src:"/storage/"+l.footer_photo_path,class:""},null,8,Ft)])])):i("",!0),a(M)?i("",!0):(e(),s("div",Lt,[t("div",Rt,[l.visit.isItTodysVisit?(e(),s("span",At,"Visit date: "+r(a(P)(l.visit.created_at).format("YYYY/MM/DD")),1)):i("",!0),l.visit.isItTodysVisit?i("",!0):(e(),s("span",Ht,"Visit date: "+r(a(P)(l.visit.created_at).format("YYYY/MM/DD")),1)),l.visit.isItTodysVisit?i("",!0):(e(),s("span",jt,r(c.trans("NotTodysVisit")),1)),t("h1",zt,r(l.visit.patient.name),1)]),(e(!0),s(g,null,v(a(D),o=>(e(),s("table",{class:"min-w-full w-full border-blue-400 h-full mt-2 overflow-hidden",key:o.category_name,style:{width:"100%"}},[t("tr",St,[t("div",Bt,[u(t("input",{class:"p-2 mx-2","onUpdate:modelValue":n=>o.printable=n,checked:o.printable,type:"checkbox"},null,8,Et),[[k,o.printable]]),t("span",It,r(o.category_name)+" By - ",1),u(t("select",{class:"px-2 py-0.5","onUpdate:modelValue":n=>o.device=n},[Ot,(e(!0),s(g,null,v(A,n=>(e(),s("option",{key:n.name,value:n.name},r(n.name),9,qt))),128))],8,Wt),[[B,o.device]])])]),o.printable?(e(),s("div",Kt,[t("tr",Gt,[Jt,Qt,Xt,a(p)?(e(),s("th",Zt,te)):i("",!0),ee]),(e(!0),s(g,null,v(o.category_tests,(n,w)=>(e(),s("tr",{class:V(["content-block text-2xl",{"border-b border-indigo-200":w!=o.category_tests.length-1}]),key:w,style:{"min-width":"56rem/* 896px */"}},[t("td",{colspan:"1",class:V(["w-4/12 px-1 py-1 text-left text-2xl font-semibold",a(x)?"":"whitespace-nowrap"])},[u(t("input",{class:"p-2 mx-2","onUpdate:modelValue":h=>n.isPrintable=h,checked:n.isPrintable,type:"checkbox"},null,8,se),[[k,n.isPrintable]]),oe,U(" "+r(n.short_name),1)],2),t("td",ne,[n.result?(e(),s("span",{key:0,class:"border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap",style:L({color:`${n.color}`})},r(n.result),5)):i("",!0)]),t("td",ie,[u(t("input",{type:"color","onUpdate:modelValue":h=>n.color=h},null,8,le),[[F,n.color]])]),a(p)?(e(),s("td",ae,[n.flag_id?(e(),s("span",re,r(T[T.findIndex(h=>h.id===n.flag_id)].name),1)):i("",!0)])):i("",!0),t("td",de,[n.normal_range?(e(),s("span",{key:0,innerHTML:n.normal_range.pivot.normal_range},null,8,ce)):i("",!0)])],2))),128))])):i("",!0)]))),128)),t("div",pe,[t("button",{class:"btn-indigo cursor-pointer",onClick:d[0]||(d[0]=(...o)=>a(C)&&a(C)(...o))},r(c.trans("Print")),1),u(t("input",{id:"flag","onUpdate:modelValue":d[1]||(d[1]=o=>Y(p)?p.value=o:p=o),type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[k,a(p)]]),t("label",ue,r(c.trans("Flags")),1),u(t("input",{id:"Notes","onUpdate:modelValue":d[2]||(d[2]=o=>Y(_)?_.value=o:_=o),type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[k,a(_)]]),t("label",he,r(c.trans("Notes")),1),u(t("input",{id:"TextWrap","onUpdate:modelValue":d[3]||(d[3]=o=>Y(x)?x.value=o:x=o),type:"checkbox",class:"text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[k,a(x)]]),t("label",_e,r(c.trans("AllowTextWrap")),1),N(a(E),{class:V(["btn-indigo",{"text-red-600":R.deleted_at}]),href:c.route("patientsVisits.editFull",l.visit.id),tabindex:"-1"},{default:z(()=>[xe]),_:1},8,["class","href"])]),a(_)?(e(),s("div",ye,[u(t("input",{class:"w-full",type:"text","onUpdate:modelValue":d[4]||(d[4]=o=>Y(b)?b.value=o:b=o)},null,512),[[F,a(b)]])])):i("",!0)]))],64))}});export{Te as default};
