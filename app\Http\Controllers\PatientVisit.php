<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request as HttpRequest;
use App\Models\Visit;
use App\Models\Test;
use App\Models\Patient;
use App\Models\Device;
use App\Models\Category;
use App\Models\Flag;
use App\Models\Setting;
use App\Models\Group;
use App\Models\Offer;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class PatientVisit extends Controller
{
    public $visit;
    public function print(Visit $visit)
    {
        $today = (date('Y-m-d') . ' 00:00:00');
        $setting = Setting::select('header_photo_path', 'footer_photo_path')->first();
        return Inertia::render('PatientsVisits/Print', [
            'devices'             => Device::select('name')->get(),
            'categories'          => Category::select('id', 'name', 'default_device')->get(),
            'flags'               => Flag::select('id', 'name')->get(),
            'header_photo_path'   => $setting->header_photo_path ? $setting->header_photo_path : null,
            'footer_photo_path'   => $setting->footer_photo_path ? $setting->footer_photo_path : null,
            'visit' => [
                'id'              => $visit->id,
                // 'created_at'      => $visit->created_at,
                'created_at'      => $visit->created_at,
                'patient'         => $visit->patient()->select('id', 'name', 'age', 'age_type', 'gender')->first(),
                'doctor'          => $visit->doctor()->select('name', 'gender')->first(),
                'isItTodysVisit'   => $visit->created_at >= $today ? true : false,
            ],
            'visitTests' => $visit->tests()->orderBy('category_id')->orderBy('sequence')->with('devices')->get()
                ->map(fn ($test) => [
                    'id'           => $test->id,
                    'short_name'   => $test->short_name,
                    'category_id'  => $test->category_id,
                    'result'       => $test->pivot->result,
                    'flag_id'      => $test->pivot->flag_id,
                    'color'                => '#000000',
                    'isPrintable'  => true,
                    'normal_range' => ($test->pivot->device_id && $test->devices()->where('device_id', $test->pivot->device_id)->exists()) ?
                    $test->devices()->where('device_id', $test->pivot->device_id)->first()->only('pivot') : null,
                    
                    //'normal_range' => $test->pivot->device_id ? $test->devices()->where('device_id', $test->pivot->device_id)->first()->only('pivot') : null,
                    
                ])
        ]);
    }

    public function create_full()
    {
        return Inertia::render('PatientsVisits/CreateFull', [
            'doctors'       => DB::table('doctors')->select('id', 'name')->whereNull('deleted_at')->get(),
            'sidelabs'      => DB::table('labs')->select('id', 'name')->whereNull('deleted_at')->get(),
            'flags'         => DB::table('flags')->select('id', 'name')->whereNull('deleted_at')->get(),
            'groups'        => Group::select('id', 'name')->with('tests:id')->orderBy('id')->get(),

            // 'groups'        => Group::with(['tests' => function ($query) {
            //     $query->select('tests.id as id');
            // }])->get()->map->only('id'),
            //'offers'        => Offer::select('id', 'name', 'price')->with('tests')->with(['tests.results', 'tests.devices'])->orderBy('id')->get(),
            'offers'        => Offer::select('id', 'name', 'price')->with('tests:id')->orderBy('id')->get(),
            // 'tests'         => Test::with('devices')->with('results')->orderBy('sequence')->get(),
            'tests'         => Test::with('devices:name')->with('results:name')->orderBy('sequence', 'desc')->get()
                // 'tests'         => Test::withCount('visits')->with('devices:name')->with('results:name')->orderBy('visits_count', 'desc')->get()
                ->map(fn ($test) => [
                    'id'                   => $test->id,
                    'short_name'           => $test->short_name,
                    'lab_to_patient_price' => $test->lab_to_patient_price,
                    'lab_to_lab_price'     => $test->lab_to_lab_price,
                    'device_id'            => $test->default_device_id,
                    'devices'              => $test->devices,
                    'results'              => $test->results,
                    'result_type'          => $test->result_type,
                    'value'                => null,
                    'flag_id'              => null,
                ]),
        ]);
    }

    public function storeFull(HttpRequest $request)
    {
        Request::validate([
            'name'             => ['required', 'min:12', 'max:45', 'unique:patients'],
            'gender'           => ['nullable'],
            'age'              => ['nullable', 'digits_between:0,3'],
            'age_type'         => ['nullable', 'digits_between:0,3'],
            'mobile'           => ['nullable', 'min:11', 'max:11'],
            'doctor_id'        => ['nullable', Rule::exists('doctors', 'id')],
            'lab_id'           => ['nullable', Rule::exists('labs', 'id')],
            'referred_by'      => ['required', 'digits_between:1,3'],
            'tests_cost'       => ['required'],
            'discount'         => ['digits_between:0,6', 'lte:final_cost',
                function ($attribute, $value, $fail) {
                    if ($value % 250 != 0) {
                        $fail('Discount must be divisible by 250.');
                    }
                },
            ],
            'final_cost'       => ['required'],
            'paid_amount'      => ['required', 'digits_between:1,6',
                function ($attribute, $value, $fail) {
                    if ($value % 250 != 0) {
                        $fail('Paid amount must be divisible by 250.');
                    }
                },
            ],
            'remaining_amount' => ['required'],
        ]);

        Request::validate(
            [
                'visitTests.*.value'     => ['required'],
            ],
        );

        if (!Request::input('visitOffers')) {
            Request::validate(
                [
                    'visitTests'     => ['required'],
                ],
                [
                    'visitTests.required' => 'At least one  test must be added.!'
                ]
            );
        }
        

        if ($request->referred_by === 1) {
            Request::validate(
                ['lab_id'           => ['required', Rule::exists('labs', 'id')],],
                ['lab_id.required' => 'The SideLab field is required.!']
            );
        }
        if ($request->referred_by === 2) {
            Request::validate(
                ['doctor_id'           => ['required', Rule::exists('doctors', 'id')],],
                ['doctor_id.required' => 'The doctor field is required.!']
            );
        }

        DB::transaction(function ()  use ($request) {
            $patient = Auth::user()->patients()->create([
                'name'      => $request->name,
                'age'       => $request->age,
                'age_type'  => $request->age_type,
                'gender'    => $request->gender,
                'mobile'    => $request->mobile,
            ]);
            $visit = Auth::user()->visits()->create([
                'patient_id'       => $patient->id,
                'doctor_id'        => $request->doctor_id,
                'lab_id'           => $request->lab_id,
                'referred_by'      => $request->referred_by,
                'tests_count'      => count(Request::input('visitTests')),
                'offers_count'     => count(Request::input('visitOffers')),
                'tests_cost'       => $request->tests_cost,
                'discount'         => $request->discount,
                'final_cost'       => $request->final_cost,
                'paid_amount'      => $request->paid_amount,
                'remaining_amount' => $request->remaining_amount,
            ]);
            foreach (Request::input('visitTests') as $test) {
                $visit->tests()->syncWithoutDetaching([$test['id']  => [
                    'user_id'     =>  Auth::user()->id,
                    'device_id'   =>  $test['device_id'] ?? null,
                    'flag_id'     =>  $test['flag_id'] ?? null,
                    'result'      =>  $test['value'],
                    'value'       =>  is_numeric($test['value']) ? $test['value'] : null,
                    'created_at'  =>  now()->toDateString(),
                    'updated_at'  =>  now()->toDateString(),
                ]]);
            }

            foreach (Request::input('visitOffers') as $offer) {
                $visit->offers()->syncWithoutDetaching([$offer['id']  => [
                    'user_id'     =>  Auth::user()->id,
                    'created_at'  =>  now()->toDateString(),
                    'updated_at'  =>  now()->toDateString(),
                ]]);

                foreach ($offer['tests'] as $test) {
                    $visit->tests()->syncWithoutDetaching([$test['id']  => [
                        'user_id'     =>  Auth::user()->id,
                        'device_id'   =>  $test['device_id'] ?? null,
                        'flag_id'     =>  $test['flag_id'] ?? null,
                        'result'      =>  $test['value'],
                        'value'       =>  is_numeric($test['value']) ? $test['value'] : null,
                        'created_at'  =>  now()->toDateString(),
                        'updated_at'  =>  now()->toDateString(),
                        'offer_id'    =>  $offer['id'],
                    ]]);
                }
            }
            if (Request::input('calcLDLAndVLDL')){
                // $vldl = $tri/5;
                // $ldl  = $cholestrol - ($hdl + $vldl);
                $cholestrol_id  = 37;
                $tri_id         = 38;
                $hdl_id         = 39;
                $ldl_id         = 40;
                $vldl_id        = 41;

                $tri_value         = 0;
                $cholestrol_value  = 0;
                $hdl_value         = 0;
                $ldl_value         = 0;
                $vldl_value        = 0;

                foreach (Request::input('visitTests') as $test) {
                    if ($test['id'] === $tri_id) {
                        $tri_value = $test['value'];
                    }
                    if ($test['id'] === $cholestrol_id) {
                        $cholestrol_value = $test['value'];
                    }
                    if ($test['id'] === $hdl_id) {
                        $hdl_value = $test['value'];
                    }

                    if ($test['id'] === $ldl_id) {
                        $ldl_value = 1;
                    }
                    if ($test['id'] === $vldl_id) {
                        $vldl_value = 1;
                    }

                    // calc vldl_value
                    if ($tri_value > 0 && $vldl_value > 0) {
                        $vldl_value = $tri_value / 5;
                        $visit->tests()->syncWithoutDetaching([$vldl_id  => [
                            'user_id'     =>  Auth::user()->id,
                            'device_id'   =>  $test['device_id'] ?? null,
                            'flag_id'     =>  $test['flag_id'] ?? null,
                            'result'      =>  $vldl_value,
                            'value'       =>  is_numeric($vldl_value) ? $vldl_value : null,
                            'created_at'  =>  now()->toDateString(),
                            'updated_at'  =>  now()->toDateString(),
                        ]]);
                    }

                    // calc ldl_value
                    if ($ldl_value > 0 && $cholestrol_value > 0 && $hdl_value > 0 && $vldl_value > 0) {
                        $ldl_value = $cholestrol_value - ($hdl_value + $vldl_value);
                        $visit->tests()->syncWithoutDetaching([$ldl_id  => [
                            'user_id'     =>  Auth::user()->id,
                            'device_id'   =>  $test['device_id'] ?? null,
                            'flag_id'     =>  $test['flag_id'] ?? null,
                            'result'      =>  $ldl_value,
                            'value'       =>  is_numeric($ldl_value) ? $ldl_value : null,
                            'created_at'  =>  now()->toDateString(),
                            'updated_at'  =>  now()->toDateString(),
                        ]]);
                    }
                }
            }
            $GLOBALS['visit'] = $visit;
        });
        return redirect()->route('patientsVisits.print', [$GLOBALS['visit']]);
    }

    public function editFull(Visit $visit)
    {

        // here we should add can authorizition, if visit belongs to user then he can edit discount and others, if not he can edit just results
        return Inertia::render('PatientsVisits/EditFull', [

            'doctors'       => DB::table('doctors')->select('id', 'name')->whereNull('deleted_at')->get(),
            'sidelabs'      => DB::table('labs')->select('id', 'name')->whereNull('deleted_at')->get(),
            'flags'         => DB::table('flags')->select('id', 'name')->whereNull('deleted_at')->get(),
            'groups'        => Group::select('id', 'name')->with('tests:id')->orderBy('id')->get(),
            'offers'        => Offer::select('id', 'name', 'price')->with('tests:id')->orderBy('id')->get(),

            'visitTests'    => DB::table('test_visit')->where('visit_id', '=', $visit->id)->select('test_id', 'device_id', 'offer_id', 'result', 'flag_id')->get(),
            'visitOffers'   => DB::table('offer_visit')->where('visit_id', '=', $visit->id)->select('offer_id')->get(),

            // 'tests'         => Test::withCount('visits')->with('devices:name')->with('results:name')->orderBy('visits_count', 'desc')->get()
            'tests'         => Test::with('devices:name')->with('results:name')->orderBy('sequence', 'desc')->get()
                ->map(fn ($test) => [
                    'id'                   => $test->id,
                    'short_name'           => $test->short_name,
                    'lab_to_patient_price' => $test->lab_to_patient_price,
                    'lab_to_lab_price'     => $test->lab_to_lab_price,
                    'device_id'            => $test->default_device_id,
                    'devices'              => $test->devices,
                    'results'              => $test->results,
                    'result_type'          => $test->result_type,
                    'value'                => null,
                    'flag_id'              => null,
                ]),

            'visit' => [
                'id'               => $visit->id,
                'patient'          => $visit->patient()->first()->only('mobile', 'name', 'gender', 'age', 'age_type'),
                'doctor_id'        => $visit->doctor_id,
                'lab_id'           => $visit->lab_id,
                'referred_by'      => $visit->referred_by,

                'tests_count'      => $visit->tests_count,
                'tests_cost'       => $visit->tests_cost,
                'discount'         => $visit->discount,
                'final_cost'       => $visit->final_cost,
                'paid_amount'      => $visit->paid_amount,
                'remaining_amount' => $visit->remaining_amount,
                'deleted_at'       => $visit->deleted_at
            ],
        ]);
    }

    public function updateFull(Visit $visit, HttpRequest $request)
    {
        if (Auth::user()->role !== "Administrator") {
            if (Auth::user()->id !== $visit->user_id) {
                return Redirect::back()->with('error', "Sorry, you can't update a visit that doesn't belong to you.!");
            }
        }
        Request::validate([
            'name'             => ['required', 'min:12', 'max:45', Rule::unique('patients')->ignore($visit->patient->id)],
            'gender'           => ['nullable'],
            'age'              => ['nullable', 'digits_between:0,3'],
            'age_type'         => ['nullable', 'digits_between:0,3'],
            'mobile'           => ['nullable', 'min:11', 'max:11'],
            'doctor_id'        => ['nullable', Rule::exists('doctors', 'id')],
            'lab_id'           => ['nullable', Rule::exists('labs', 'id')],
            'referred_by'      => ['required', 'digits_between:1,3'],
            'tests_cost'       => ['required'],
            // 'discount'         => ['required'],
            'discount'         => ['digits_between:1,6', 'lte:tests_cost',
                function ($attribute, $value, $fail)  {
                    if ($value % 250 != 0) {
                        $fail('Discount must be divisible by 250.');
                    }
                },
            ],
            'final_cost'       => ['required'],
            // 'paid_amount'      => ['required'],
            'paid_amount'      => ['required', 'digits_between:1,6',
                function ($attribute, $value, $fail) {
                    if ($value % 250 != 0) {
                        $fail('Paid amount must be divisible by 250.');
                    }
                },
            ],
            'remaining_amount' => ['required'],
        ]);

        Request::validate(
            [
                'visitTests.*.value'     => ['required'],
            ],
        );

        if (!Request::input('visitOffers')) {
            Request::validate(
                [
                    'visitTests'     => ['required'],
                ],
                [
                    'visitTests.required' => 'At least one  test must be added.!'
                ]
            );
        }

        if ($request->referred_by === 1) {
            Request::validate(
                ['lab_id'          => ['required', Rule::exists('labs', 'id')],],
                ['lab_id.required' => 'The SideLab field is required.!']
            );
        }
        if ($request->referred_by === 2) {
            Request::validate(
                ['doctor_id'          => ['required', Rule::exists('doctors', 'id')],],
                ['doctor_id.required' => 'The doctor field is required.!']
            );
        }
        DB::transaction(function ()  use ($request, $visit) {
            $visit->patient()->update(
                [
                    'name'      => $request->name,
                    'age'       => $request->age,
                    'age_type'  => $request->age_type,
                    'gender'    => $request->gender,
                    'mobile'    => $request->mobile,
                    'user_id'   => Auth::user()->id,
                ]
            );
            $visit->update([
                'doctor_id'        => $request->doctor_id,
                'lab_id'           => $request->lab_id,
                'referred_by'      => $request->referred_by,
                'tests_count'      => count(Request::input('visitTests')),
                'offers_count'     => count(Request::input('visitOffers')),
                'tests_cost'       => $request->tests_cost,
                'discount'         => $request->discount,
                'final_cost'       => $request->final_cost,
                'paid_amount'      => $request->paid_amount,
                'remaining_amount' => $request->remaining_amount,
                'user_id'          => Auth::user()->id,
            ]);
            foreach (Request::input('visitTests') as $test) {
                $visit->tests()->syncWithoutDetaching([$test['id']  => [
                    'user_id'     =>  Auth::user()->id,
                    'device_id'   =>  $test['device_id'] ?? null,
                    'flag_id'     =>  $test['flag_id'] ?? null,
                    'result'      =>  $test['value'],
                    'value'       =>  is_numeric($test['value']) ? $test['value'] : null,
                    'created_at'  =>  now()->toDateString(),
                    'updated_at'  =>  now()->toDateString(),
                ]]);
            }

            foreach (Request::input('visitOffers') as $offer) {
                $visit->offers()->syncWithoutDetaching([$offer['id']  => [
                    'user_id'     =>  Auth::user()->id,
                    'created_at'  =>  now()->toDateString(),
                    'updated_at'  =>  now()->toDateString(),
                ]]);

                foreach ($offer['tests'] as $test) {
                    $visit->tests()->syncWithoutDetaching([$test['id']  => [
                        'user_id'     =>  Auth::user()->id,
                        'device_id'   =>  $test['device_id'] ?? null,
                        'flag_id'     =>  $test['flag_id'] ?? null,
                        'result'      =>  $test['value'],
                        'value'       =>  is_numeric($test['value']) ? $test['value'] : null,
                        'created_at'  =>  now()->toDateString(),
                        'updated_at'  =>  now()->toDateString(),
                        'offer_id'    =>  $offer['id'],
                    ]]);
                }
            }

            if (Request::input('calcLDLAndVLDL')){
                // $vldl = $tri/5;
                // $ldl  = $cholestrol - ($hdl + $vldl);
                $cholestrol_id  = 37;
                $tri_id         = 38;
                $hdl_id         = 39;
                $ldl_id         = 40;
                $vldl_id        = 41;

                $tri_value         = 0;
                $cholestrol_value  = 0;
                $hdl_value         = 0;
                $ldl_value         = 0;
                $vldl_value        = 0;

                foreach (Request::input('visitTests') as $test) {
                    if ($test['id'] === $tri_id) {
                        $tri_value = $test['value'];
                    }
                    if ($test['id'] === $cholestrol_id) {
                        $cholestrol_value = $test['value'];
                    }
                    if ($test['id'] === $hdl_id) {
                        $hdl_value = $test['value'];
                    }

                    if ($test['id'] === $ldl_id) {
                        $ldl_value = 1;
                    }
                    if ($test['id'] === $vldl_id) {
                        $vldl_value = 1;
                    }

                    // calc vldl_value
                    if ($tri_value > 0 && $vldl_value > 0) {
                        $vldl_value = $tri_value / 5;
                        $visit->tests()->syncWithoutDetaching([$vldl_id  => [
                            'user_id'     =>  Auth::user()->id,
                            'device_id'   =>  $test['device_id'] ?? null,
                            'flag_id'     =>  $test['flag_id'] ?? null,
                            'result'      =>  $vldl_value,
                            'value'       =>  is_numeric($vldl_value) ? $vldl_value : null,
                            'created_at'  =>  now()->toDateString(),
                            'updated_at'  =>  now()->toDateString(),
                        ]]);
                    }

                    // calc ldl_value
                    if ($ldl_value > 0 && $cholestrol_value > 0 && $hdl_value > 0 && $vldl_value > 0) {
                        $ldl_value = $cholestrol_value - ($hdl_value + $vldl_value);
                        $visit->tests()->syncWithoutDetaching([$ldl_id  => [
                            'user_id'     =>  Auth::user()->id,
                            'device_id'   =>  $test['device_id'] ?? null,
                            'flag_id'     =>  $test['flag_id'] ?? null,
                            'result'      =>  $ldl_value,
                            'value'       =>  is_numeric($ldl_value) ? $ldl_value : null,
                            'created_at'  =>  now()->toDateString(),
                            'updated_at'  =>  now()->toDateString(),
                        ]]);
                    }
                }
            }

            $GLOBALS['visit'] = $visit;
        });
        return redirect()->route('patientsVisits.print', [$GLOBALS['visit']]);
    }

    public function createNew(Patient $patient)
    {
        return Inertia::render('PatientsVisits/CreateNew', [
            'patient'     => $patient->only('id', 'name', 'gender', 'age_type', 'age'),
            'doctors'       => DB::table('doctors')->select('id', 'name')->whereNull('deleted_at')->get(),
            'sidelabs'      => DB::table('labs')->select('id', 'name')->whereNull('deleted_at')->get(),
            'flags'         => DB::table('flags')->select('id', 'name')->whereNull('deleted_at')->get(),
            'groups'        => Group::select('id', 'name')->with('tests:id')->orderBy('id')->get(),
            'offers'        => Offer::select('id', 'name', 'price')->with('tests:id')->orderBy('id')->get(),
            // 'tests'         => Test::withCount('visits')->with('devices:name')->with('results:name')->orderBy('visits_count', 'desc')->get()
            'tests'         => Test::with('devices:name')->with('results:name')->orderBy('sequence', 'desc')->get()
                ->map(fn ($test) => [
                    'id'                   => $test->id,
                    'short_name'           => $test->short_name,
                    'lab_to_patient_price' => $test->lab_to_patient_price,
                    'lab_to_lab_price'     => $test->lab_to_lab_price,
                    'device_id'            => $test->default_device_id,
                    'devices'              => $test->devices,
                    'results'              => $test->results,
                    'result_type'          => $test->result_type,
                    'value'                => null,
                    'flag_id'              => null,
                ]),
        ]);
    }

    public function storeNew(Patient $patient, HttpRequest $request)
    {
        Request::validate([
            'name'             => ['required', 'min:12', 'max:45', Rule::unique('patients')->ignore($patient->id)],
            'gender'           => ['nullable'],
            'age'              => ['nullable', 'digits_between:0,3'],
            'age_type'         => ['nullable', 'digits_between:0,3'],
            'mobile'           => ['nullable', 'min:11', 'max:11'],
            'doctor_id'        => ['nullable', Rule::exists('doctors', 'id')],
            'lab_id'           => ['nullable', Rule::exists('labs', 'id')],
            'referred_by'      => ['required', 'digits_between:1,3'],
            'tests_cost'       => ['required'],
            'discount'         => ['digits_between:0,6', 'lte:tests_cost',
                function ($attribute, $value, $fail)  {
                    if ($value % 250 != 0) {
                        $fail('Discount must be divisible by 250.');
                    }
                },
            ],
            'final_cost'       => ['required'],
            'paid_amount'      => ['required', 'digits_between:1,6',
                function ($attribute, $value, $fail) {
                    if ($value % 250 != 0) {
                        $fail('Paid amount must be divisible by 250.');
                    }
                },
            ],
            'remaining_amount' => ['required'],
        ]);

        Request::validate(
            [
                'visitTests.*.value'     => ['required'],
            ],
        );

        if (!Request::input('visitOffers')) {
            Request::validate(
                [
                    'visitTests'     => ['required'],
                ],
                [
                    'visitTests.required' => 'At least one  test must be added.!'
                ]
            );
        }

        if ($request->referred_by === 1) {
            Request::validate(
                ['lab_id'          => ['required', Rule::exists('labs', 'id')],],
                ['lab_id.required' => 'The SideLab field is required.!']
            );
        }
        if ($request->referred_by === 2) {
            Request::validate(
                ['doctor_id'          => ['required', Rule::exists('doctors', 'id')],],
                ['doctor_id.required' => 'The doctor field is required.!']
            );
        }
        DB::transaction(function ()  use ($request, $patient) {
            $patient->update([
                'name'      => $request->name,
                'age'       => $request->age,
                'age_type'  => $request->age_type,
                'gender'    => $request->gender,
                'mobile'    => $request->mobile,
            ]);
            $visit = Auth::user()->visits()->create([
                'patient_id'       => $patient->id,
                'doctor_id'        => $request->doctor_id,
                'lab_id'           => $request->lab_id,
                'referred_by'      => $request->referred_by,
                'tests_count'      => count(Request::input('visitTests')),
                'offers_count'     => count(Request::input('visitOffers')),
                'tests_cost'       => $request->tests_cost,
                'discount'         => $request->discount,
                'final_cost'       => $request->final_cost,
                'paid_amount'      => $request->paid_amount,
                'remaining_amount' => $request->remaining_amount,
            ]);
            foreach (Request::input('visitTests') as $test) {
                $visit->tests()->syncWithoutDetaching([$test['id']  => [
                    'user_id'     =>  Auth::user()->id,
                    'device_id'   =>  $test['device_id'] ?? null,
                    'flag_id'     =>  $test['flag_id'] ?? null,
                    'result'      =>  $test['value'],
                    'value'       =>  is_numeric($test['value']) ? $test['value'] : null,
                    'created_at'  =>  now()->toDateString(),
                    'updated_at'  =>  now()->toDateString(),
                ]]);
            }
            foreach (Request::input('visitOffers') as $offer) {
                $visit->offers()->syncWithoutDetaching([$offer['id']  => [
                    'user_id' =>  Auth::user()->id,
                    'created_at'  =>  now()->toDateString(),
                    'updated_at'  =>  now()->toDateString(),
                ]]);
                foreach ($offer['tests'] as $test) {
                    $visit->tests()->syncWithoutDetaching([$test['id']  => [
                        'user_id'     =>  Auth::user()->id,
                        'device_id'   =>  $test['device_id'] ?? null,
                        'flag_id'     =>  $test['flag_id'] ?? null,
                        'result'      =>  $test['value'],
                        'value'       =>  is_numeric($test['value']) ? $test['value'] : null,
                        'created_at'  =>  now()->toDateString(),
                        'updated_at'  =>  now()->toDateString(),
                        'offer_id'    =>  $offer['id'],
                    ]]);
                }
            }
            if (Request::input('calcLDLAndVLDL')){
                // $vldl = $tri/5;
                // $ldl  = $cholestrol - ($hdl + $vldl);
                $cholestrol_id  = 37;
                $tri_id         = 38;
                $hdl_id         = 39;
                $ldl_id         = 40;
                $vldl_id        = 41;

                $tri_value         = 0;
                $cholestrol_value  = 0;
                $hdl_value         = 0;
                $ldl_value         = 0;
                $vldl_value        = 0;

                foreach (Request::input('visitTests') as $test) {
                    if ($test['id'] === $tri_id) {
                        $tri_value = $test['value'];
                    }
                    if ($test['id'] === $cholestrol_id) {
                        $cholestrol_value = $test['value'];
                    }
                    if ($test['id'] === $hdl_id) {
                        $hdl_value = $test['value'];
                    }

                    if ($test['id'] === $ldl_id) {
                        $ldl_value = 1;
                    }
                    if ($test['id'] === $vldl_id) {
                        $vldl_value = 1;
                    }

                    // calc vldl_value
                    if ($tri_value > 0 && $vldl_value > 0) {
                        $vldl_value = $tri_value / 5;
                        $visit->tests()->syncWithoutDetaching([$vldl_id  => [
                            'user_id'     =>  Auth::user()->id,
                            'device_id'   =>  $test['device_id'] ?? null,
                            'flag_id'     =>  $test['flag_id'] ?? null,
                            'result'      =>  $vldl_value,
                            'value'       =>  is_numeric($vldl_value) ? $vldl_value : null,
                            'created_at'  =>  now()->toDateString(),
                            'updated_at'  =>  now()->toDateString(),
                        ]]);
                    }

                    // calc ldl_value
                    if ($ldl_value > 0 && $cholestrol_value > 0 && $hdl_value > 0 && $vldl_value > 0) {
                        $ldl_value = $cholestrol_value - ($hdl_value + $vldl_value);
                        $visit->tests()->syncWithoutDetaching([$ldl_id  => [
                            'user_id'     =>  Auth::user()->id,
                            'device_id'   =>  $test['device_id'] ?? null,
                            'flag_id'     =>  $test['flag_id'] ?? null,
                            'result'      =>  $ldl_value,
                            'value'       =>  is_numeric($ldl_value) ? $ldl_value : null,
                            'created_at'  =>  now()->toDateString(),
                            'updated_at'  =>  now()->toDateString(),
                        ]]);
                    }
                }
            }
            $GLOBALS['visit'] = $visit;
        });
        return redirect()->route('patientsVisits.print', [$GLOBALS['visit']]);
    }
}
