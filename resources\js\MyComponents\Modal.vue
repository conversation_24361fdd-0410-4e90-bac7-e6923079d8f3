<template>
<teleport to="body">
    <transition enter-active-class="transition duration-100 ease-out transform" enter-from-class="opacity-0 scale-50" enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-100 ease-in transform" leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-50">
    <div class="backdrop" @click.self="closeModal">
        <div class="modal" :class="{sale: theme === 'sale', sale2: theme2 === 'sale2'}">
            <slot></slot>
            <div class="actions">
                <slot name="links"></slot>
            </div>
        </div>
    
    </div>
    </transition>
</teleport>
</template>
<script>
export default{
    props:['theme'],
    // props:{
    //     title:String,
    //     content:String
    // },
    methods:{
        closeModal(){
            this.$emit('close')
        }
    }
}
</script>

<style scoped>
.modal{
    padding: 20px;
    margin: 100px auto;
    width: 400px;
    height: 200px;
    background-color: white;
    border-radius: 20px;

}

.backdrop{
    top:0;
    right: 0;
    position: fixed;
    background-color: rgba(0, 0, 0, 0.5);
    width: 100%;
    height: 100%;
}

.modal.sale{
    background-color: red;
    color: white;
}

.modal.sale h1{
    color: white;
}

.modal .actions{
    text-align: center;
    margin: 30px 0 10px 0;
}

.modal .actions a{
    color: #333;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 4px;
    text-decoration: none;
    margin: 10px;
}

.modal.sale .actions{
    color: white;
}

.modal.sale .actions a{
    color: white;
}
</style>