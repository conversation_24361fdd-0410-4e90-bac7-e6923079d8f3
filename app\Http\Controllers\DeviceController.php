<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request as HttpRequest;

use App\Models\Device;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Gate;

class DeviceController extends Controller
{
    public function index()
    {
        return Inertia::render('Devices/Index', [
            'filters' => Request::all('search', 'trashed'),
            'devices' => Device::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($device) => [
                    'id' => $device->id,
                    'name' => $device->name,
                    'created_at' => $device->created_at->diffForHumans(),
                    'updated_at' => $device->updated_at->diffForHumans(),
                    'deleted_at' => $device->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Devices/Create');
    }

    public function store()
    {
        Auth::user()->devices()->create(
            Request::validate([
                'name' => ['required', 'max:100', 'unique:devices']
            ])
        );

        if (Request::input('createAnother')) {
            return Redirect::back()->with('success', 'Device created.');
        } else {
            return Redirect::route('devices')->with('success', 'Device created.');
        }
    }

    public function edit(Device $device)
    {
        return Inertia::render('Devices/Edit', [
            'device' => [
                'id' => $device->id,
                'name' => $device->name,
                'created_at' => $device->created_at,
                'deleted_at' => $device->deleted_at,
                'tests' => $device->tests() ? $device->tests()->orderBy('short_name')->get()->map->only('id', 'short_name', 'full_name', 'created_at') : null,
            ],
        ]);
    }

    public function update(Device $device)
    {
        $device->update(
            Request::validate([
                'name' => ['required', 'max:100', Rule::unique('devices')->ignore($device->id)],
            ])
        );

        return Redirect::route('devices')->with('success', 'Device updated.');
    }

    public function destroy(Device $device)
    {
        $device->delete();
        return Redirect::route('devices')->with('success', 'Device deleted.');
    }

    public function restore(Device $device)
    {
        $device->restore();
        return Redirect::back()->with('success', 'Device restored.');
    }
}
