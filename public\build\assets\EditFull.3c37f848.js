import{v as T,Q as de,o as d,c as n,b as p,u as r,a as l,B as S,d as C,t as a,E as _,z as K,X as b,C as w,F as x,D as g,K as v,O as ne,H as ae,L as ue,S as H,R as E}from"./app.5bf25e6f.js";import{A as me}from"./AppLayout.14f8c8f6.js";import{_ as y}from"./TextInput.48e8e32c.js";import{D as U}from"./DropdownSearch.7a091d54.js";import{_ as P}from"./SelectInput.16ffd220.js";import{L as z}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const pe={class:"px-4 py-2"},ce={class:"mb-2 flex justify-start max-w-3xl"},fe={class:"font-bold text-2xl"},_e=l("span",{class:"text-indigo-400 font-medium"}," / ",-1),be={class:""},xe={class:"flex justify-start space-x-2"},ye={class:"bg-orange-400 rounded-md shadow max-w-xs",style:{"max-width":"270px"}},ge=l("option",{value:null},null,-1),ve=l("option",{value:"1"},"Male",-1),he=l("option",{value:"2"},"Female",-1),we={value:3,selected:""},Ve={value:2},ke={value:1},Le={value:3},Ae={value:2},Se={value:1},Ee={key:0,class:"pr-3 pb-3 w-full ltr"},Ue={key:1,class:"pr-3 pb-3 w-full ltr"},Fe={class:"flex px-1 mx-auto justify-between py-2 max-w-full"},Ce={class:"bg-orange-400 rounded-md shadow w-full max-w-6xl px-3 py-4 flex flex-wrap"},De={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Be={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Ie={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Me={class:"min-h-full min-w-full"},Oe={key:0,class:"min-w-full px-1 lg:px-2"},Te=l("div",{class:"inline-flex w-2/4 items-center justify-start"},[l("span",{class:"shadow px-2 bg-indigo-600 text-white max-h-8"},"Requested Tests:")],-1),Ke={class:"min-w-full mt-1 flex rounded-md shadow-sm"},Pe={class:"max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"30%",width:"35%"}},je=["onUpdate:modelValue"],$e=["onUpdate:modelValue"],Ne=["value"],Re=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"4%"}}," Flag: ",-1),He=["onUpdate:modelValue"],ze=["value"],qe=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"6%"}}," Device: ",-1),Ge=["onUpdate:modelValue"],Qe=["value"],Xe=["innerHTML"],Ye={class:"max-h-8 inline-flex items-center text-xs px-px font-medium border border-r-0 border-gray-300 text-gray-700",style:{"background-color":"#B1F56C","border-color":"#6F6AE6",width:"7%"}},Je={key:1,class:"min-w-full px-1 lg:px-2"},We=l("div",{class:"inline-flex w-2/4 items-center justify-start mt-1"},[l("span",{class:"shadow px-2 bg-indigo-600 text-white max-h-8"},"Requested Offers:")],-1),Ze={class:"inline-flex w-2/4 mt-1 items-center justify-start"},et={class:"shadow px-2 py-1 bg-indigo-600 text-white max-h-8"},tt={class:"shadow bg-yellow-300 px-2 py-1 text-gray-700 max-h-8"},ot={class:"min-w-full mt-1 flex rounded-md shadow-sm"},rt={class:"max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"30%",width:"30%"}},it=["onUpdate:modelValue"],lt=["onUpdate:modelValue"],st=["value"],dt=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"4%"}}," Flag: ",-1),nt=["onUpdate:modelValue"],at=["value"],ut=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"6%"}}," Device: ",-1),mt=["onUpdate:modelValue"],pt=["value"],ct=["innerHTML"],ft={class:"flex items-center my-2 ml-2"},_t=l("label",{for:"calcLDLAndVLDL",class:"ml-2 block text-sm text-gray-50"}," Calculate LDL & VLDL ",-1),bt={id:"confirmBox",hidden:"",class:"relative z-50"},xt=l("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1),yt={class:"fixed z-10 inset-0 overflow-y-auto"},gt={class:"flex items-end sm:items-center justify-center min-h-full p-4 text-center sm:p-0"},vt={class:"relative bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full rtl"},ht={class:"bg-white"},wt={class:"bg-orange-300 w-full"},Vt=l("div",{class:"bg-gray-50 p-2"},[l("p",{class:"text-xl text-gray-800 sm:text-right"}," \u0627\u0644\u0645\u0628\u0644\u063A \u0627\u0644\u0645\u062F\u0641\u0648\u0639 \u0644\u0627 \u064A\u0633\u0627\u0648\u064A \u0627\u0644\u062A\u0643\u0644\u0641\u0629 \u0627\u0644\u0646\u0647\u0627\u0626\u064A\u0629! ")],-1),kt={class:"flex p-3 mt-2 text-right rtl"},Lt={class:"bg-gray-50 p-2 sm:flex sm:flex-row-reverse space-x-2"},At={layout:me},Mt=Object.assign(At,{__name:"EditFull",props:{locale:String,doctors:Array,sidelabs:Array,tests:Array,groups:Array,offers:Array,flags:Array,visitTests:Array,visitOffers:Array,visit:Object},setup(F){var R;const u=F;let D=u.doctors,B=u.sidelabs,c=T(u.tests.slice()),V=T(u.offers.slice()),I=u.groups,j=u.flags,q=u.visitTests,G=u.visitOffers,t=de({_method:"put",id:u.visit.patient.id,name:u.visit.patient.name,calcLDLAndVLDL:!1,gender:u.visit.patient.gender,age:u.visit.patient.age,age_type:(R=u.visit.patient.age_type)!=null?R:3,mobile:u.visit.patient.mobile,doctor_id:u.visit.doctor_id,lab_id:u.visit.lab_id,referred_by:u.visit.referred_by,tests_cost:u.visit.tests_cost,discount:u.visit.discount,final_cost:u.visit.final_cost,paid_amount:u.visit.paid_amount,remaining_amount:u.visit.remaining_amount,visitTests:[],visitOffers:[]}),k=T([]),h=[],Q=()=>t.doctor_id!=null?D[D.findIndex(i=>i.id===t.doctor_id)].name:"Select Doctor",X=()=>t.lab_id!=null?B[B.findIndex(i=>i.id===t.lab_id)].name:"Select side Lab",Y=()=>{G.forEach(i=>{let e=V[V.findIndex(o=>o.id===i.offer_id)];h.push({id:e.id,name:e.name,price:e.price,tests:[]}),V.splice(V.findIndex(o=>o.id===i.offer_id),1)})},J=()=>{q.forEach(i=>{let e=c[c.findIndex(o=>o.id===i.test_id)];i.offer_id===null?k.push({id:i.test_id,device_id:i.device_id,value:i.result,flag_id:i.flag_id,short_name:e.short_name,lab_to_patient_price:e.lab_to_patient_price,lab_to_lab_price:e.lab_to_lab_price,devices:e.devices,results:e.results,result_type:e.result_type}):h[h.findIndex(o=>o.id===i.offer_id)].tests.push({id:i.test_id,device_id:i.device_id,value:i.result,flag_id:i.flag_id,offer_id:i.offer_id,short_name:e.short_name,lab_to_patient_price:e.lab_to_patient_price,lab_to_lab_price:e.lab_to_lab_price,devices:e.devices,results:e.results,result_type:e.result_type}),c.splice(c.findIndex(o=>o.id===i.test_id),1)})};Y(),J();let W=i=>{k.push({id:i.id,device_id:i.device_id,flag_id:i.flag_id,result_type:i.result_type,short_name:i.short_name,lab_to_lab_price:i.lab_to_lab_price,lab_to_patient_price:i.lab_to_patient_price,devices:i.devices,results:i.results,value:i.value}),c.splice(c.findIndex(e=>e.id===i.id),1),L()},Z=i=>{i.tests.forEach(e=>{let o=c[c.findIndex(f=>f.id===e.id)];o&&(k.push(o),c.splice(c.findIndex(f=>f.id===o.id),1))}),I.splice(I.findIndex(e=>e.id===i.id),1),L()},ee=i=>{let e=!0;if(i.tests.forEach(o=>{c.some(f=>f.id===o.id)||(e=!1)}),e){let o=[];i.tests.forEach(f=>{let s=c[c.findIndex(A=>A.id===f.id)];o.push(s),c.splice(c.findIndex(A=>A.id===f.id),1)}),h.push({id:i.id,name:i.name,price:i.price,tests:o}),V.splice(V.findIndex(f=>f.id===i.id),1)}else return alert("Some tests already exist, Please delete them first.");L()},te=()=>{t.referred_by==1?t.doctor_id=null:(t.referred_by==2||(t.doctor_id=null),t.lab_id=null),L()},oe=i=>{t.doctor_id=i.id},re=i=>{t.lab_id=i.id},L=()=>{let i=0;h.forEach(e=>{i+=e.price}),k.forEach(e=>{if(t.referred_by===1)i+=e.lab_to_lab_price;else if(t.referred_by===2||t.referred_by===3)i+=e.lab_to_patient_price;else return alert("ReferredBy field is required")}),t.tests_cost=i,t.final_cost=t.tests_cost-t.discount,t.remaining_amount=t.final_cost-t.paid_amount},ie=()=>{t.paid_amount=t.final_cost,t.remaining_amount=0},le=()=>{$(),t.paid_amount=t.final_cost,t.remaining_amount=0,O()},se=()=>{document.getElementById("confirmBox").hidden=!1},$=()=>{document.getElementById("confirmBox").hidden=!0},N=i=>{i&&O(),$()},M=()=>{t.paid_amount!=t.final_cost?se():O()},O=()=>{(t.discount===null||t.discount=="")&&(t.discount=0),k.forEach(i=>{t.visitTests.push({id:i.id,device_id:i.device_id,flag_id:i.flag_id,value:i.value})}),h.forEach(i=>{let e=[];i.tests.forEach(o=>{e.push({id:o.id,device_id:o.device_id,flag_id:o.flag_id,value:o.value,offer_id:i.id})}),t.visitOffers.push({id:i.id,tests:e})}),t.put(route("patientsVisits.updateFull",u.visit.id),{preserveState:!0,onError:()=>{t.reset("visitTests","visitOffers")},onSuccess:()=>{t.reset()}})};return(i,e)=>(d(),n(x,null,[p(r(ae),{title:"Update Visit"}),l("div",pe,[l("div",ce,[l("h2",fe,[p(r(ue),{class:"text-indigo-400 hover:text-indigo-600",href:i.route("patients")},{default:S(()=>[C(a(i.trans("PatientsList")),1)]),_:1},8,["href"]),_e,C(" "+a(r(t).name)+" / "+a(i.trans("Update")+" "+i.trans("Visit")),1)])]),l("div",be,[l("form",{onSubmit:e[26]||(e[26]=_((...o)=>r(M)&&r(M)(...o),["prevent"]))},[l("div",xe,[l("div",ye,[l("div",{class:K(["p-3 -mr-3 -mb-5 flex flex-wrap",F.locale=="ar"?"rtl text-right":"ltr text-left"])},[p(y,{modelValue:r(t).name,"onUpdate:modelValue":e[0]||(e[0]=o=>r(t).name=o),onKeydown:e[1]||(e[1]=b(_(()=>{},["prevent"]),["enter"])),error:r(t).errors.name,direction:"rtl",class:"pr-3 pb-3 w-full",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),p(P,{modelValue:r(t).gender,"onUpdate:modelValue":e[2]||(e[2]=o=>r(t).gender=o),modelModifiers:{number:!0},onKeydown:e[3]||(e[3]=b(_(()=>{},["prevent"]),["enter"])),error:r(t).errors.gender,direction:"ltr",class:"pr-3 pb-3 w-1/3",label:"Gender"},{default:S(()=>[ge,ve,he]),_:1},8,["modelValue","error"]),p(y,{modelValue:r(t).age,"onUpdate:modelValue":e[4]||(e[4]=o=>r(t).age=o),modelModifiers:{number:!0},onKeydown:e[5]||(e[5]=b(_(()=>{},["prevent"]),["enter"])),error:r(t).errors.age,direction:"ltr",class:"pr-3 pb-3 w-1/3",type:"number",label:"Age"},null,8,["modelValue","error"]),p(P,{modelValue:r(t).age_type,"onUpdate:modelValue":e[6]||(e[6]=o=>r(t).age_type=o),modelModifiers:{number:!0},onKeydown:e[7]||(e[7]=b(_(()=>{},["prevent"]),["enter"])),error:r(t).errors.age_type,class:"pr-3 pb-3 w-1/3",label:"Age Type"},{default:S(()=>[l("option",we,a(i.trans("Year")),1),l("option",Ve,a(i.trans("Month")),1),l("option",ke,a(i.trans("Day")),1)]),_:1},8,["modelValue","error"]),p(y,{modelValue:r(t).mobile,"onUpdate:modelValue":e[8]||(e[8]=o=>r(t).mobile=o),onKeydown:e[9]||(e[9]=b(_(()=>{},["prevent"]),["enter"])),error:r(t).errors.mobile,direction:"ltr",class:"pr-3 pb-3 w-1/2",type:"number",label:"Mobile"},null,8,["modelValue","error"]),p(P,{modelValue:r(t).referred_by,"onUpdate:modelValue":e[10]||(e[10]=o=>r(t).referred_by=o),modelModifiers:{number:!0},onKeydown:e[11]||(e[11]=b(_(()=>{},["prevent"]),["enter"])),error:r(t).errors.referred_by,onChange:r(te),class:"pr-3 pb-3 w-1/2",label:"ReferredBy"},{default:S(()=>[l("option",Le,a(i.trans("OutPatient")),1),l("option",Ae,a(i.trans("Doctor")),1),l("option",Se,a(i.trans("SideLab")),1)]),_:1},8,["modelValue","error","onChange"]),r(t).referred_by==2?(d(),n("div",Ee,[l("label",{for:"doctor",class:K(["form-label",F.locale=="ar"?"rtl":"ltr"])},a(i.trans("Doctor"))+":",3),p(U,{id:"doctor",error:r(t).errors.doctor_id,direction:"rtl",onKeydown:e[12]||(e[12]=b(_(()=>{},["prevent"]),["enter"])),options:r(D),onSelect:r(oe),CloseOnSelect:!0,fixedLabel:!1,label:r(Q)(),searchBy:"name",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0627\u0637\u0628\u0627\u0621"},null,8,["error","options","onSelect","label"])])):w("",!0),r(t).referred_by==1?(d(),n("div",Ue,[l("label",{for:"lab_id",class:K(["form-label",F.locale=="ar"?"rtl":"ltr"])},a(i.trans("SideLab"))+":",3),p(U,{id:"lab_id",error:r(t).errors.lab_id,direction:"rtl",onKeydown:e[13]||(e[13]=b(_(()=>{},["prevent"]),["enter"])),options:r(B),onSelect:r(re),CloseOnSelect:!0,fixedLabel:!1,label:r(X)(),searchBy:"name",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0645\u062E\u062A\u0628\u0631\u0627\u062A \u0627\u0644\u062C\u0627\u0646\u0628\u064A\u0629"},null,8,["error","options","onSelect","label"])])):w("",!0),p(y,{modelValue:r(t).tests_cost,"onUpdate:modelValue":e[14]||(e[14]=o=>r(t).tests_cost=o),modelModifiers:{number:!0},error:r(t).errors.tests_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"TestsCost"},null,8,["modelValue","error"]),p(y,{modelValue:r(t).discount,"onUpdate:modelValue":e[15]||(e[15]=o=>r(t).discount=o),modelModifiers:{number:!0},onKeydown:e[16]||(e[16]=b(_(()=>{},["prevent"]),["enter"])),direction:"ltr",error:r(t).errors.discount,onInput:e[17]||(e[17]=o=>r(L)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"Discount"},null,8,["modelValue","error"]),p(y,{modelValue:r(t).final_cost,"onUpdate:modelValue":e[18]||(e[18]=o=>r(t).final_cost=o),modelModifiers:{number:!0},error:r(t).errors.final_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"FinalCost"},null,8,["modelValue","error"]),p(y,{modelValue:r(t).paid_amount,"onUpdate:modelValue":e[19]||(e[19]=o=>r(t).paid_amount=o),modelModifiers:{number:!0},onKeydown:e[20]||(e[20]=b(_(()=>{},["prevent"]),["enter"])),direction:"ltr",error:r(t).errors.paid_amount,onInput:e[21]||(e[21]=o=>r(L)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"PaidAmount"},null,8,["modelValue","error"]),p(y,{modelValue:r(t).remaining_amount,"onUpdate:modelValue":e[22]||(e[22]=o=>r(t).remaining_amount=o),modelModifiers:{number:!0},error:r(t).errors.remaining_amount,direction:"ltr",class:"pr-3 pb-3 w-full",disabled:"disabled",type:"number",label:"RemainingAmount"},null,8,["modelValue","error"])],2),l("div",Fe,[p(z,{type:"button",class:"btn-green",onClick:r(ie)},{default:S(()=>[C(a(i.trans("FullyPaid")),1)]),_:1},8,["onClick"]),p(z,{loading:r(t).processing,class:"btn-indigo",onClick:r(M)},{default:S(()=>[C(a(i.trans("Update")+" & "+i.trans("Print")),1)]),_:1},8,["loading","onClick"])])]),l("div",Ce,[l("div",De,[p(U,{class:"bg-white rounded-md",options:r(c),onSelect:r(W),CloseOnSelect:!1,fixedLabel:!0,label:"Select Test",searchBy:"short_name",placeholder:"Search for tests"},null,8,["options","onSelect"])]),l("div",Be,[p(U,{class:"bg-white rounded-md",options:r(I),onSelect:r(Z),CloseOnSelect:!1,fixedLabel:!1,label:"Select Group",searchBy:"name",placeholder:"Search for groups"},null,8,["options","onSelect"])]),l("div",Ie,[p(U,{class:"bg-white rounded-md",options:r(V),onSelect:r(ee),CloseOnSelect:!0,fixedLabel:!1,label:"Select Offer",searchBy:"name",placeholder:"Search for Offers"},null,8,["options","onSelect"])]),l("div",Me,[r(k).length>0?(d(),n("div",Oe,[Te,(d(!0),n(x,null,g(r(k),(o,f)=>(d(),n("div",{key:f,class:"flex justify-start items-start"},[l("div",Ke,[l("span",Pe,a(f+1+" - "+o.short_name)+": ",1),o.result_type===0?v((d(),n("input",{key:0,onKeydown:e[23]||(e[23]=b(_(()=>{},["prevent"]),["enter"])),type:"text","onUpdate:modelValue":s=>o.value=s,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},null,40,je)),[[H,o.value]]):w("",!0),o.result_type===1?v((d(),n("select",{key:1,"onUpdate:modelValue":s=>o.value=s,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},[(d(!0),n(x,null,g(o.results,s=>(d(),n("option",{key:s.id,value:s.name},a(s.name),9,Ne))),128))],8,$e)),[[E,o.value]]):w("",!0),Re,v(l("select",{"onUpdate:modelValue":s=>o.flag_id=s,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(d(!0),n(x,null,g(r(j),s=>(d(),n("option",{class:"p-2",key:s.id,value:s.id},a(s.name),9,ze))),128))],8,He),[[E,o.flag_id]]),qe,v(l("select",{"onUpdate:modelValue":s=>o.device_id=s,class:"max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(d(!0),n(x,null,g(o.devices,s=>(d(),n("option",{key:s.id,value:s.pivot.device_id},a(s.name),9,Qe))),128))],8,Ge),[[E,o.device_id]]),l("span",{class:"hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-r-0 border-gray-300 bg-white text-gray-500",style:{"border-color":"#6F6AE6",width:"18%"},innerHTML:o.devices[o.devices.findIndex(s=>s.pivot.device_id===o.device_id)]?o.devices[o.devices.findIndex(s=>s.pivot.device_id===o.device_id)].pivot.normal_range:""},null,8,Xe),l("span",Ye,a(r(t).referred_by===1?o.lab_to_lab_price:o.lab_to_patient_price)+" IQD ",1)])]))),128))])):w("",!0),r(h).length>0?(d(),n("div",Je,[We,(d(!0),n(x,null,g(r(h),(o,f)=>(d(),n("div",{key:f},[l("div",Ze,[l("span",et,a(o.name),1),l("span",tt,a(o.price+" IQD"),1)]),(d(!0),n(x,null,g(o.tests,(s,A)=>(d(),n("div",{key:A,class:"min-w-full flex items-start"},[l("div",ot,[l("span",rt,a(A+1+" - "+s.short_name)+": ",1),s.result_type===0?v((d(),n("input",{key:0,onKeydown:e[24]||(e[24]=b(_(()=>{},["prevent"]),["enter"])),type:"text","onUpdate:modelValue":m=>s.value=m,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},null,40,it)),[[H,s.value]]):w("",!0),s.result_type===1?v((d(),n("select",{key:1,"onUpdate:modelValue":m=>s.value=m,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},[(d(!0),n(x,null,g(s.results,m=>(d(),n("option",{key:m.id,value:m.name},a(m.name),9,st))),128))],8,lt)),[[E,s.value]]):w("",!0),dt,v(l("select",{"onUpdate:modelValue":m=>s.flag_id=m,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(d(!0),n(x,null,g(r(j),m=>(d(),n("option",{class:"p-2",key:m.id,value:m.id},a(m.name),9,at))),128))],8,nt),[[E,s.flag_id]]),ut,v(l("select",{"onUpdate:modelValue":m=>s.device_id=m,class:"max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(d(!0),n(x,null,g(s.devices,m=>(d(),n("option",{key:m.id,value:m.pivot.device_id},a(m.name),9,pt))),128))],8,mt),[[E,s.device_id]]),l("span",{class:"hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-gray-300 bg-white text-gray-500",style:{"border-color":"#6F6AE6",width:"18%"},innerHTML:s.devices[s.devices.findIndex(m=>m.pivot.device_id===s.device_id)]?s.devices[s.devices.findIndex(m=>m.pivot.device_id===s.device_id)].pivot.normal_range:""},null,8,ct)])]))),128))]))),128))])):w("",!0),l("div",ft,[v(l("input",{id:"calcLDLAndVLDL","onUpdate:modelValue":e[25]||(e[25]=o=>r(t).calcLDLAndVLDL=o),type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[ne,r(t).calcLDLAndVLDL]]),_t])])])])],32)]),l("div",bt,[xt,l("div",yt,[l("div",gt,[l("div",vt,[l("div",ht,[l("div",wt,[Vt,l("div",kt,[p(y,{modelValue:r(t).final_cost,"onUpdate:modelValue":e[27]||(e[27]=o=>r(t).final_cost=o),modelModifiers:{number:!0},error:r(t).errors.final_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"\u0627\u0644\u062A\u0643\u0644\u0641\u0629 \u0627\u0644\u0646\u0647\u0627\u0626\u064A\u0629"},null,8,["modelValue","error"]),p(y,{modelValue:r(t).paid_amount,"onUpdate:modelValue":e[28]||(e[28]=o=>r(t).paid_amount=o),modelModifiers:{number:!0},onKeydown:e[29]||(e[29]=b(_(()=>{},["prevent"]),["enter"])),direction:"ltr",error:r(t).errors.paid_amount,onInput:e[30]||(e[30]=o=>r(L)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"\u0627\u0644\u0645\u0628\u0644\u063A \u0627\u0644\u0645\u062F\u0641\u0648\u0639 "},null,8,["modelValue","error"])])])]),l("div",Lt,[l("button",{type:"button",onClick:e[31]||(e[31]=o=>r(le)()),class:"btn-green2"},"\u0645\u0633\u0627\u0648\u0627\u0629 \u0648\u0627\u0633\u062A\u0645\u0631\u0627\u0631"),l("button",{type:"button",onClick:e[32]||(e[32]=o=>r(N)(!0)),class:"btn-green2"},"\u0627\u0633\u062A\u0645\u0631\u0627\u0631"),l("button",{type:"button",onClick:e[33]||(e[33]=o=>r(N)(!1)),class:"btn-indigo2"},"\u0627\u0644\u063A\u0627\u0621")])])])])])])],64))}});export{Mt as default};
