<template>
  <Head title="Create SideLab" />
  <div class="p-4">
    <h2 class="mb-2 font-bold text-2xl">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('labs')">{{ trans('SideLabsList') }}</Link>
      <span class="text-indigo-400 font-medium"> /</span> {{ trans('Create') }}
    </h2>
    <div class="bg-white rounded-md shadow overflow-hidden max-w-xl">
      <form @submit.prevent="labs">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" direction="rtl" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Name" :autofocus="true" />
          <text-input v-model="form.owner" :error="form.errors.owner" direction="rtl" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Owner" />
          <text-input v-model="form.mobile" :error="form.errors.mobile" direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Mobile" />
        </div>
        <div class="flex px-4 py-2 bg-gray-100 border-t border-gray-100  justify-start items-center">
          <loading-button :loading="form.processing" class="mr-4  px-3 py-2 btn-green" @click="submit2">{{trans("CreateAndCreateAnother")}}</loading-button>
          <loading-button :loading="form.processing" class="px-3 py-2  btn-green" @click="submit">{{trans('Create') + ' ' + trans('SideLab')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
  locale: String,
});

  let form = useForm({
    createAnother: false,
    name: null,
    owner: null,
    mobile: null,
  });

 let submit2 = () => {
    form.createAnother = true;
    store();
  };

  let submit = () => {
    form.createAnother = false;
    store();
  };

  let store = () => {
    form.post(route('labs.store'),{
      preserveState: true,
      onSuccess: () => { 
          form.reset();
        },
    })
  };
</script>
