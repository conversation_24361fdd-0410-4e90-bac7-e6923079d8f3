<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Request as FacadesRequest;
use Inertia\Inertia;
use App\Models\Visit;
use App\Models\Doctor;
use App\Models\User;
use App\Models\Lab;
use Illuminate\Support\Facades\DB;

class StatisticController extends Controller
{
    public function visitsDetails()
    {
        $per_page = 10;
        if (FacadesRequest::filled('per_page')) {
            $per_page = FacadesRequest::input('per_page');
        }

        return Inertia::render('Statistics/VisitsDetails', [
            'filters' => FacadesRequest::all(
                'search',
                'trashed',
                'sentToday',
                'HasDiscount',
                'start_date',
                'end_date',
                'gender',
                'per_page',
            ),
            'visits' => Visit::with('patient:id,name')
                ->orderBy('id', 'desc')
                ->filter(FacadesRequest::only(
                    'search',
                    'trashed',
                    'sentToday',
                    'HasDiscount',
                    'start_date',
                    'end_date',
                    'gender',
                    'per_page',
                ))
                ->paginate($per_page)
                ->withQueryString()
                ->through(fn ($visit) => [
                    'id'               => $visit->id,
                    'name'             => $visit->patient->name ?? 'Deleted Patient.!',
                    // 'patient_id'       => $visit->patient ? $visit->patient->id : 'deleted',
                    // 'referred_by'      => $visit->referred_by,
                    // 'doctor_name'      => $visit->doctor ? $visit->doctor->name : null,
                    'user'             => $visit->user->name ?? 'Deleted User.!',
                    // 'lab_name'         => $visit->lab ? $visit->lab->name : null,
                    'tests_count'      => $visit->tests_count,
                    'offers_count'     => $visit->offers_count,

                    'tests_cost'       => $visit->tests_cost,
                    'discount'         => $visit->discount,
                    'final_cost'       => $visit->final_cost,
                    'paid_amount'      => $visit->paid_amount,
                    'remaining_amount' => $visit->remaining_amount,
                    'created_at'       => $visit->created_at,
                    'deleted_at'       => $visit->deleted_at,
                    // 'visitTests'       => $visit->tests()->orderBy('sequence')->get()->map->only('id', 'short_name', 'lab_to_patient_price', 'lab_to_lab_price', 'category_id'),
                ]),
        ]);
    }

    public function IncomeReportIndex(Request $request)
    {
        $users = User::orderBy('id', 'asc')->get()->map->only('id', 'name');
        foreach ($users as $key => $user) {
            $users_income[$key]['user_name'] = $user['name'];
            $users_income[$key]['todays_income'] = Visit::whereDate('created_at', date('Y-m-d'))->where('user_id', '=', $user['id'])->sum('final_cost');
            $users_income[$key]['cumulative_income'] = Visit::whereMonth('created_at', date('m'))->where('user_id', '=', $user['id'])->sum('final_cost');
        }
        return Inertia::render('Statistics/IncomeReport', [
            'all_users_todays_income'     =>  Visit::whereDate('created_at', date('Y-m-d'))->sum('final_cost'),
            'all_users_cumulative_income' =>  Visit::whereMonth('created_at', date('m'))->sum('final_cost'),
            'ReportDateAndTime' => now()->toDateTimeString(),
            'users_income' => $users_income,
        ]);
    }
    public function IncomeReportFetch(Request $request)
    {
        $request->validate([
            'start_date'            => ['required', 'date'],
            'end_date'              => ['required', 'date'],
        ]);

        $users = User::orderBy('id', 'asc')->get()->map->only('id', 'name');
        foreach ($users as $key => $user) {
            $users_income[$key]['user_name'] = $user['name'];
            $users_income[$key]['todays_income'] = Visit::whereDate('created_at', date('Y-m-d'))->where('user_id', '=', $user['id'])->sum('final_cost');
            //$users_income[$key]['cumulative_income'] = Visit::whereMonth('created_at', date('m'))->where('user_id', '=', $user['id'])->sum('final_cost');
            $users_income[$key]['cumulative_income'] = Visit::whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59'])->where('user_id', '=', $user['id'])->sum('final_cost');
        }

        return Inertia::render('Statistics/IncomeReport', [
            'all_users_todays_income'     =>  Visit::whereDate('created_at', date('Y-m-d'))->sum('final_cost'),
            'all_users_cumulative_income' =>  Visit::whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59'])->sum('final_cost'),
            'start_date'  => $request->start_date,
            'end_date'  => $request->end_date,
            'ReportDateAndTime' => now(),
            'users_income' => $users_income,
        ]);
    }

    // Start Doctors Reports
    public function DoctorsReportIndex(Request $request)
    {

        $doctors = Doctor::withCount(['visits' => function($q)  {
            $q->whereMonth('created_at', date('m'));
        }])->orderBy('visits_count', 'desc')->get()->map->only('id', 'name', 'visits_count');

        foreach ($doctors as $key => $doctor) {
            $doctors_income[$key]['doctor_name'] = $doctor['name'];
            $doctors_income[$key]['visits_count'] = $doctor['visits_count'];
            $doctors_income[$key]['doctor_income'] = Visit::whereMonth('created_at', date('m'))->where('doctor_id', '=', $doctor['id'])->sum('final_cost');
        }

        return Inertia::render('Statistics/DoctorsReport', [
            'doctors_income'     => $doctors_income,
            'ReportDateAndTime' => now(),
        ]);
    }

    public function DoctorsReportFetch(Request $request)
    {
        $request->validate([
            'start_date'            => ['required', 'date'],
            'end_date'              => ['required', 'date'],
        ]);

        $doctors = Doctor::withCount(['visits' => function($q) use($request) {
            $q->whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59']);
        }])->orderBy('visits_count', 'desc')->get()->map->only('id', 'name', 'visits_count');

        foreach ($doctors as $key => $doctor) {
            $doctors_income[$key]['doctor_name'] = $doctor['name'];
            $doctors_income[$key]['visits_count'] = $doctor['visits_count'];
            $doctors_income[$key]['doctor_income'] = Visit::whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59'])
                ->where('doctor_id', '=', $doctor['id'])->sum('final_cost');
        }

        return Inertia::render('Statistics/DoctorsReport', [
            'doctors_income'     =>  $doctors_income,
            'start_date'  => $request->start_date,
            'end_date'  => $request->end_date,
            'ReportDateAndTime' => now(),
        ]);
    }
    // End Doctors Reports

    // Start SideLabs Reports
    public function SideLabsReportIndex(Request $request)
    {
        $labs = Lab::withCount(['visits' => function($q)  {
            $q->whereMonth('created_at', date('m'));
        }])->orderBy('visits_count', 'desc')->get()->map->only('id', 'name', 'visits_count');

        foreach ($labs as $key => $lab) {
            $side_labs_income[$key]['side_lab_name'] = $lab['name'];
            $side_labs_income[$key]['visits_count'] = $lab['visits_count'];
            $side_labs_income[$key]['side_lab_income'] = Visit::whereMonth('created_at', date('m'))->where('lab_id', '=', $lab['id'])->sum('final_cost');
        }
        
        return Inertia::render('Statistics/SideLabsReport', [
            'side_labs_income'     => $side_labs_income,
            'ReportDateAndTime' => now(),
        ]);
    }

    public function SideLabsReportFetch(Request $request)
    {
        $request->validate([
            'start_date'            => ['required', 'date'],
            'end_date'              => ['required', 'date'],
        ]);

        $labs = Lab::withCount(['visits' => function($q) use($request) {
            $q->whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59']);
        }])->orderBy('visits_count', 'desc')->get()->map->only('id', 'name', 'visits_count');

        foreach ($labs as $key => $lab) {
            $side_labs_income[$key]['side_lab_name'] = $lab['name'];
            $side_labs_income[$key]['visits_count'] = $lab['visits_count'];
            $side_labs_income[$key]['side_lab_income'] = Visit::whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59'])
                ->where('lab_id', '=', $lab['id'])->sum('final_cost');
        }

        return Inertia::render('Statistics/SideLabsReport', [
            'side_labs_income'     =>  $side_labs_income,
            'start_date'  => $request->start_date,
            'end_date'  => $request->end_date,
            'ReportDateAndTime' => now(),
        ]);
    }
    // End SideLabs Reports

    public function TestsReportIndex(Request $request)
    {
        $tests = DB::table('tests')->select('id', 'short_name')->orderBy('sequence')->get();
        foreach ($tests as $key => $test) {
            $tests_array[$key]['count']  = DB::table('test_visit')->where('test_id', $test->id)->whereMonth('created_at', date('m'))->count();
            $tests_array[$key]['name'] = $test->short_name;
        }
        return Inertia::render('Statistics/TestsReport', [
            'tests_array'            =>  $tests_array,
            'ReportDateAndTime' => now(),
        ]);
    }

    public function TestsReportFetch(Request $request)
    {
        $request->validate([
            'start_date'            => ['required', 'date'],
            'end_date'              => ['required', 'date'],
        ]);
        $tests = DB::table('tests')->select('id', 'short_name')->orderBy('sequence')->get();
        foreach ($tests as $key => $test) {
            $tests_array[$key]['count']  = DB::table('test_visit')->where('test_id', $test->id)->whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59'])->count();

            // $tests_array[$key]['count']  = round( DB::table('test_visit')->where('test_id', $test->id)->count()/3, 1);
            $tests_array[$key]['name'] = $test->short_name;
        }
        return Inertia::render('Statistics/TestsReport', [
            'tests_array'            =>  $tests_array,
            'start_date'  => $request->start_date,
            'end_date'  => $request->end_date,
            'ReportDateAndTime' => now(),
        ]);
    }

    public function OffersReportIndex(Request $request)
    {
        $offers = DB::table('offers')->select('id', 'name')->get();
        foreach ($offers as $key => $offer) {
            $offers_array[$key]['count']  = DB::table('offer_visit')->where('offer_id', $offer->id)->whereMonth('created_at', date('m'))->count();
            $offers_array[$key]['name'] = $offer->name;
        }
        return Inertia::render('Statistics/OffersReport', [
            'offers_array'   =>  $offers_array,
            'ReportDateAndTime' => now(),
        ]);
    }

    public function OffersReportFetch(Request $request)
    {
        $request->validate([
            'start_date'            => ['required', 'date'],
            'end_date'              => ['required', 'date'],
        ]);
        $offers = DB::table('offers')->select('id', 'name')->get();
        foreach ($offers as $key => $offer) {
            $offers_array[$key]['count']  = DB::table('offer_visit')->where('offer_id', $offer->id)->whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59'])->count();
            $offers_array[$key]['name'] = $offer->name;
        }
        return Inertia::render('Statistics/OffersReport', [
            'offers_array'  =>  $offers_array,
            'start_date'   => $request->start_date,
            'end_date'     => $request->end_date,
            'ReportDateAndTime' => now(),
        ]);
    }

    public function VisitsReportIndex(Request $request)
    {
        return Inertia::render('Statistics/VisitsReport', [
            'todays_visits'   =>  Visit::whereDate('created_at', date('Y-m-d'))->count(),
            'cumulative_visits'   =>  Visit::whereMonth('created_at', date('m'))->count(),
            'ReportDateAndTime' => now(),
        ]);
    }

    public function VisitsReportFetch(Request $request)
    {
        $request->validate([
            'start_date'            => ['required', 'date'],
            'end_date'              => ['required', 'date'],
        ]);

        return Inertia::render('Statistics/VisitsReport', [
            'todays_visits'     =>  Visit::whereDate('created_at', date('Y-m-d'))->count(),
            'cumulative_visits' =>  Visit::whereBetween('created_at', [$request->start_date . ' 00:00:00', $request->end_date . ' 23:59:59'])->count(),
            'start_date'  => $request->start_date,
            'end_date'  => $request->end_date,
            'ReportDateAndTime' => now(),
        ]);
    }
}
