import{v as S,q as T,o as a,c as _,b as d,u as n,a as e,B as l,t as r,y as u,C as c,F as b,D as k,H as L,K as v,R as w,d as C,L as p,U as g}from"./app.5bf25e6f.js";import{A as q,I as f}from"./AppLayout.14f8c8f6.js";import{t as A,S as B,p as I}from"./SearchFilter.f110f3d1.js";import{P as $}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const D={class:"p-4"},O={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},P=e("label",{class:"block text-gray-700"},"Category:",-1),U=e("option",{value:null},null,-1),F=["value"],R=e("label",{class:"block text-gray-700"},"Trashed:",-1),H=e("option",{value:null},null,-1),E={value:"with"},K={value:"only"},M={class:"shadow overflow-x-auto rounded-lg bg-white"},W={class:"min-w-full divide-y divide-gray-200"},z={class:"bg-gray-50"},G={class:"bg-green-500"},J={class:"flex justify-start items-center"},Q={class:"mr-1"},X={scope:"col",class:"px-2 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},Y={class:"flex justify-start items-center"},Z={class:"mr-1"},ee={class:"flex justify-start items-center"},te={class:"mr-1"},se={scope:"col",class:"px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},oe={scope:"col",class:"px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},re={class:"flex justify-start items-center"},ae={class:"mr-1"},ie={scope:"col",class:"px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},de={scope:"col",class:"px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},ne={class:"border-t p-0"},le={class:"px-6 py-2 flex items-center"},ce={class:"ml-4"},ue={class:"flex items-center text-sm font-medium text-gray-900"},pe={class:"border-t p-0"},fe={class:"px-6 py-2"},he={class:"border-t p-0"},_e={class:"px-6 py-2"},me={class:"border-t p-0"},ye={class:"px-6 py-2"},xe={class:"border-t p-0"},ge={class:"px-6 py-2"},be={class:"border-t p-0"},ke={class:"px-6 py-2"},ve={class:"border-t p-0"},we={class:"px-6 py-2"},Ce={class:"border-t p-0"},je={class:"px-6 py-2"},Ve={key:0,class:"border-t p-0"},Ne=["onClick"],Se={key:1,class:"border-t p-0"},Te=["onClick"],Le={key:0},qe=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No tests found.",-1),Ae=[qe],Be=e("hr",{class:"bg-gray-300 pt-px"},null,-1),Ie={layout:q},Re=Object.assign(Ie,{__name:"Index",props:{filters:Object,tests:Object,categories:Array},setup(h){const m=h,o=S({search:m.filters.search,trashed:m.filters.trashed,category_id:m.filters.category_id,field:m.filters.field,direction:m.filters.direction});T(o,A(function(){g.Inertia.get(route("tests"),I(o),{preserveState:!0,replace:!0})},300),{deep:!0});function y(s){o.field=s,o.direction=o.direction==="asc"?"desc":"asc"}function j(){Object.assign(o,{search:null,trashed:null,category_id:null,field:null,direction:null})}let V=s=>{confirm("Are you sure you want to destroy this visit ?")&&g.Inertia.delete(route("tests.destroy",s))},N=s=>{confirm("Are you sure you want to restore this visit ? ")&&g.Inertia.put(route("tests.restore",s))};return(s,i)=>(a(),_("div",D,[d(n(L),{title:"Tests"}),e("div",O,[d(B,{modelValue:o.search,"onUpdate:modelValue":i[2]||(i[2]=t=>o.search=t),direction:"ltr",placeholder:"Search for tests.....",class:"mb-2 sm:mb-0 w-full",onReset:j},{default:l(()=>[P,v(e("select",{"onUpdate:modelValue":i[0]||(i[0]=t=>o.category_id=t),class:"mt-1 w-full form-select"},[U,(a(!0),_(b,null,k(h.categories,(t,x)=>(a(),_("option",{key:x,value:t.id},r(t.name),9,F))),128))],512),[[w,o.category_id]]),R,v(e("select",{"onUpdate:modelValue":i[1]||(i[1]=t=>o.trashed=t),class:"mt-1 w-full form-select"},[H,e("option",E,r(s.trans("WithTrashed")),1),e("option",K,r(s.trans("OnlyTrashed")),1)],512),[[w,o.trashed]])]),_:1},8,["modelValue"]),d(n(p),{class:"btn-green",href:s.route("tests.create")},{default:l(()=>[C(r(s.trans("CreateANewTest")),1)]),_:1},8,["href"])]),e("div",M,[e("table",W,[e("thead",z,[e("tr",G,[e("th",{scope:"col",onClick:i[3]||(i[3]=t=>y("short_name")),class:"py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},[e("div",J,[e("span",Q,r(s.trans("ShortName")),1),o.field==="short_name"&&o.direction==="asc"?(a(),u(f,{key:0,name:"asc",class:"mt-1"})):c("",!0),o.field==="short_name"&&o.direction==="desc"?(a(),u(f,{key:1,name:"desc",class:"mt-1"})):c("",!0)])]),e("th",X,r(s.trans("FullName")),1),e("th",{scope:"col",onClick:i[4]||(i[4]=t=>y("category_id")),class:"py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},[e("div",Y,[e("span",Z,r(s.trans("Category")),1),o.field==="category_id"&&o.direction==="asc"?(a(),u(f,{key:0,name:"asc",class:"mt-1"})):c("",!0),o.field==="category_id"&&o.direction==="desc"?(a(),u(f,{key:1,name:"desc",class:"mt-1"})):c("",!0)])]),e("th",{scope:"col",onClick:i[5]||(i[5]=t=>y("sequence")),class:"py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},[e("div",ee,[e("span",te,r(s.trans("Sequence")),1),o.field==="sequence"&&o.direction==="asc"?(a(),u(f,{key:0,name:"asc",class:"mt-1"})):c("",!0),o.field==="sequence"&&o.direction==="desc"?(a(),u(f,{key:1,name:"desc",class:"mt-1"})):c("",!0)])]),e("th",se,r(s.trans("LabToPatientPrice")),1),e("th",oe,r(s.trans("LabToLabPrice")),1),e("th",{scope:"col",onClick:i[6]||(i[6]=t=>y("created_at")),class:"py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},[e("div",re,[e("span",ae,r(s.trans("Created_at")),1),o.field==="created_at"&&o.direction==="asc"?(a(),u(f,{key:0,name:"asc",class:"mt-1"})):c("",!0),o.field==="created_at"&&o.direction==="desc"?(a(),u(f,{key:1,name:"desc",class:"mt-1"})):c("",!0)])]),e("th",ie,r(s.trans("Updated_at")),1),e("th",de,r(s.trans("Delete")+" / "+s.trans("Restore")),1)])]),(a(!0),_(b,null,k(h.tests.data,t=>(a(),_("tr",{key:t.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",ne,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",le,[e("div",ce,[e("div",ue,[C(r(t.short_name)+" ",1),t.deleted_at?(a(),u(f,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):c("",!0)])])])]),_:2},1032,["href"])]),e("td",pe,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",fe,r(t.full_name),1)]),_:2},1032,["href"])]),e("td",he,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",_e,r(t.category.name),1)]),_:2},1032,["href"])]),e("td",me,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",ye,r(t.sequence),1)]),_:2},1032,["href"])]),e("td",xe,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",ge,r(t.lab_to_patient_price),1)]),_:2},1032,["href"])]),e("td",be,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",ke,r(t.lab_to_lab_price),1)]),_:2},1032,["href"])]),e("td",ve,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",we,r(t.created_at),1)]),_:2},1032,["href"])]),e("td",Ce,[d(n(p),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",je,r(t.updated_at),1)]),_:2},1032,["href"])]),t.deleted_at?(a(),_("td",Se,[e("button",{class:"text-green-600 hover:underline",type:"button",onClick:x=>n(N)(t.id)},r(s.trans("Restore")),9,Te)])):(a(),_("td",Ve,[e("button",{class:"text-red-600 hover:underline",type:"button",onClick:x=>n(V)(t.id)},r(s.trans("Delete")),9,Ne)]))]))),128)),h.tests.data.length===0?(a(),_("tr",Le,Ae)):c("",!0)]),Be,d($,{class:"px-6 py-2 bg-white border-none border-t p-0",links:h.tests.links,from:h.tests.from,to:h.tests.to,total:h.tests.total},null,8,["links","from","to","total"])])]))}});export{Re as default};
