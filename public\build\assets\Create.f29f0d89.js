import{Q as k,v as T,o as i,c as d,b as c,u as t,a as r,B as f,d as u,t as l,E as S,z as A,F as x,D as L,C as b,H as V,L as B}from"./app.5bf25e6f.js";import{A as D}from"./AppLayout.14f8c8f6.js";import{_ as N}from"./TextInput.48e8e32c.js";import{D as j}from"./DropdownSearch.7a091d54.js";import{L as v}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const E={class:"p-2 sm:p-4"},F={class:"mb-2 flex justify-start"},G={class:"font-bold text-2xl"},z=r("span",{class:"text-indigo-400 font-medium"}," /",-1),H={class:"bg-white rounded-md shadow max-w-2xl"},O={class:"pr-4 pb-0 w-full text-blue-600 font-semibold"},$={class:"p-1"},I={key:0,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},M={class:"w-full flex-row flex justify-start items-center flex-wrap"},Q=["onClick","for"],R={key:1,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},U=r("div",{class:"flex flex-wrap"},[r("div",{class:"flex items-stretch p-1"},"There are no attached tests yet.")],-1),q=[U],J={key:2,class:"form-error"},K={class:"ltr text-left w-full mt-2"},P={key:0,class:"ml-6 pb-2 text-red-600"},W={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},X={layout:D},le=Object.assign(X,{__name:"Create",props:{locale:String,tests:Array},setup(h){const _=h;let e=k({createAnother:!1,name:null,tests_count:null,groupTests:[]});const n=T(_.tests);let y=s=>{e.groupTests.push({id:s.id,short_name:s.short_name}),n.splice(n.findIndex(a=>a.id===s.id),1)},w=(s,a,o)=>{n.push({id:s,short_name:a}),e.groupTests.splice(o,1)},p=()=>{e.createAnother=!0,g()},C=()=>{e.createAnother=!1,g()},g=()=>{e.tests_count=e.groupTests.length,e.post(route("groups.store"),{preserveState:!0,onSuccess:()=>{e.reset(),n.splice(0,n.length),_.tests.forEach(s=>{n.push({id:s.id,short_name:s.short_name})})}})};return(s,a)=>(i(),d(x,null,[c(t(V),{title:"Create Group"}),r("div",E,[r("div",F,[r("h2",G,[c(t(B),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("groups")},{default:f(()=>[u(l(s.trans("GroupsList")),1)]),_:1},8,["href"]),z,u(" "+l(s.trans("Create")),1)])]),r("div",H,[r("form",{onSubmit:a[1]||(a[1]=S((...o)=>t(p)&&t(p)(...o),["prevent"]))},[r("div",{class:A(["p-5 -mr-6 -mb-5 flex flex-wrap",h.locale=="ar"?"rtl text-right":"ltr text-left"])},[c(N,{modelValue:t(e).name,"onUpdate:modelValue":a[0]||(a[0]=o=>t(e).name=o),error:t(e).errors.name,direction:"ltr",class:"pr-6 pb-4 w-full",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),r("div",O,[r("span",$,l(s.trans("Tests"))+":",1)]),t(e).groupTests.length>0?(i(),d("div",I,[r("div",M,[(i(!0),d(x,null,L(t(e).groupTests,(o,m)=>(i(),d("div",{class:"p-1 min-w-fit max-w-fit mt-1",key:m},[r("label",{title:"Remove Test",onClick:Y=>t(w)(o.id,o.short_name,m),class:"pt-0.5 btn-remove border border-green-400",for:o.short_name},l(m+1)+" - "+l(o.short_name),9,Q)]))),128))])])):(i(),d("div",R,q)),t(e).errors.tests_count?(i(),d("div",J,l(t(e).errors.tests_count),1)):b("",!0),r("div",K,[c(j,{class:"pr-6 pb-2 w-full sm:w-1/2",options:n,onSelect:t(y),CloseOnSelect:!1,fixedLabel:!0,label:"Select Test",direction:"ltr",searchBy:"short_name",placeholder:"Search for tests"},null,8,["options","onSelect"])])],2),t(e).isDirty?(i(),d("div",P,"There are unsaved form changes.")):b("",!0),r("div",W,[c(v,{loading:t(e).processing,class:"mr-4 flex px-3 py-2 btn-green",onClick:t(p)},{default:f(()=>[u(l(s.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),c(v,{loading:t(e).processing,class:"px-3 py-2 flex btn-green",onClick:t(C)},{default:f(()=>[u(l(s.trans("Create")+" "+s.trans("Group")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{le as default};
