<?php

namespace App\Http\Controllers;

use App\Models\Doctor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Gate;

class DoctorController extends Controller
{
    public function index()
    {
        return Inertia::render('Doctors/Index', [
            'filters' => Request::all('search', 'trashed'),
            'doctors' => Doctor::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($doctor) => [
                    'id' => $doctor->id,
                    'name' => $doctor->name,
                    'specialty' => $doctor->specialty,
                    'mobile' => $doctor->mobile,
                    'gender' => $doctor->gender,
                    'created_at' => $doctor->created_at->diffForHumans(),
                    'updated_at' => $doctor->updated_at->diffForHumans(),
                    'deleted_at' => $doctor->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Doctors/Create');
    }

    public function store()
    {
        Auth::user()->doctors()->create(
            Request::validate([
                'name' => ['required', 'min:8', 'max:55'],
                'specialty' => ['nullable', 'max:75'],
                'mobile' => ['nullable', 'min:11', 'max:11'],
                'gender' => ['required', 'max:2', 'min:1'],
            ])
        );
        if (Request::input('createAnother')) {
            return Redirect::back()->with('success', 'Doctor created.');
        } else {
            return Redirect::route('doctors')->with('success', 'Doctor created.');
        }
    }

    public function edit(Doctor $doctor)
    {
        return Inertia::render('Doctors/Edit', [
            'doctor' => [
                'id' => $doctor->id,
                'name' => $doctor->name,
                'specialty' => $doctor->specialty,
                'mobile' => $doctor->mobile,
                'gender' => $doctor->gender,
                'deleted_at' => $doctor->deleted_at,
                'visits' => $doctor->visits()->orderBy('created_at')->get()->map->only('id', 'created_at'),
            ],
        ]);
    }

    public function update(Doctor $doctor)
    {
        $doctor->update(
            Request::validate([
                'name' => ['required', 'min:8', 'max:55'],
                'specialty' => ['nullable', 'max:75'],
                'mobile' => ['nullable', 'min:11', 'max:11'],
                'gender' => ['required', 'max:2', 'min:1'],
            ])
        );
        return Redirect::route('doctors')->with('success', 'Doctor updated.');
    }

    public function destroy(Doctor $doctor)
    {
        $doctor->delete();
        return Redirect::route('doctors')->with('success', 'Doctor deleted.');
    }

    public function restore(Doctor $doctor)
    {
        $doctor->restore();
        return Redirect::back()->with('success', 'Doctor restored.');
    }
}
