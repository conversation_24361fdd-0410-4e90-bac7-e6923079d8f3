<template>
<Head title="Edit Tracking Tag" />
<div class="p-2 sm:p-4">
    <div class="mb-2 flex justify-start max-w-xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('tracking_tags')">{{ trans('TrackingTagsList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
        {{ form.name }}
      </h2>
    </div>
    <div class="bg-white rounded-md shadow-sm overflow-hidden max-w-xl">
      <trashed-message v-if="tracking_tag.deleted_at" class="mb-6" @restore="restore">
      {{ trans('This') + ' ' +  trans('TrackingTag') + ' ' + trans('HasBeenDeleted')}}
    </trashed-message>
      <form @submit.prevent="update">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" direction="ltr" class="pr-6 pb-8 w-full" type="text" label="Name" :autofocus="true" />
        </div>
        <div class="px-4 py-2 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
          <loading-button :loading="form.processing" class="btn-green" type="submit">{{trans('Update') + ' ' + trans('TrackingTag')}}</loading-button>
          <button v-if="!tracking_tag.deleted_at" class="text-red-600 hover:underline" tabindex="-1" type="button" @click="destroy">{{trans('Delete') + ' ' + trans('TrackingTag')}}</button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'
import moment from 'moment'

const props = defineProps({
  locale: String,
    tracking_tag: Object,
});

let form = useForm({
    _method: 'put',
    name: props.tracking_tag.name,
});

let update = () => {
    form.post(route('tracking_tags.update', props.tracking_tag.id), {
    onSuccess: () => form.reset('name'),
    })
};

let destroy = () => {
    if (confirm('Are you sure you want to delete this tracking tag?')) {
    Inertia.delete(route('tracking_tags.destroy', props.tracking_tag.id))
    }
};

let restore = () => {
    if (confirm('Are you sure you want to restore this tracking tag?')) {
    Inertia.put(route('tracking_tags.restore', props.tracking_tag.id))
    }
};
</script>