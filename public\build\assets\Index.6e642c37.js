import{v as x,q as y,o as d,c as u,b as r,u as n,a as t,B as l,t as a,F as p,D as b,C as m,H as k,K as w,R as v,L as c,d as T,y as V,U as N}from"./app.5bf25e6f.js";import{A as j,I as _}from"./AppLayout.14f8c8f6.js";import{t as B,S as C,p as S}from"./SearchFilter.f110f3d1.js";import{P as O}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const I={class:"p-4 max-w-7xl"},L={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},U=t("label",{class:"block text-gray-700"},"Trashed:",-1),A=t("option",{value:null},null,-1),D={value:"with"},F={value:"only"},E={class:"inline sm:hidden"},H={class:"hidden sm:flex w-52 text-center"},P={class:"shadow overflow-x-auto rounded-lg bg-white"},R={class:"min-w-full divide-y divide-gray-200"},q={class:"bg-gray-50"},K={class:"bg-green-500"},M={scope:"col",class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},W={scope:"col",class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},z={scope:"col",class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},G={scope:"col",class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},J={class:"border-t p-0"},Q={class:"px-6 py-2 flex items-center"},X={class:"ml-4"},Y={class:"flex items-center text-sm font-medium text-gray-900"},Z={class:"border-t p-0"},$={class:"px-6 py-2"},tt={class:"border-t p-0"},et={class:"px-6 py-2"},st={class:"border-t p-0"},at={class:"px-6 py-2"},rt={key:0},ot=t("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No tracking tags found.",-1),it=[ot],nt=t("hr",{class:"bg-gray-300 pt-px"},null,-1),lt={layout:j},mt=Object.assign(lt,{__name:"Index",props:{filters:Object,tracking_tags:Object},setup(o){const f=o,i=x({search:f.filters.search,trashed:f.filters.trashed});y(i,B(function(){N.Inertia.get(route("tracking_tags"),S(i),{preserveState:!0,replace:!0})},300),{deep:!0});function g(){Object.assign(i,{search:null,trashed:""})}return(e,h)=>(d(),u(p,null,[r(n(k),{title:"Tracking Tags"}),t("div",I,[t("div",L,[r(C,{modelValue:i.search,"onUpdate:modelValue":h[1]||(h[1]=s=>i.search=s),direction:"ltr",placeholder:"Search for tracking tags",class:"mb-2 sm:mb-0 w-full",onReset:g},{default:l(()=>[U,w(t("select",{"onUpdate:modelValue":h[0]||(h[0]=s=>i.trashed=s),class:"mt-1 w-full form-select"},[A,t("option",D,a(e.trans("WithTrashed")),1),t("option",F,a(e.trans("OnlyTrashed")),1)],512),[[v,i.trashed]])]),_:1},8,["modelValue"]),r(n(c),{class:"whitespace-nowrap w-full sm:w-52 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-3 rounded-md font-medium",href:e.route("tracking_tags.create")},{default:l(()=>[t("span",E,a(e.trans("Create")),1),t("span",H,a(e.trans("CreateANewTrackingTag")),1)]),_:1},8,["href"])]),t("div",P,[t("table",R,[t("thead",q,[t("tr",K,[t("th",M,a(e.trans("Name")),1),t("th",W,a(e.trans("Created_at")),1),t("th",z,a(e.trans("Updated_at")),1),t("th",G,a(e.trans("Edit")),1)])]),(d(!0),u(p,null,b(o.tracking_tags.data,s=>(d(),u("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[t("td",J,[r(n(c),{href:e.route("tracking_tags.edit",s.id),tabindex:"-1"},{default:l(()=>[t("div",Q,[t("div",X,[t("div",Y,[T(a(s.name)+" ",1),s.deleted_at?(d(),V(_,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):m("",!0)])])])]),_:2},1032,["href"])]),t("td",Z,[r(n(c),{href:e.route("tracking_tags.edit",s.id),tabindex:"-1"},{default:l(()=>[t("div",$,a(s.created_at),1)]),_:2},1032,["href"])]),t("td",tt,[r(n(c),{href:e.route("tracking_tags.edit",s.id),tabindex:"-1"},{default:l(()=>[t("div",et,a(s.updated_at),1)]),_:2},1032,["href"])]),t("td",st,[r(n(c),{class:"flex items-center text-right text-sm font-medium",href:e.route("tracking_tags.edit",s.id),tabindex:"-1"},{default:l(()=>[t("div",at,[r(_,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),o.tracking_tags.data.length===0?(d(),u("tr",rt,it)):m("",!0)]),nt,r(O,{class:"px-6 py-2 bg-white border-none border-t p-0",links:o.tracking_tags.links,from:o.tracking_tags.from,to:o.tracking_tags.to,total:o.tracking_tags.total},null,8,["links","from","to","total"])])])],64))}});export{mt as default};
