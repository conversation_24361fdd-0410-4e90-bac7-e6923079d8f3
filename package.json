{"private": true, "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"@inertiajs/inertia": "^0.11.0", "@inertiajs/inertia-vue3": "^0.6.0", "@inertiajs/progress": "^0.2.7", "@tailwindcss/forms": "^0.5.2", "@tailwindcss/typography": "^0.5.2", "@vitejs/plugin-vue": "^2.3.3", "autoprefixer": "^10.4.7", "axios": "^0.25", "laravel-vite-plugin": "^0.4.0", "lodash": "^4.17.19", "postcss": "^8.4.14", "tailwindcss": "^3.1.0", "vite": "^2.9.11", "vue": "^3.2.31"}, "dependencies": {"@headlessui/vue": "^1.6.7", "@heroicons/vue": "^1.0.6", "@vueup/vue-quill": "^1.0.0-beta.7", "moment": "^2.29.4", "xlsx": "^0.18.5"}}