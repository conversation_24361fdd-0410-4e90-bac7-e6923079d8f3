<?php

namespace App\Http\Controllers;

use App\Models\Result;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;

class ResultController extends Controller
{
    public function index()
    {
        return Inertia::render('Results/Index', [
            'filters' => Request::all('search', 'trashed'),
            'results' => Result::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($result) => [
                    'id' => $result->id,
                    'name' => $result->name,
                    'created_at' => $result->created_at->diffForHumans(),
                    'updated_at' => $result->updated_at->diffForHumans(),
                    'deleted_at' => $result->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Results/Create');
    }

    public function store()
    {
        Auth::user()->results()->create(
            Request::validate([
                'name' => ['required', 'max:100', 'unique:results']
            ])
        );
        if (Request::input('createAnother')) {
            return Redirect::back()->with('success', 'Result created.');
        } else {
            return Redirect::route('results')->with('success', 'Result created.');
        }
    }

    public function edit(Result $result)
    {
        return Inertia::render('Results/Edit', [
            'result' => [
                'id' => $result->id,
                'name' => $result->name,
                'created_at' => $result->created_at,
                'deleted_at' => $result->deleted_at,
                'tests' => $result->tests() ? $result->tests()->orderBy('short_name')->get()->map->only('id', 'short_name', 'full_name', 'created_at') : null,
            ],
        ]);
    }
    public function update(Result $result)
    {
        $result->update(
            Request::validate([
                'name' => ['required', 'max:100', Rule::unique('results')->ignore($result->id)],
            ])
        );
        return Redirect::route('results')->with('success', 'Result updated.');
    }

    public function destroy(Result $result)
    {
        $result->delete();
        return Redirect::route('results')->with('success', 'Result deleted.');
    }

    public function restore(Result $result)
    {
        $result->restore();
        return Redirect::back()->with('success', 'Result restored.');
    }
}
