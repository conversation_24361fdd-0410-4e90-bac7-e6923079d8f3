<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();

        $user = User::create([
            'name'     => '<PERSON>',
            'email'    => '<EMAIL>',
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'isAdmin'  => true,
            'role'     => 'Administrator',
        ]);

        $setting = Setting::create([
            'lab_name'          => 'LaraLab',
            'header_photo_path' => '../SaraiLabHeader.jpg',
            'footer_photo_path' => '../SaraiLabFooter.jpg',
        ]);
    }
}
