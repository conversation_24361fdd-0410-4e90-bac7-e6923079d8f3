<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDoctorsTable extends Migration
{
    public function up()
    {
        Schema::enableForeignKeyConstraints();
        Schema::create('doctors', function (Blueprint $table) {

            $table->id();
            $table->string('name', 35)->unique();
            $table->string('specialty', 55)->nullable();
            $table->string('mobile', 25)->nullable();

            $table->tinyInteger('gender')->nullable();

            $table->foreignId('user_id')->constrained()->onDelete('restrict')->onUpdate('cascade');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('doctors');
    }
}
