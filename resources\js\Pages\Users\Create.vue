<template>
  <Head title="Create User" />
  <div class="p-4">
    <h2 class="mb-2 font-bold text-2xl">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('users')">{{ trans('UsersList') }}</Link>
      <span class="text-indigo-400 font-medium">/</span> {{ trans('Create') }}
    </h2>
    <div class="bg-white rounded-md shadow overflow-hidden max-w-3xl">
      <form @submit.prevent="store">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input   v-model="form.name"     :error="form.errors.name"      direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" type="text"     label="Username" :autofocus="true" />
          <text-input   v-model="form.email"    :error="form.errors.email"     direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" type="email"    label="Email" />
          <text-input   v-model="form.password" :error="form.errors.password"  direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" type="password" label="Password" autocomplete="new-password" />
          <select-input v-model="form.role"     :error="form.errors.role"      direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2"                 label="Role">
            <option :value='null'></option>
            <option value="Administrator">Administrator</option>
            <option value="Reception_staff">Reception Staff</option>
          </select-input>
        </div>
        <div class="flex px-8 py-4 bg-gray-50 border-t border-gray-100  justify-start items-center">
          <loading-button :loading="form.processing" class="flex btn-green" type="submit">{{trans('Create') + ' ' + trans('User')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import FileInput from '@/MyComponents/FileInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { reactive, ref } from '@vue/reactivity'
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
  locale: String,
});

let form = useForm({
  name: null,
  email: null,
  password: null,
  role: null,
  photo: null,
});

let store = () => {
  form.post(route('users.store'));
};

</script>
