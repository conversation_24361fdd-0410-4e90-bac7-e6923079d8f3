<template>
  <div class="p-2 xl:p-3">
    <Head title="Visits" />
    <div class="flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-2">
      <form @submit.prevent="submit">
        <div class="flex flex-wrap">
            <div class="flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2">
              <span class="inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"> {{trans('From')}}: </span>
              <input v-model="form.start_date" type="date" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"/>
            </div>
            <div class="flex rounded-md shadow-sm  mr-0.5 md:mr-1 xl:mr-2">
              <span class="inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"> {{trans('To')}}: </span>
              <input v-model="form.end_date" type="date" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"/>
            </div>
            <loading-button :loading="form.processing" type="submit" class="btn-indigo flex items-center group  mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3">{{trans('Go')}}  </loading-button>
            <div class="btn-indigo  mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3" @click="tabletoExcel('visits.xlsx')"> {{trans('ExportThisPage')}} </div> 
            <button type="button" class="btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2" @click="print">{{ trans("Print")}}</button> 
        </div>
      </form>
      <researchs-nav />
    </div>

    <section class="flex justify-between p-2 mb-2 bg-white shadow rounded items-center">
      <div class="flex justify-start items-center w-full">
        <research-filter class="mr-1 w-3/12" name="Filters">
          <div class="overflow-y-auto max-h-96 pr-2 pl-1">
            <label class="form-label min-w-full pt-2">Age Between:</label>
            <div class="flex">
                <input v-model="form.age_min" type="number" placeholder="Min" class="form-input  w-1/2 mr-1">
                <input v-model="form.age_max"   type="number" placeholder="Max" class="form-input  w-1/2">
            </div>
            <label class="block text-gray-700 pt-2">Gender:</label>
            <select v-model.number="form.gender" class="mt-1 w-full form-select">
              <option :value="null" />
              <option value="1">Male</option>
              <option value="2">Female</option>
            </select>
            <label class="block text-gray-700">Smoking:</label>
            <select v-model="form.smoking" class="mt-1 w-full form-select">
              <option :value="null" />
              <option value="1">Smoker</option>
              <option value="2">Non-Smoker</option>
              <option value="3">Ex-Smoker</option>
            </select>

            <label class="block text-gray-700">Marital Status:</label>
            <select v-model="form.marital" class="mt-1 w-full form-select">
              <option :value="null" />
              <option value="1">Single</option>
              <option value="2">Married</option>
              <option value="3">Divorced</option>
              <option value="4">Widowed</option>
            </select>

          </div>
        </research-filter>
        <research-filter class="mr-1 w-3/12" :name="form.tracking_tag_id ? ourTrackingTags[ourTrackingTags.findIndex(diag => diag.id == form.tracking_tag_id)].name : 'Select Tracking Tag'">
          <dropdown-search class="w-full ltr pt-1 h-32" :options="ourTrackingTags" @select="addTrackingTag" 
            :CloseOnSelect="true" :fixedLabel="false" label="Select Tracking Tag" searchBy="name" :direction="'ltr'" placeholder="Search for tracking tags">
          </dropdown-search>
        </research-filter>
        <research-filter class="mr-1 w-3/12" :name="form.test_id ? ourTests[ourTests.findIndex(test => test.id == form.test_id)].short_name : 'Select Test'">
          <dropdown-search class="w-full ltr pt-2 h-32" :options="ourTests" @select="addTest" 
            :CloseOnSelect="true" :fixedLabel="false" label="Select Test" searchBy="short_name" :direction="'ltr'" placeholder="Search for tests">
          </dropdown-search>
          <label class="form-label min-w-full">Between:</label>
          <div class="flex">
              <input v-model="form.test_min_value" type="number" placeholder="Min" class="form-input  w-1/2 mr-1">
              <input v-model="form.test_max_value"   type="number" placeholder="Max" class="form-input  w-1/2">
          </div>
        </research-filter>
        <button class="mx-2 text-sm text-gray-500 hover:text-gray-700 focus:text-indigo-500" @click="reset">Reset</button>
      </div>
      <select v-model.number="form.per_page" class="mr-1 block py-2 px-4 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm w-2/12">
          <option value="10">    10 Per Page </option>
          <option value="100">  100 Per Page </option>
          <option value="200">  200 Per Page </option>
          <option value="300">  300 Per Page </option>
          <option value="1000">1000 Per Page </option>
          <option value="2000">2000 Per Page </option>
          <option value="5000">5000 Per Page </option>
        </select>
      <div class="flex items-center whitespace-nowrap px-4 h-9 rounded border border-gray-300 shadow-sm
         focus:ring-blue-500 focus:border-blue-500">Total: {{ visits.total}}</div>
    </section>
    <div id="reportToPrint" class="flex overflow-hidden">
      <div class="shadow overflow-x-auto rounded-lg bg-white min-w-full">
      <table id="table" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-green-500">
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('ID')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Name')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Age')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Gender')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('MaritalStatus')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Smoking')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Tests')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('TrackingTags')}}</th>
          <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Date')}}</th>
        </tr>
        </thead>
        <tr v-for="visit in visits.data" :key="visit.id">
          <td class="border-t px-8 py-1">{{ visit.patient_id }}</td>
          <td class="border-t px-8 py-1 whitespace-nowrap">{{ visit.name }}</td>
          <td class="border-t px-8 py-1">{{ visit.age }}</td>
          <td class="border-t px-8 py-1">{{ visit.gender === 1? trans('Male') : trans('Female') }}</td>
          <td class="border-t px-2 py-1">
            <span v-if="visit.marital === 1">{{trans('Single')}}</span>
            <span v-else-if="visit.marital === 2">{{trans('Married')}} </span>
            <span v-else-if="visit.marital === 3">{{trans('Divorced')}}</span>
            <span v-else-if="visit.marital === 4">{{trans('Widowed')}} </span>
            <span v-else>Unknown</span>
          </td>
          <td class="border-t px-2 py-1">
            <span v-if="visit.smoking === 1">     {{trans('Smoker')}}</span>
            <span v-else-if="visit.smoking === 2">{{trans('NonSmoker')}}</span>
            <span v-else-if="visit.smoking === 3">{{trans('ExSmoker')}}</span>
            <span v-else>{{trans('Unknown')}}</span>
          </td>
          <td v-if="visit.tests" class="border-t px-8"><span v-for="test in visit.tests" :key="test.id">{{test.short_name}}=>{{test.pivot.value}}, </span></td>
          <td class="border-t px-8"><span v-if="visit.tracking_tags"><span v-for="(tracking_tag, i) in visit.tracking_tags" :key="i" class="inline-flex text-red-500">{{ tracking_tag.name }} ,</span> </span></td>
          <td class="border-t px-8 whitespace-nowrap">{{moment(visit.created_at).format('YYYY-MM-DD')}}</td>
        </tr>
        <tr v-if="visits.data.length === 0">
          <td class="border-t px-8 py-4 min-w-full" colspan="14">No visits found.</td>
        </tr>
      </table>
      <hr class="bg-gray-300 pt-px">
      <pagination class="px-3 py-2 bg-white border-none border-t p-0" :links="visits.links" :from="visits.from" :to="visits.to" :total="visits.total" />
      </div>
    </div>
  </div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
import Pagination from '@/MyComponents/Pagination.vue'
import ResearchsNav from '@/MyComponents/ResearchsNav.vue'
import ResearchFilter from '@/MyComponents/ResearchFilter.vue'
import DropdownSearch from '@/MyComponents/DropdownSearch.vue' 
import Icon from '@/MyComponents/Icon.vue'
import JetNavLink from '@/Jetstream/NavLink.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'
import { computed, reactive, ref } from '@vue/reactivity'
import moment from 'moment'

// import XLSX from 'xlsx';
import * as XLSX from 'xlsx/xlsx.mjs';


const props = defineProps({
  errors: Object,
  locale: String,
  visits: Object,
  filters: Object,
  tracking_tags: Array,
  tests: Array,
});

let ourTrackingTags = reactive(props.tracking_tags.slice());
let ourTests        = reactive(props.tests.slice());

let form = useForm({
  start_date:        props.filters.start_date,
  end_date:          props.filters.end_date,
  test_id:           props.filters.test_id,
  test_min_value:    props.filters.test_min_value,
  test_max_value:    props.filters.test_max_value,
  tracking_tag_id:   props.filters.tracking_tag_id,
  gender:            props.filters.gender? props.filters.gender :null,
  smoking:           props.filters.smoking,
  marital:           props.filters.marital,
  age_min:           props.filters.age_min,
  age_max:           props.filters.age_max,
  per_page:          props.filters.per_page>10?props.filters.per_page:10,
});

let addTrackingTag = (selectedTrackingTag) => {
  form.tracking_tag_id = selectedTrackingTag.id;
};

let addTest = (selectedTest) => {
  form.test_id = selectedTest.id;
};

let reset = () => {
  Object.assign(form,{
    start_date:        null,
    end_date:          null,
    test_id:           null,
    test_min_value:    null,
    test_max_value:    null,
    tracking_tag_id:   null,
    gender:            null,
    smoking:           null,
    marital:           null,
    age_min:           null,
    age_max:           null,
    per_page:          null,
  });
  form.get(route('researchs.VisitsIndex'))
}

let submit = () => {
  form.get(route('researchs.VisitsIndex'),{
    preserveState: true,
  })
};

let print = () => {
  setTimeout(function(){
    window.print();
  } , 50);
};
  
let tabletoExcel = (fn, dl) => {
  var elt = document.getElementById('table');
  var wb = XLSX.utils.table_to_book(elt, { sheet: "sheet1" });
  return dl ? XLSX.write(wb, { bookType: type, bookSST: true, type: 'base64' }): XLSX.writeFile(wb, fn || ('MySheetName.' + (type || 'xlsx')));
}
</script>
