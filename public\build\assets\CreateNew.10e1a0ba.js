import{v as M,Q as re,o as n,c as d,b as u,u as r,a as l,B as A,d as I,t as m,E as b,z as K,X as f,C as w,F as x,D as v,K as h,O as le,H as se,L as ie,S as P,R as S}from"./app.5bf25e6f.js";import{A as ne,I as H}from"./AppLayout.14f8c8f6.js";import{_ as y}from"./TextInput.48e8e32c.js";import{D as C}from"./DropdownSearch.7a091d54.js";import{_ as O}from"./SelectInput.16ffd220.js";import{L as de}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const ae={class:"px-4 py-2"},ue={class:"mb-2 flex justify-start max-w-xl"},me={class:"font-bold text-2xl"},pe=l("span",{class:"text-indigo-400 font-medium"}," /",-1),ce={class:""},be={class:"flex justify-start space-x-2"},fe={class:"bg-orange-400 rounded-md shadow max-w-xs",style:{"max-width":"270px"}},_e=l("option",{value:null},null,-1),xe=l("option",{value:"1"},"Male",-1),ye=l("option",{value:"2"},"Female",-1),ge={value:3,selected:""},ve={value:2},he={value:1},we={value:3},Ve={value:2},ke={value:1},Le={key:0,class:"pr-3 pb-3 w-full ltr"},Se={key:1,class:"pr-3 pb-3 w-full ltr"},Ae={class:"flex px-1 mx-auto justify-between py-2 max-w-full"},Ce={class:"bg-orange-400 rounded-md shadow w-full max-w-6xl px-3 py-4 flex flex-wrap"},Ee={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Ue={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},Fe={class:"px-1 md:px-2 lg:px-2 pb-3 w-1/3"},De={class:"min-h-full min-w-full"},Be={key:0,class:"min-w-full px-1 lg:px-2"},Me=l("div",{class:"inline-flex w-2/4 items-center justify-start"},[l("span",{class:"shadow px-2 bg-indigo-600 text-white max-h-8"},"Requested Tests:")],-1),Ie={class:"min-w-full mt-1 flex rounded-md shadow-sm"},Ke={class:"max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"30%",width:"35%"}},Oe=["onUpdate:modelValue"],Te=["onUpdate:modelValue"],je=["value"],Ne=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"4%"}}," Flag: ",-1),Re=["onUpdate:modelValue"],$e=["value"],Pe=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"6%"}}," Device: ",-1),He=["onUpdate:modelValue"],ze=["value"],qe=["innerHTML"],Ge={class:"max-h-8 inline-flex items-center text-xs px-px font-medium border border-r-0 border-gray-300 text-gray-700",style:{"background-color":"#B1F56C","border-color":"#6F6AE6",width:"7%"}},Qe=["onClick"],Xe={key:1,class:"min-w-full px-1 lg:px-2"},Ye=l("div",{class:"inline-flex w-2/4 items-center justify-start mt-1"},[l("span",{class:"shadow px-2 bg-indigo-600 text-white max-h-8"},"Requested Offers:")],-1),Je={class:"inline-flex w-2/4 mt-1 items-center justify-start"},We={class:"shadow px-2 py-1 bg-indigo-600 text-white max-h-8"},Ze={class:"shadow bg-yellow-300 px-2 py-1 text-gray-700 max-h-8"},eo=["onClick"],oo={class:"min-w-full mt-1 flex rounded-md shadow-sm"},to={class:"max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"30%",width:"30%"}},ro=["onUpdate:modelValue"],lo=["onUpdate:modelValue"],so=["value"],io=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"4%"}}," Flag: ",-1),no=["onUpdate:modelValue"],ao=["value"],uo=l("span",{class:"max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50",style:{"background-color":"#3B3799","border-color":"#6F6AE6","min-width":"6%"}}," Device: ",-1),mo=["onUpdate:modelValue"],po=["value"],co=["innerHTML"],bo={class:"flex items-center my-2 ml-2"},fo=l("label",{for:"calcLDLAndVLDL",class:"ml-2 block text-sm text-gray-50"}," Calculate LDL & VLDL ",-1),_o={id:"confirmBox",hidden:"",class:"relative z-50"},xo=l("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1),yo={class:"fixed z-10 inset-0 overflow-y-auto"},go={class:"flex items-end sm:items-center justify-center min-h-full p-4 text-center sm:p-0"},vo={class:"relative bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full rtl"},ho={class:"bg-white"},wo={class:"bg-orange-300 w-full"},Vo=l("div",{class:"bg-gray-50 p-2"},[l("p",{class:"text-xl text-gray-800 sm:text-right"}," \u0627\u0644\u0645\u0628\u0644\u063A \u0627\u0644\u0645\u062F\u0641\u0648\u0639 \u0644\u0627 \u064A\u0633\u0627\u0648\u064A \u0627\u0644\u062A\u0643\u0644\u0641\u0629 \u0627\u0644\u0646\u0647\u0627\u0626\u064A\u0629! ")],-1),ko={class:"flex p-3 mt-2 text-right rtl"},Lo={class:"bg-gray-50 p-2 sm:flex sm:flex-row-reverse space-x-2"},So={layout:ne},Io=Object.assign(So,{__name:"CreateNew",props:{locale:String,patient:Object,doctors:Array,sidelabs:Array,tests:Array,groups:Array,offers:Array,flags:Array},setup(E){var $;const _=E,z=_.doctors,q=_.sidelabs;let c=M(_.tests.slice()),U=M(_.offers.slice());const F=_.groups,T=_.flags;let o=re({name:_.patient.name,calcLDLAndVLDL:!1,gender:_.patient.gender,age:_.patient.age,age_type:($=_.patient.age_type)!=null?$:3,mobile:_.patient.mobile,doctor_id:null,lab_id:null,referred_by:3,tests_cost:0,discount:null,final_cost:0,paid_amount:null,remaining_amount:0,visitTests:[],visitOffers:[]}),V=M([]),k=[],G=s=>{V.push({id:s.id,device_id:s.device_id,flag_id:s.flag_id,result_type:s.result_type,short_name:s.short_name,lab_to_lab_price:s.lab_to_lab_price,lab_to_patient_price:s.lab_to_patient_price,devices:s.devices,results:s.results,value:s.value}),c.splice(c.findIndex(e=>e.id===s.id),1),g()},Q=(s,e)=>{c.push(s),V.splice(e,1),g()},X=s=>{s.tests.forEach(e=>{let t=c[c.findIndex(p=>p.id===e.id)];t&&(V.push(t),c.splice(c.findIndex(p=>p.id===t.id),1))}),F.splice(F.findIndex(e=>e.id===s.id),1),g()},Y=s=>{let e=!0;if(s.tests.forEach(t=>{c.some(p=>p.id===t.id)||(e=!1)}),e){let t=[];s.tests.forEach(p=>{let i=c[c.findIndex(L=>L.id===p.id)];t.push(i),c.splice(c.findIndex(L=>L.id===p.id),1)}),k.push({id:s.id,name:s.name,price:s.price,tests:t}),U.splice(U.findIndex(p=>p.id===s.id),1)}else return alert("Some tests already exist, Please delete them first.");g()},J=(s,e)=>{s.tests.forEach(t=>{c.push(t)}),U.push(s),k.splice(e,1),g()},W=()=>{o.referred_by===1?o.doctor_id=null:(o.referred_by===2||(o.doctor_id=null),o.lab_id=null),g()},Z=s=>{o.doctor_id=s.id},ee=s=>{o.lab_id=s.id},g=()=>{let s=0;k.forEach(e=>{s+=e.price}),V.forEach(e=>{if(o.referred_by===1)s+=e.lab_to_lab_price;else if(o.referred_by===2||o.referred_by===3)s+=e.lab_to_patient_price;else return alert("ReferredBy field is required")}),o.tests_cost=s,o.final_cost=o.tests_cost-o.discount,o.remaining_amount=o.final_cost-o.paid_amount,console.log("computed!")},j=()=>{o.paid_amount=o.final_cost,o.remaining_amount=0},oe=()=>{N(),o.paid_amount=o.final_cost,o.remaining_amount=0,B()},te=()=>{document.getElementById("confirmBox").hidden=!1},N=()=>{document.getElementById("confirmBox").hidden=!0},R=s=>{s&&B(),N()},D=()=>{o.paid_amount!=o.final_cost?te():B()},B=()=>{(o.discount===null||o.discount=="")&&(o.discount=0),V.forEach(s=>{o.visitTests.push({id:s.id,device_id:s.device_id,flag_id:s.flag_id,value:s.value})}),k.forEach(s=>{let e=[];s.tests.forEach(t=>{e.push({id:t.id,device_id:t.device_id,flag_id:t.flag_id,value:t.value,offer_id:s.id})}),o.visitOffers.push({id:s.id,tests:e})}),o.post(route("patientsVisits.storeNew",_.patient.id),{preserveState:!0,onError:()=>{o.reset("visitTests","visitOffers")},onSuccess:()=>{o.reset()}})};return(s,e)=>(n(),d(x,null,[u(r(se),{title:"Create Visit"}),l("div",ae,[l("div",ue,[l("h2",me,[u(r(ie),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("patients")},{default:A(()=>[I(m(s.trans("PatientsList")),1)]),_:1},8,["href"]),pe,I(" "+m(s.trans("RegisterANewVisit")),1)])]),l("div",ce,[l("form",{onSubmit:e[27]||(e[27]=b((...t)=>r(D)&&r(D)(...t),["prevent"]))},[l("div",be,[l("div",fe,[l("div",{class:K(["p-3 -mr-3 -mb-5 flex flex-wrap",E.locale=="ar"?"rtl text-right":"ltr text-left"])},[u(y,{modelValue:r(o).name,"onUpdate:modelValue":e[0]||(e[0]=t=>r(o).name=t),onKeydown:e[1]||(e[1]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.name,direction:"rtl",class:"pr-3 pb-3 w-full",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),u(O,{modelValue:r(o).gender,"onUpdate:modelValue":e[2]||(e[2]=t=>r(o).gender=t),modelModifiers:{number:!0},onKeydown:e[3]||(e[3]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.gender,direction:"ltr",class:"pr-3 pb-3 w-1/3",label:"Gender"},{default:A(()=>[_e,xe,ye]),_:1},8,["modelValue","error"]),u(y,{modelValue:r(o).age,"onUpdate:modelValue":e[4]||(e[4]=t=>r(o).age=t),modelModifiers:{number:!0},onKeydown:e[5]||(e[5]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.age,direction:"ltr",class:"pr-3 pb-3 w-1/3",type:"number",label:"Age"},null,8,["modelValue","error"]),u(O,{modelValue:r(o).age_type,"onUpdate:modelValue":e[6]||(e[6]=t=>r(o).age_type=t),modelModifiers:{number:!0},onKeydown:e[7]||(e[7]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.age_type,class:"pr-3 pb-3 w-1/3",label:"Age Type"},{default:A(()=>[l("option",ge,m(s.trans("Year")),1),l("option",ve,m(s.trans("Month")),1),l("option",he,m(s.trans("Day")),1)]),_:1},8,["modelValue","error"]),u(y,{modelValue:r(o).mobile,"onUpdate:modelValue":e[8]||(e[8]=t=>r(o).mobile=t),onKeydown:e[9]||(e[9]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.mobile,direction:"ltr",class:"pr-3 pb-3 w-1/2",type:"number",label:"Mobile"},null,8,["modelValue","error"]),u(O,{modelValue:r(o).referred_by,"onUpdate:modelValue":e[10]||(e[10]=t=>r(o).referred_by=t),modelModifiers:{number:!0},onKeydown:e[11]||(e[11]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.referred_by,onChange:r(W),class:"pr-3 pb-3 w-1/2",label:"ReferredBy"},{default:A(()=>[l("option",we,m(s.trans("OutPatient")),1),l("option",Ve,m(s.trans("Doctor")),1),l("option",ke,m(s.trans("SideLab")),1)]),_:1},8,["modelValue","error","onChange"]),r(o).referred_by===2?(n(),d("div",Le,[l("label",{for:"doctor",class:K(["form-label",E.locale=="ar"?"rtl":"ltr"])},m(s.trans("Doctor"))+":",3),u(C,{id:"doctor",error:r(o).errors.doctor_id,direction:"rtl",onKeydown:e[12]||(e[12]=f(b(()=>{},["prevent"]),["enter"])),options:r(z),onSelect:r(Z),CloseOnSelect:!0,fixedLabel:!1,label:"Select Doctor",searchBy:"name",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0627\u0637\u0628\u0627\u0621"},null,8,["error","options","onSelect"])])):w("",!0),r(o).referred_by===1?(n(),d("div",Se,[l("label",{for:"lab_id",class:K(["form-label",E.locale=="ar"?"rtl":"ltr"])},m(s.trans("SideLab"))+":",3),u(C,{id:"lab_id",error:r(o).errors.lab_id,direction:"rtl",onKeydown:e[13]||(e[13]=f(b(()=>{},["prevent"]),["enter"])),options:r(q),onSelect:r(ee),CloseOnSelect:!0,fixedLabel:!1,label:"Select Side Lab",searchBy:"name",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0645\u062E\u062A\u0628\u0631\u0627\u062A \u0627\u0644\u062C\u0627\u0646\u0628\u064A\u0629"},null,8,["error","options","onSelect"])])):w("",!0),u(y,{modelValue:r(o).tests_cost,"onUpdate:modelValue":e[14]||(e[14]=t=>r(o).tests_cost=t),modelModifiers:{number:!0},error:r(o).errors.tests_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"TestsCost"},null,8,["modelValue","error"]),u(y,{modelValue:r(o).discount,"onUpdate:modelValue":e[15]||(e[15]=t=>r(o).discount=t),modelModifiers:{number:!0},onKeydown:e[16]||(e[16]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.discount,direction:"ltr",onInput:e[17]||(e[17]=t=>r(g)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"Discount"},null,8,["modelValue","error"]),u(y,{modelValue:r(o).final_cost,"onUpdate:modelValue":e[18]||(e[18]=t=>r(o).final_cost=t),modelModifiers:{number:!0},error:r(o).errors.final_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"FinalCost"},null,8,["modelValue","error"]),u(y,{modelValue:r(o).paid_amount,"onUpdate:modelValue":e[19]||(e[19]=t=>r(o).paid_amount=t),modelModifiers:{number:!0},onKeydown:e[20]||(e[20]=f(b(()=>{},["prevent"]),["enter"])),error:r(o).errors.paid_amount,direction:"ltr",onInput:e[21]||(e[21]=t=>r(g)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"PaidAmount"},null,8,["modelValue","error"]),u(y,{modelValue:r(o).remaining_amount,"onUpdate:modelValue":e[22]||(e[22]=t=>r(o).remaining_amount=t),modelModifiers:{number:!0},error:r(o).errors.remaining_amount,direction:"ltr",class:"pr-3 pb-3 w-full",disabled:"disabled",type:"number",label:"RemainingAmount"},null,8,["modelValue","error"])],2),l("div",Ae,[l("button",{type:"button",class:"btn-green",onClick:e[23]||(e[23]=(...t)=>r(j)&&r(j)(...t))},m(s.trans("FullyPaid")),1),u(de,{loading:r(o).processing,class:"btn-indigo",onClick:r(D)},{default:A(()=>[I(m(s.trans("Save")+" & "+s.trans("Print")),1)]),_:1},8,["loading","onClick"])])]),l("div",Ce,[l("div",Ee,[u(C,{class:"bg-white rounded-md",options:r(c),onSelect:r(G),CloseOnSelect:!1,fixedLabel:!0,label:"Select Test",searchBy:"short_name",placeholder:"Search for tests"},null,8,["options","onSelect"])]),l("div",Ue,[u(C,{class:"bg-white rounded-md",options:r(F),onSelect:r(X),CloseOnSelect:!1,fixedLabel:!1,label:"Select Group",searchBy:"name",placeholder:"Search for groups"},null,8,["options","onSelect"])]),l("div",Fe,[u(C,{class:"bg-white rounded-md",options:r(U),onSelect:r(Y),CloseOnSelect:!0,fixedLabel:!1,label:"Select Offer",searchBy:"name",placeholder:"Search for Offers"},null,8,["options","onSelect"])]),l("div",De,[r(V).length>0?(n(),d("div",Be,[Me,(n(!0),d(x,null,v(r(V),(t,p)=>(n(),d("div",{key:p,class:"flex justify-start items-start"},[l("div",Ie,[l("span",Ke,m(p+1+" - "+t.short_name)+": ",1),t.result_type===0?h((n(),d("input",{key:0,onKeydown:e[24]||(e[24]=f(b(()=>{},["prevent"]),["enter"])),type:"text","onUpdate:modelValue":i=>t.value=i,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},null,40,Oe)),[[P,t.value]]):w("",!0),t.result_type===1?h((n(),d("select",{key:1,"onUpdate:modelValue":i=>t.value=i,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},[(n(!0),d(x,null,v(t.results,i=>(n(),d("option",{key:i.id,value:i.name},m(i.name),9,je))),128))],8,Te)),[[S,t.value]]):w("",!0),Ne,h(l("select",{"onUpdate:modelValue":i=>t.flag_id=i,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(x,null,v(r(T),i=>(n(),d("option",{class:"p-2",key:i.id,value:i.id},m(i.name),9,$e))),128))],8,Re),[[S,t.flag_id]]),Pe,h(l("select",{"onUpdate:modelValue":i=>t.device_id=i,class:"max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(x,null,v(t.devices,i=>(n(),d("option",{key:i.id,value:i.pivot.device_id},m(i.name),9,ze))),128))],8,He),[[S,t.device_id]]),l("span",{class:"hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-r-0 border-gray-300 bg-white text-gray-500",style:{"border-color":"#6F6AE6",width:"18%"},innerHTML:t.devices[t.devices.findIndex(i=>i.pivot.device_id===t.device_id)]?t.devices[t.devices.findIndex(i=>i.pivot.device_id===t.device_id)].pivot.normal_range:""},null,8,qe),l("span",Ge,m(r(o).referred_by===1?t.lab_to_lab_price:t.lab_to_patient_price)+" IQD ",1),l("span",{title:"Remove Test",onClick:i=>r(Q)(t,p),class:"max-h-8 text-sm inline-flex items-center px-2 rounded-r-md border border-l-0 bg-red-600 hover:bg-red-500 cursor-pointer",style:{"border-color":"#6F6AE6",width:"3%"}},[u(H,{name:"x",class:"h-4 w-4"})],8,Qe)])]))),128))])):w("",!0),r(k).length>0?(n(),d("div",Xe,[Ye,(n(!0),d(x,null,v(r(k),(t,p)=>(n(),d("div",{key:p},[l("div",Je,[l("span",We,m(t.name),1),l("span",Ze,m(t.price+" IQD"),1),l("span",{title:"Remove Offer",onClick:i=>r(J)(t,p),class:"shadow max-h-8 text-sm px-2 py-1 bg-red-600 hover:bg-red-500 cursor-pointer"},[u(H,{name:"x",class:"h-6 w-6"})],8,eo)]),(n(!0),d(x,null,v(t.tests,(i,L)=>(n(),d("div",{key:L,class:"min-w-full flex items-start"},[l("div",oo,[l("span",to,m(L+1+" - "+i.short_name)+": ",1),i.result_type===0?h((n(),d("input",{key:0,onKeydown:e[25]||(e[25]=f(b(()=>{},["prevent"]),["enter"])),type:"text","onUpdate:modelValue":a=>i.value=a,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},null,40,ro)),[[P,i.value]]):w("",!0),i.result_type===1?h((n(),d("select",{key:1,"onUpdate:modelValue":a=>i.value=a,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"18%","max-width":"25%",width:"25%"}},[(n(!0),d(x,null,v(i.results,a=>(n(),d("option",{key:a.id,value:a.name},m(a.name),9,so))),128))],8,lo)),[[S,i.value]]):w("",!0),io,h(l("select",{"onUpdate:modelValue":a=>i.flag_id=a,class:"max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(x,null,v(r(T),a=>(n(),d("option",{class:"p-2",key:a.id,value:a.id},m(a.name),9,ao))),128))],8,no),[[S,i.flag_id]]),uo,h(l("select",{"onUpdate:modelValue":a=>i.device_id=a,class:"max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300",style:{"border-color":"#6F6AE6","min-width":"8%"}},[(n(!0),d(x,null,v(i.devices,a=>(n(),d("option",{key:a.id,value:a.pivot.device_id},m(a.name),9,po))),128))],8,mo),[[S,i.device_id]]),l("span",{class:"hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-gray-300 bg-white text-gray-500",style:{"border-color":"#6F6AE6",width:"18%"},innerHTML:i.devices[i.devices.findIndex(a=>a.pivot.device_id===i.device_id)]?i.devices[i.devices.findIndex(a=>a.pivot.device_id===i.device_id)].pivot.normal_range:""},null,8,co)])]))),128))]))),128))])):w("",!0),l("div",bo,[h(l("input",{id:"calcLDLAndVLDL","onUpdate:modelValue":e[26]||(e[26]=t=>r(o).calcLDLAndVLDL=t),type:"checkbox",class:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"},null,512),[[le,r(o).calcLDLAndVLDL]]),fo])])])])],32)]),l("div",_o,[xo,l("div",yo,[l("div",go,[l("div",vo,[l("div",ho,[l("div",wo,[Vo,l("div",ko,[u(y,{modelValue:r(o).final_cost,"onUpdate:modelValue":e[28]||(e[28]=t=>r(o).final_cost=t),modelModifiers:{number:!0},error:r(o).errors.final_cost,direction:"ltr",class:"pr-3 pb-3 w-1/2",disabled:"disabled",type:"number",label:"\u0627\u0644\u062A\u0643\u0644\u0641\u0629 \u0627\u0644\u0646\u0647\u0627\u0626\u064A\u0629"},null,8,["modelValue","error"]),u(y,{modelValue:r(o).paid_amount,"onUpdate:modelValue":e[29]||(e[29]=t=>r(o).paid_amount=t),modelModifiers:{number:!0},onKeydown:e[30]||(e[30]=f(b(()=>{},["prevent"]),["enter"])),direction:"ltr",error:r(o).errors.paid_amount,onInput:e[31]||(e[31]=t=>r(g)()),class:"pr-3 pb-3 w-1/2",type:"number",label:"\u0627\u0644\u0645\u0628\u0644\u063A \u0627\u0644\u0645\u062F\u0641\u0648\u0639 "},null,8,["modelValue","error"])])])]),l("div",Lo,[l("button",{type:"button",onClick:e[32]||(e[32]=t=>r(oe)()),class:"btn-green2"},"\u0645\u0633\u0627\u0648\u0627\u0629 \u0648\u0627\u0633\u062A\u0645\u0631\u0627\u0631"),l("button",{type:"button",onClick:e[33]||(e[33]=t=>r(R)(!0)),class:"btn-green2"},"\u0627\u0633\u062A\u0645\u0631\u0627\u0631"),l("button",{type:"button",onClick:e[34]||(e[34]=t=>r(R)(!1)),class:"btn-indigo2"},"\u0627\u0644\u063A\u0627\u0621")])])])])])])],64))}});export{Io as default};
