import{n as c,r as a,o as _,c as w,b as o,B as m,F as b,H as h,a as r,z as j,E as V,d as v}from"./app.5bf25e6f.js";import{J as g}from"./AuthenticationCard.d2e642d8.js";import{J as $}from"./AuthenticationCardLogo.c737ac3b.js";import{_ as k}from"./Button.de389ba7.js";import{_ as J}from"./Input.f95445aa.js";import{_ as C}from"./Label.a34a8f2d.js";import{_ as y}from"./ValidationErrors.40b6029f.js";import{_ as B}from"./plugin-vue_export-helper.21dcd24c.js";const P=c({components:{Head:h,JetAuthenticationCard:g,JetAuthenticationCardLogo:$,JetButton:k,JetInput:J,JetLabel:C,JetValidationErrors:y},props:{email:String,token:String},data(){return{form:this.$inertia.form({token:this.token,email:this.email,password:"",password_confirmation:""})}},methods:{submit(){this.form.post(this.route("password.update"),{onFinish:()=>this.form.reset("password","password_confirmation")})}}}),E={class:"mt-4"},H={class:"mt-4"},q={class:"flex items-center justify-end mt-4"},F=v(" Reset Password ");function N(t,e,R,S,U,z){const l=a("Head"),d=a("jet-authentication-card-logo"),p=a("jet-validation-errors"),n=a("jet-label"),i=a("jet-input"),u=a("jet-button"),f=a("jet-authentication-card");return _(),w(b,null,[o(l,{title:"Reset Password"}),o(f,null,{logo:m(()=>[o(d)]),default:m(()=>[o(p,{class:"mb-4"}),r("form",{onSubmit:e[3]||(e[3]=V((...s)=>t.submit&&t.submit(...s),["prevent"]))},[r("div",null,[o(n,{for:"email",value:"Email"}),o(i,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:t.form.email,"onUpdate:modelValue":e[0]||(e[0]=s=>t.form.email=s),required:"",autofocus:""},null,8,["modelValue"])]),r("div",E,[o(n,{for:"password",value:"Password"}),o(i,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:t.form.password,"onUpdate:modelValue":e[1]||(e[1]=s=>t.form.password=s),required:"",autocomplete:"new-password"},null,8,["modelValue"])]),r("div",H,[o(n,{for:"password_confirmation",value:"Confirm Password"}),o(i,{id:"password_confirmation",type:"password",class:"mt-1 block w-full",modelValue:t.form.password_confirmation,"onUpdate:modelValue":e[2]||(e[2]=s=>t.form.password_confirmation=s),required:"",autocomplete:"new-password"},null,8,["modelValue"])]),r("div",q,[o(u,{class:j({"opacity-25":t.form.processing}),disabled:t.form.processing},{default:m(()=>[F]),_:1},8,["class","disabled"])])],32)]),_:1})],64)}var O=B(P,[["render",N]]);export{O as default};
