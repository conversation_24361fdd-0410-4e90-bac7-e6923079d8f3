<template>
  <Head title="Create Group" />
  <div class="p-2 sm:p-4">
    
    <div class="mb-2 flex justify-start">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('groups')">{{ trans('GroupsList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
         {{ trans('Create') }}
      </h2>
    </div>

    <div class="bg-white rounded-md shadow max-w-2xl">
      <form @submit.prevent="submit2">
        <div class="p-5 -mr-6 -mb-5 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" direction="ltr" class="pr-6 pb-4 w-full" type="text" label="Name" :autofocus="true" />
          <div class="pr-4 pb-0 w-full text-blue-600 font-semibold">
            <span class="p-1">{{trans('Tests')}}:</span>
          </div>
          <div v-if="form.groupTests.length>0" class="ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2">
            <div class="w-full flex-row flex justify-start items-center flex-wrap">
                <div class="p-1 min-w-fit max-w-fit mt-1"  v-for="(test, index) in form.groupTests" :key="index">
                    <label title="Remove Test" @click="removeTest(test.id, test.short_name, index)"  class="pt-0.5 btn-remove border border-green-400" :for="test.short_name">{{index+1}} - {{test.short_name}}</label>
                </div>
            </div>
          </div>
          <div v-else class="ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2">
            <div class="flex flex-wrap">
                <div class="flex items-stretch p-1">There are no attached tests yet.</div>
            </div>
          </div>
          <div v-if="form.errors.tests_count" class="form-error">{{ form.errors.tests_count }}</div>

          <div class="ltr text-left w-full mt-2">
            <dropdown-search class="pr-6 pb-2 w-full sm:w-1/2" :options="ourTests" @select="addTest" 
            :CloseOnSelect="false" :fixedLabel="true" label="Select Test" direction="ltr" searchBy="short_name" placeholder="Search for tests">
            </dropdown-search>
          </div>
          
        </div>
        <div class="ml-6 pb-2 text-red-600" v-if="form.isDirty">There are unsaved form changes.</div>
        <div class="flex px-4 py-2 bg-gray-100 border-t border-gray-100  justify-start items-center">
          <loading-button :loading="form.processing" class="mr-4 flex px-3 py-2 btn-green" @click="submit2">{{trans("CreateAndCreateAnother")}}</loading-button>
          <loading-button :loading="form.processing" class="px-3 py-2 flex btn-green" @click="submit">{{trans('Create') + ' ' + trans('Group')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
 import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import DropdownSearch from '@/MyComponents/DropdownSearch.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { reactive } from '@vue/reactivity'
import { Inertia } from '@inertiajs/inertia'

  const props = defineProps({
    locale: String,
    tests: Array,
  });

  let form = useForm({
    // _token: document.head.querySelector('meta[name="csrf_token"]').content,
    createAnother: false,
    name: null,
    tests_count: null,
    groupTests: [],
  });

  const ourTests = reactive(props.tests);

  let addTest = (selectedTest) => {
    form.groupTests.push({
      id:   selectedTest.id,
      short_name: selectedTest.short_name,
    });
    ourTests.splice(ourTests.findIndex(test => test.id === selectedTest.id), 1);
  };

  let removeTest = (id, short_name, index) => {
    ourTests.push({
        id:   id,
        short_name: short_name,
    });
    form.groupTests.splice(index, 1);
  };

  let submit2 = () => {
    form.createAnother = true;
    store();
  };

  let submit = () => {
    form.createAnother = false;
    store();
  };

  let store = () => {
    form.tests_count = form.groupTests.length;
    form.post(route('groups.store'),{
      preserveState: true,
      onSuccess: () => { 
          form.reset();
          ourTests.splice(0, ourTests.length);
          props.tests.forEach((test) => { 
            ourTests.push({
              id: test.id,
              short_name: test.short_name,
            });
          })
        },
    })
  };

</script>
