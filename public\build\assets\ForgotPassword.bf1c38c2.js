import{n as b,r as o,o as i,c as m,b as e,B as r,F as h,H as w,t as g,C as j,a,z as v,E as y,d as J}from"./app.5bf25e6f.js";import{J as $}from"./AuthenticationCard.d2e642d8.js";import{J as C}from"./AuthenticationCardLogo.c737ac3b.js";import{_ as V}from"./Button.de389ba7.js";import{_ as k}from"./Input.f95445aa.js";import{_ as B}from"./Label.a34a8f2d.js";import{_ as E}from"./ValidationErrors.40b6029f.js";import{_ as F}from"./plugin-vue_export-helper.21dcd24c.js";const N=b({components:{Head:w,JetAuthenticationCard:$,JetAuthenticationCardLogo:C,JetButton:V,JetInput:k,JetLabel:B,JetValidationErrors:E},props:{status:String},data(){return{form:this.$inertia.form({email:""})}},methods:{submit(){this.form.post(this.route("password.email"))}}}),H=a("div",{class:"mb-4 text-sm text-gray-600"}," Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1),L={key:0,class:"mb-4 font-medium text-sm text-green-600"},P={class:"flex items-center justify-end mt-4"},S=J(" Email Password Reset Link ");function x(t,s,z,A,q,D){const l=o("Head"),d=o("jet-authentication-card-logo"),u=o("jet-validation-errors"),c=o("jet-label"),_=o("jet-input"),p=o("jet-button"),f=o("jet-authentication-card");return i(),m(h,null,[e(l,{title:"Forgot Password"}),e(f,null,{logo:r(()=>[e(d)]),default:r(()=>[H,t.status?(i(),m("div",L,g(t.status),1)):j("",!0),e(u,{class:"mb-4"}),a("form",{onSubmit:s[1]||(s[1]=y((...n)=>t.submit&&t.submit(...n),["prevent"]))},[a("div",null,[e(c,{for:"email",value:"Email"}),e(_,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:t.form.email,"onUpdate:modelValue":s[0]||(s[0]=n=>t.form.email=n),required:"",autofocus:""},null,8,["modelValue"])]),a("div",P,[e(p,{class:v({"opacity-25":t.form.processing}),disabled:t.form.processing},{default:r(()=>[S]),_:1},8,["class","disabled"])])],32)]),_:1})],64)}var Q=F(N,[["render",x]]);export{Q as default};
