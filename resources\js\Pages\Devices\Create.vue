<template>
  <Head title="Create Device" />
  <div class="p-4">
    <h2 class="mb-2 font-bold text-2xl">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('devices')">{{ trans('DevicesList') }}</Link>
      <span class="text-indigo-400 font-medium"> / </span> {{ trans('Create') }}
    </h2>
    <div class="bg-white rounded-md shadow overflow-hidden max-w-xl">
      <form @submit.prevent="submit2">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" class="pr-6 pb-8 w-full" type="text" direction="ltr" label="Name" :autofocus="true" />
        </div>
        <div class="flex px-4 py-2 bg-gray-100 border-t border-gray-100  justify-start items-center">
          <loading-button :loading="form.processing" class="mr-4  px-3 py-2 btn-green" @click="submit2">{{trans("CreateAndCreateAnother")}}</loading-button>
          <loading-button :loading="form.processing" class="px-3 py-2  btn-green" @click="submit">{{trans('Create') + ' ' + trans('Device')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
  locale: String,
});

  let form = useForm({
    createAnother: false,
    name: null
  });
 let submit2 = () => {
    form.createAnother = true;
    store();
  };

  let submit = () => {
    form.createAnother = false;
    store();
  };

  let store = () => {
    form.post(route('devices.store'),{
      preserveState: true,
      onSuccess: () => { 
          form.reset();
        },
    })
  };

</script>
