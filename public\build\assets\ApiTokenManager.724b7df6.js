import{_ as J}from"./ActionMessage.eaf60617.js";import{_ as I,a as x,b as M}from"./DialogModal.3b39e601.js";import{_ as V}from"./Button.de389ba7.js";import{o as l,y as L,B as o,a as s,A as k,n as N,r,c as m,b as i,C as c,F as f,D as h,t as p,z as g,d as a}from"./app.5bf25e6f.js";import{_ as z}from"./DangerButton.177534fb.js";import{_ as U}from"./FormSection.3de6ac09.js";import{_ as E}from"./Input.f95445aa.js";import{_ as W}from"./Checkbox.c93fed4a.js";import{_ as Y}from"./InputError.2a9befad.js";import{_ as q}from"./Label.a34a8f2d.js";import{_ as G}from"./SecondaryButton.fcd49457.js";import{J as H}from"./SectionBorder.aab76ee9.js";import{_ as K}from"./plugin-vue_export-helper.21dcd24c.js";const O={class:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4"},Q={class:"sm:flex sm:items-start"},R=s("div",{class:"mx-auto shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10"},[s("svg",{class:"h-6 w-6 text-red-600",stroke:"currentColor",fill:"none",viewBox:"0 0 24 24"},[s("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})])],-1),X={class:"mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left"},Z={class:"text-lg"},ee={class:"mt-2"},oe={class:"flex flex-row justify-end px-6 py-4 bg-gray-100 text-right"},te={__name:"ConfirmationModal",props:{show:{type:Boolean,default:!1},maxWidth:{type:String,default:"2xl"},closeable:{type:Boolean,default:!0}},emits:["close"],setup(e,{emit:n}){const v=()=>{n("close")};return(u,$)=>(l(),L(I,{show:e.show,"max-width":e.maxWidth,closeable:e.closeable,onClose:v},{default:o(()=>[s("div",O,[s("div",Q,[R,s("div",X,[s("h3",Z,[k(u.$slots,"title")]),s("div",ee,[k(u.$slots,"content")])])])]),s("div",oe,[k(u.$slots,"footer")])]),_:3},8,["show","max-width","closeable"]))}},se=N({components:{JetActionMessage:J,JetActionSection:x,JetButton:V,JetConfirmationModal:te,JetDangerButton:z,JetDialogModal:M,JetFormSection:U,JetInput:E,JetCheckbox:W,JetInputError:Y,JetLabel:q,JetSecondaryButton:G,JetSectionBorder:H},props:["tokens","availablePermissions","defaultPermissions"],data(){return{createApiTokenForm:this.$inertia.form({name:"",permissions:this.defaultPermissions}),updateApiTokenForm:this.$inertia.form({permissions:[]}),deleteApiTokenForm:this.$inertia.form(),displayingToken:!1,managingPermissionsFor:null,apiTokenBeingDeleted:null}},methods:{createApiToken(){this.createApiTokenForm.post(route("api-tokens.store"),{preserveScroll:!0,onSuccess:()=>{this.displayingToken=!0,this.createApiTokenForm.reset()}})},manageApiTokenPermissions(e){this.updateApiTokenForm.permissions=e.abilities,this.managingPermissionsFor=e},updateApiToken(){this.updateApiTokenForm.put(route("api-tokens.update",this.managingPermissionsFor),{preserveScroll:!0,preserveState:!0,onSuccess:()=>this.managingPermissionsFor=null})},confirmApiTokenDeletion(e){this.apiTokenBeingDeleted=e},deleteApiToken(){this.deleteApiTokenForm.delete(route("api-tokens.destroy",this.apiTokenBeingDeleted),{preserveScroll:!0,preserveState:!0,onSuccess:()=>this.apiTokenBeingDeleted=null})}}}),ne=a(" Create API Token "),ie=a(" API tokens allow third-party services to authenticate with our application on your behalf. "),ae={class:"col-span-6 sm:col-span-4"},re={key:0,class:"col-span-6"},le={class:"mt-2 grid grid-cols-1 md:grid-cols-2 gap-4"},me={class:"flex items-center"},de={class:"ml-2 text-sm text-gray-600"},ce=a(" Created. "),pe=a(" Create "),ue={key:0},_e={class:"mt-10 sm:mt-0"},ke=a(" Manage API Tokens "),fe=a(" You may delete any of your existing tokens if they are no longer needed. "),he={class:"space-y-6"},ge={class:"flex items-center"},ve={key:0,class:"text-sm text-gray-400"},ye=["onClick"],Te=["onClick"],be=a(" API Token "),Ae=s("div",null," Please copy your new API token. For your security, it won't be shown again. ",-1),$e={key:0,class:"mt-4 bg-gray-100 px-4 py-2 rounded font-mono text-sm text-gray-500"},Fe=a(" Close "),je=a(" API Token Permissions "),Ce={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},we={class:"flex items-center"},Pe={class:"ml-2 text-sm text-gray-600"},Be=a(" Cancel "),Se=a(" Save "),De=a(" Delete API Token "),Je=a(" Are you sure you would like to delete this API token? "),Ie=a(" Cancel "),xe=a(" Delete ");function Me(e,n,v,u,$,Ve){const y=r("jet-label"),F=r("jet-input"),j=r("jet-input-error"),T=r("jet-checkbox"),C=r("jet-action-message"),b=r("jet-button"),w=r("jet-form-section"),P=r("jet-section-border"),B=r("jet-action-section"),_=r("jet-secondary-button"),A=r("jet-dialog-modal"),S=r("jet-danger-button"),D=r("jet-confirmation-modal");return l(),m("div",null,[i(w,{onSubmitted:e.createApiToken},{title:o(()=>[ne]),description:o(()=>[ie]),form:o(()=>[s("div",ae,[i(y,{for:"name",value:"Name"}),i(F,{id:"name",type:"text",class:"mt-1 block w-full",modelValue:e.createApiTokenForm.name,"onUpdate:modelValue":n[0]||(n[0]=t=>e.createApiTokenForm.name=t),autofocus:""},null,8,["modelValue"]),i(j,{message:e.createApiTokenForm.errors.name,class:"mt-2"},null,8,["message"])]),e.availablePermissions.length>0?(l(),m("div",re,[i(y,{for:"permissions",value:"Permissions"}),s("div",le,[(l(!0),m(f,null,h(e.availablePermissions,t=>(l(),m("div",{key:t},[s("label",me,[i(T,{value:t,checked:e.createApiTokenForm.permissions,"onUpdate:checked":n[1]||(n[1]=d=>e.createApiTokenForm.permissions=d)},null,8,["value","checked"]),s("span",de,p(t),1)])]))),128))])])):c("",!0)]),actions:o(()=>[i(C,{on:e.createApiTokenForm.recentlySuccessful,class:"mr-3"},{default:o(()=>[ce]),_:1},8,["on"]),i(b,{class:g({"opacity-25":e.createApiTokenForm.processing}),disabled:e.createApiTokenForm.processing},{default:o(()=>[pe]),_:1},8,["class","disabled"])]),_:1},8,["onSubmitted"]),e.tokens.length>0?(l(),m("div",ue,[i(P),s("div",_e,[i(B,null,{title:o(()=>[ke]),description:o(()=>[fe]),content:o(()=>[s("div",he,[(l(!0),m(f,null,h(e.tokens,t=>(l(),m("div",{class:"flex items-center justify-between",key:t.id},[s("div",null,p(t.name),1),s("div",ge,[t.last_used_ago?(l(),m("div",ve," Last used "+p(t.last_used_ago),1)):c("",!0),e.availablePermissions.length>0?(l(),m("button",{key:1,class:"cursor-pointer ml-6 text-sm text-gray-400 underline",onClick:d=>e.manageApiTokenPermissions(t)}," Permissions ",8,ye)):c("",!0),s("button",{class:"cursor-pointer ml-6 text-sm text-red-500",onClick:d=>e.confirmApiTokenDeletion(t)}," Delete ",8,Te)])]))),128))])]),_:1})])])):c("",!0),i(A,{show:e.displayingToken,onClose:n[3]||(n[3]=t=>e.displayingToken=!1)},{title:o(()=>[be]),content:o(()=>[Ae,e.$page.props.jetstream.flash.token?(l(),m("div",$e,p(e.$page.props.jetstream.flash.token),1)):c("",!0)]),footer:o(()=>[i(_,{onClick:n[2]||(n[2]=t=>e.displayingToken=!1)},{default:o(()=>[Fe]),_:1})]),_:1},8,["show"]),i(A,{show:e.managingPermissionsFor,onClose:n[6]||(n[6]=t=>e.managingPermissionsFor=null)},{title:o(()=>[je]),content:o(()=>[s("div",Ce,[(l(!0),m(f,null,h(e.availablePermissions,t=>(l(),m("div",{key:t},[s("label",we,[i(T,{value:t,checked:e.updateApiTokenForm.permissions,"onUpdate:checked":n[4]||(n[4]=d=>e.updateApiTokenForm.permissions=d)},null,8,["value","checked"]),s("span",Pe,p(t),1)])]))),128))])]),footer:o(()=>[i(_,{onClick:n[5]||(n[5]=t=>e.managingPermissionsFor=null)},{default:o(()=>[Be]),_:1}),i(b,{class:g(["ml-3",{"opacity-25":e.updateApiTokenForm.processing}]),onClick:e.updateApiToken,disabled:e.updateApiTokenForm.processing},{default:o(()=>[Se]),_:1},8,["onClick","class","disabled"])]),_:1},8,["show"]),i(D,{show:e.apiTokenBeingDeleted,onClose:n[8]||(n[8]=t=>e.apiTokenBeingDeleted=null)},{title:o(()=>[De]),content:o(()=>[Je]),footer:o(()=>[i(_,{onClick:n[7]||(n[7]=t=>e.apiTokenBeingDeleted=null)},{default:o(()=>[Ie]),_:1}),i(S,{class:g(["ml-3",{"opacity-25":e.deleteApiTokenForm.processing}]),onClick:e.deleteApiToken,disabled:e.deleteApiTokenForm.processing},{default:o(()=>[xe]),_:1},8,["onClick","class","disabled"])]),_:1},8,["show"])])}var Re=K(se,[["render",Me]]);export{Re as default};
