<template>
<nav class="flex justify-start items-center">
    <Link @click="sidebarOpened = false" v-for="(item, index) in Navigation" :key="index" :href="route(item.route)" class="flex items-center group py-3 mr-1 xl:mr-2"
    :class="$page.url == item.url? 'text-orange-300' : 'text-white group-hover:text-white'">
    <icon :name="item.icon" class="w-5 h-5 mx-1" :class="$page.url == item.url? 'fill-white' :  'fill-indigo-400 group-hover:fill-white'" />{{ trans(item.label) }}
    </Link>
</nav>
</template>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import JetNavLink from '@/Jetstream/NavLink.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
    // errors: Object,
    // doctors_income: Array,
    // start_date: String,
    // end_date: String,
});

let Navigation = [
    {route: 'income_report/index', url:'/statistics/income_report', label: 'Income', icon: 'income'},
    {route: 'doctors_report/index', url:'/statistics/doctors_report', label: 'Doctors', icon: 'physician'},
    {route: 'side_labs_report/index', url:'/statistics/side_labs_report', label: 'SideLabs', icon: 'hospital'},
    {route: 'tests_report/index', url:'/statistics/tests_report', label: 'Tests', icon: 'test'},
    {route: 'offers_report/index', url:'/statistics/offers_report', label: 'Offers', icon: 'offers'},
    {route: 'visits_report/index', url:'/statistics/visits_report', label: 'Visits', icon: 'client'},
    {route: 'visitsDetails', url:'/statistics/visitsDetails', label: 'VisitsDetails', icon: 'client'},
    
];

// let form = useForm({
//   start_date: null,
//   end_date: null,
// });

// let submit = () => {
//   form.post(route('doctors_report/fetch'),{
//     preserveState: true,
//     onSuccess: () => { 
//         form.reset();
//       },
//   })
// };

  let tabletoExcel = () => {
        var uri = 'data:application/vnd.ms-excel;base64,'
              , template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>'
              , base64 = function (s) { return window.btoa(unescape(encodeURIComponent(s))); }
              , format = function (s, c) { return s.replace(/{(\w+)}/g, function (m, p) { return c[p]; }); };
            if (!table.nodeType) table = document.getElementById('table');
            var ctx = { worksheet: 'Patients_Statistics' || 'Worksheet', table: table.innerHTML };
            window.location.href = uri + base64(format(template, ctx));

    };
</script>

