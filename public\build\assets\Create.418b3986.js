import{Q as f,o as _,c as b,b as s,u as e,a,B as m,d as n,t as l,E as h,z as x,F as k,H as C,L as y}from"./app.5bf25e6f.js";import{A as v}from"./AppLayout.14f8c8f6.js";import{_ as w}from"./TextInput.48e8e32c.js";import{L as u}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";const A={class:"p-2 sm:p-4"},T={class:"mb-2 font-bold text-2xl"},L=a("span",{class:"text-indigo-400 font-medium"}," /",-1),V={class:"bg-white rounded-md shadow overflow-hidden max-w-xl"},B={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},S={layout:v},H=Object.assign(S,{__name:"Create",props:{locale:String},setup(p){let t=f({createAnother:!1,name:null}),i=()=>{t.createAnother=!0,c()},g=()=>{t.createAnother=!1,c()},c=()=>{t.post(route("tracking_tags.store"),{preserveState:!0,onSuccess:()=>{t.reset()}})};return(r,o)=>(_(),b(k,null,[s(e(C),{title:"Create Tracking Tag"}),a("div",A,[a("h2",T,[s(e(y),{class:"text-indigo-400 hover:text-indigo-600",href:r.route("tracking_tags")},{default:m(()=>[n(l(r.trans("TrackingTagsList")),1)]),_:1},8,["href"]),L,n(" "+l(r.trans("Create")),1)]),a("div",V,[a("form",{onSubmit:o[1]||(o[1]=h((...d)=>e(i)&&e(i)(...d),["prevent"]))},[a("div",{class:x(["p-8 -mr-6 -mb-8 flex flex-wrap",p.locale=="ar"?"rtl text-right":"ltr text-left"])},[s(w,{modelValue:e(t).name,"onUpdate:modelValue":o[0]||(o[0]=d=>e(t).name=d),error:e(t).errors.name,direction:"ltr",class:"pr-6 pb-8 w-full",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),a("div",B,[s(u,{loading:e(t).processing,class:"mr-4 px-3 py-2 btn-green",onClick:e(i)},{default:m(()=>[n(l(r.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),s(u,{loading:e(t).processing,class:"px-3 py-2 btn-green",onClick:e(g)},{default:m(()=>[n(l(r.trans("Create")+" "+r.trans("TrackingTag")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{H as default};
