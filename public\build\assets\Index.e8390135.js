import{v as x,q as b,o as n,c as u,b as o,u as d,a as e,B as i,t as a,F as w,D as g,C as p,H as y,K as v,R as k,L as c,d as V,y as N,U as j}from"./app.5bf25e6f.js";import{A as B,I as m}from"./AppLayout.14f8c8f6.js";import{t as C,S,p as O}from"./SearchFilter.f110f3d1.js";import{P as I}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const L={class:"p-4 max-w-7xl"},R={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},T=e("label",{class:"block text-gray-700"},"Trashed:",-1),U=e("option",{value:null},null,-1),A={value:"with"},D={value:"only"},F={class:"inline sm:hidden"},E={class:"hidden sm:flex w-48 text-center"},H={class:"shadow overflow-x-auto rounded-lg bg-white"},P={class:"min-w-full divide-y divide-gray-200"},q={class:"bg-gray-50"},K={class:"bg-green-500"},M={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},G={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={class:"border-t p-0"},Q={class:"px-6 py-2 flex items-center"},X={class:"ml-4"},Y={class:"flex items-center text-sm font-medium text-gray-900"},Z={class:"border-t p-0"},$={class:"px-6 py-2"},ee={class:"border-t p-0"},te={class:"px-6 py-2"},se={class:"border-t p-0"},ae={class:"px-6 py-2"},oe={key:0},re=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No results found.",-1),le=[re],de=e("hr",{class:"bg-gray-300 pt-px"},null,-1),ie={layout:B},me=Object.assign(ie,{__name:"Index",props:{filters:Object,results:Object},setup(r){const f=r,l=x({search:f.filters.search,trashed:f.filters.trashed});b(l,C(function(){j.Inertia.get(route("results"),O(l),{preserveState:!0,replace:!0})},300),{deep:!0});function _(){Object.assign(l,{search:null,trashed:""})}return(t,h)=>(n(),u("div",L,[o(d(y),{title:"Results"}),e("div",R,[o(S,{modelValue:l.search,"onUpdate:modelValue":h[1]||(h[1]=s=>l.search=s),direction:"ltr",placeholder:"Search results...",class:"mb-2 sm:mb-0 w-full",onReset:_},{default:i(()=>[T,v(e("select",{"onUpdate:modelValue":h[0]||(h[0]=s=>l.trashed=s),class:"mt-1 w-full form-select"},[U,e("option",A,a(t.trans("WithTrashed")),1),e("option",D,a(t.trans("OnlyTrashed")),1)],512),[[k,l.trashed]])]),_:1},8,["modelValue"]),o(d(c),{class:"w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium",href:t.route("results.create")},{default:i(()=>[e("span",F,a(t.trans("Create")),1),e("span",E,a(t.trans("CreateANewResult")),1)]),_:1},8,["href"])]),e("div",H,[e("table",P,[e("thead",q,[e("tr",K,[e("th",M,a(t.trans("Name")),1),e("th",W,a(t.trans("Created_at")),1),e("th",z,a(t.trans("Updated_at")),1),e("th",G,a(t.trans("Edit")),1)])]),(n(!0),u(w,null,g(r.results.data,s=>(n(),u("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",J,[o(d(c),{href:t.route("results.edit",s.id),tabindex:"-1"},{default:i(()=>[e("div",Q,[e("div",X,[e("div",Y,[V(a(s.name)+" ",1),s.deleted_at?(n(),N(m,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):p("",!0)])])])]),_:2},1032,["href"])]),e("td",Z,[o(d(c),{href:t.route("results.edit",s.id),tabindex:"-1"},{default:i(()=>[e("div",$,a(s.created_at),1)]),_:2},1032,["href"])]),e("td",ee,[o(d(c),{href:t.route("results.edit",s.id),tabindex:"-1"},{default:i(()=>[e("div",te,a(s.updated_at),1)]),_:2},1032,["href"])]),e("td",se,[o(d(c),{class:"flex items-center text-right text-sm font-medium",href:t.route("results.edit",s.id),tabindex:"-1"},{default:i(()=>[e("div",ae,[o(m,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),r.results.data.length===0?(n(),u("tr",oe,le)):p("",!0)]),de,o(I,{class:"px-6 py-2 bg-white border-none border-t p-0",links:r.results.links,from:r.results.from,to:r.results.to,total:r.results.total},null,8,["links","from","to","total"])])]))}});export{me as default};
