<template>
<Head title="Edit Device" />
<div class="p-4">
    <div class="mb-2 flex justify-start max-w-xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('devices')">{{ trans('DevicesList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
        {{ form.name }}
      </h2>
    </div>
  
    <div class="bg-white rounded-md shadow-sm overflow-hidden max-w-xl">
      <trashed-message v-if="device.deleted_at" class="mb-6" @restore="restore">
      {{ trans('This') + ' ' +  trans('Device') + ' ' + trans('HasBeenDeleted')}}
    </trashed-message>
      <form @submit.prevent="update">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input   v-model="form.name" :error="form.errors.name" type="text" class="pr-6 pb-8 w-full" direction="ltr" label="Name"  :autofocus="true"/>
        </div>
        <div class="px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
          <loading-button :loading="form.processing" class="btn-green" type="submit">{{trans('Update') + ' ' + trans('Device')}}</loading-button>
          <button v-if="!device.deleted_at" class="text-red-600 hover:underline" tabindex="-1" type="button" @click="destroy">{{trans('Delete') + ' ' + trans('Device')}}</button>
        </div>
      </form>
    </div>

    <hr class="border-2">

    <div class="shadow overflow-x-auto rounded-lg bg-white max-w-xl mt-4">
      <table class="w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-green-500">
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('RelatedTests') }}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Edit')}}	</th>
          </tr>
        </thead>

        <tr v-for="test in device.tests" :key="test.id" class="hover:bg-gray-100 focus-within:bg-gray-100">
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2 flex items-center">
                {{ test.short_name }}
            </div>
            </Link>
          </td>
          
          <td class="border-t p-0">
            <Link class="flex items-center  text-right text-sm font-medium" :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">
              <icon name="cheveron-right" class="block w-6 h-6 fill-gray-400" />
            </div>
            </Link>
          </td>
        </tr>
        <tr v-if="device.tests.length === 0">
          <td class="border-t p-0 px-6 py-2" colspan="4">No tests found.</td>
        </tr>
      </table>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
    device: Object,
    locale: String,
});

let form = useForm({
    _method: 'put',
    name: props.device.name,
});

let update = () => {
    form.post(route('devices.update', props.device.id), {
    onSuccess: () => form.reset('name'),
    })
};

let destroy = () => {
    if (confirm('Are you sure you want to delete this device?')) {
    Inertia.delete(route('devices.destroy', props.device.id))
    }
};

let restore = () => {
    if (confirm('Are you sure you want to restore this device?')) {
    Inertia.put(route('devices.restore', props.device.id))
    }
};

</script>