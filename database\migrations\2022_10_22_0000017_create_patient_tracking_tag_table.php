<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePatientTrackingTagTable extends Migration
{
    public function up()
    {
        Schema::enableForeignKeyConstraints();
        Schema::create('patient_tracking_tag', function (Blueprint $table) {
            $table->id();

            $table->foreignId('tracking_tag_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('restrict')->onUpdate('cascade');

            $table->timestamps();
        });
    }
    public function down()
    {
        Schema::dropIfExists('patient_tracking_tag');
    }
}
