import{v as y,q as g,o as r,c as p,b as i,u as n,a as t,B as d,y as _,C as f,t as o,F as v,D as k,H as V,K as m,R as x,L as h,d as S,U}from"./app.5bf25e6f.js";import{A as j,I as b}from"./AppLayout.14f8c8f6.js";import{t as A,S as N,p as R}from"./SearchFilter.f110f3d1.js";import{P as B}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const C={class:"p-1 sm:p-4 xl:px-8 xl:py-6 max-w-7xl"},O={class:"flex flex-col sm:flex-row mb-6 justify-between items-center"},I=t("label",{class:"block text-gray-700"},"Trashed:",-1),L=t("option",{value:null},null,-1),T={value:"with"},D={value:"only"},F=t("label",{class:"block text-gray-700"},"Role:",-1),E=t("option",{value:null},null,-1),H=t("option",{value:"Administrator"},"Administrator",-1),P=t("option",{value:"Reception_staff"},"Reception Staff",-1),q=[E,H,P],K={class:"inline sm:hidden"},M={class:"hidden sm:flex w-48 text-center"},W={class:"shadow overflow-x-auto rounded-lg bg-white"},$={class:"min-w-full divide-y divide-gray-200"},z={class:"bg-gray-50"},G={class:"bg-green-600"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},X={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Y={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},tt={class:"border-t p-0"},et={class:"px-6 py-2 flex items-center"},st={class:"flex-shrink-0 h-10 w-10"},ot=["src"],at={class:"ml-4"},lt={class:"flex items-center text-sm font-medium text-gray-900"},rt={class:"text-sm text-gray-500"},it={class:"border-t p-0"},nt={class:"px-6 py-2"},dt={class:""},ct={class:"border-t p-0"},ht={class:"px-6 py-2"},pt={class:"border-t p-0"},ft={class:"px-6 py-2"},ut={class:"border-t p-0"},_t={class:"px-6 py-2"},mt={key:0},xt=t("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No users found.",-1),bt=[xt],wt=t("hr",{class:"bg-gray-300 pt-px"},null,-1),yt={layout:j},jt=Object.assign(yt,{__name:"Index",props:{filters:Object,users:Object,can:Object},setup(l){const u=l,a=y({search:u.filters.search,trashed:u.filters.trashed,role:u.filters.role});g(a,A(function(){U.Inertia.get(route("users"),R(a),{preserveState:!0,replace:!0})},300),{deep:!0});function w(){Object.assign(a,{search:null,trashed:""})}return(s,c)=>(r(),p("div",C,[i(n(V),{title:"Users"}),t("div",O,[i(N,{modelValue:a.search,"onUpdate:modelValue":c[2]||(c[2]=e=>a.search=e),direction:"ltr",placeholder:"Search by name or email...",class:"mb-2 sm:mb-0 w-full",onReset:w},{default:d(()=>[I,m(t("select",{"onUpdate:modelValue":c[0]||(c[0]=e=>a.trashed=e),class:"mt-1 w-full form-select"},[L,t("option",T,o(s.trans("WithTrashed")),1),t("option",D,o(s.trans("OnlyTrashed")),1)],512),[[x,a.trashed]]),F,m(t("select",{"onUpdate:modelValue":c[1]||(c[1]=e=>a.role=e),class:"mt-1 w-full form-select"},q,512),[[x,a.role]])]),_:1},8,["modelValue"]),l.can.createUser?(r(),_(n(h),{key:0,class:"w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium",href:s.route("users.create")},{default:d(()=>[t("span",K,o(s.trans("Create")),1),t("span",M,o(s.trans("CreateANewUser")),1)]),_:1},8,["href"])):f("",!0)]),t("div",W,[t("table",$,[t("thead",z,[t("tr",G,[t("th",J,o(s.trans("Name")),1),t("th",Q,o(s.trans("Role")),1),t("th",X,o(s.trans("Created_at")),1),t("th",Y,o(s.trans("Updated_at")),1),t("th",Z,o(s.trans("Edit")),1)])]),(r(!0),p(v,null,k(l.users.data,e=>(r(),p("tr",{key:e.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[t("td",tt,[i(n(h),{href:s.route("users.edit",e.id),tabindex:"-1"},{default:d(()=>[t("div",et,[t("div",st,[e.photo?(r(),p("img",{key:0,class:"h-10 w-10 rounded-full",src:`/storage/${e.photo}`,alt:""},null,8,ot)):f("",!0)]),t("div",at,[t("div",lt,[S(o(e.name)+" ",1),e.deleted_at?(r(),_(b,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):f("",!0)]),t("div",rt,o(e.email),1)])])]),_:2},1032,["href"])]),t("td",it,[i(n(h),{class:"text-sm",href:s.route("users.edit",e.id),tabindex:"-1"},{default:d(()=>[t("div",nt,[t("span",dt,o(e.role==="Administrator"?"Administrator":"Reception Staff"),1)])]),_:2},1032,["href"])]),t("td",ct,[i(n(h),{href:s.route("users.edit",e.id),tabindex:"-1"},{default:d(()=>[t("div",ht,o(e.created_at),1)]),_:2},1032,["href"])]),t("td",pt,[i(n(h),{href:s.route("users.edit",e.id),tabindex:"-1"},{default:d(()=>[t("div",ft,o(e.updated_at),1)]),_:2},1032,["href"])]),t("td",ut,[e.can.edit?(r(),_(n(h),{key:0,class:"flex items-center text-right text-sm font-medium",href:s.route("users.edit",e.id),tabindex:"-1"},{default:d(()=>[t("div",_t,[i(b,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])):f("",!0)])]))),128)),l.users.data.length===0?(r(),p("tr",mt,bt)):f("",!0)]),wt,i(B,{class:"px-6 py-2 bg-white border-none border-t p-0",links:l.users.links,from:l.users.from,to:l.users.to,total:l.users.total},null,8,["links","from","to","total"])])]))}});export{jt as default};
