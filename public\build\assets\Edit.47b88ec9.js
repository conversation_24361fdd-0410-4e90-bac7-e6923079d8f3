import{Q as S,v as B,o as a,c as d,b as u,u as e,a as r,B as f,d as p,t as l,y,C as h,E as L,z as D,F as b,D as V,H as j,L as A,U as x}from"./app.5bf25e6f.js";import{A as G}from"./AppLayout.14f8c8f6.js";import{_ as N}from"./TextInput.48e8e32c.js";import{D as U}from"./DropdownSearch.7a091d54.js";import{L as v}from"./LoadingButton.c8fb65b2.js";import{T as E}from"./TrashedMessage.5487e7e2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const F={class:"p-2 sm:p-4"},H={class:"mb-2 flex justify-start"},I={class:"font-bold text-2xl"},O=r("span",{class:"text-indigo-400 font-medium"}," /",-1),R={class:"bg-white rounded-md shadow max-w-2xl"},z={class:"pr-4 pb-0 w-full text-blue-600 font-semibold"},M={class:"p-1"},$={key:0,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},Q={class:"w-full flex-row flex justify-start items-center flex-wrap"},q=["onClick","for"],J={key:1,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},K=r("div",{class:"flex flex-wrap"},[r("div",{class:"flex items-stretch p-1"},"There are no attached tests yet.")],-1),P=[K],W={key:2,class:"form-error"},X={class:"ltr text-left w-full mt-2"},Y={key:0,class:"ml-6 pb-2 text-red-600"},Z={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-between items-center"},ee={layout:G},ue=Object.assign(ee,{__name:"Edit",props:{locale:String,tests:Array,group:Object},setup(c){const i=c;let s=S({name:i.group.name,tests_count:i.group.tests_count,groupTests:i.group.groupTests});const m=B(i.tests);let w=t=>{s.groupTests.push({id:t.id,short_name:t.short_name}),m.splice(m.findIndex(n=>n.id===t.id),1)},k=(t,n,o)=>{m.push({id:t,short_name:n}),s.groupTests.splice(o,1)},g=()=>{s.tests_count=s.groupTests.length,s.put(route("groups.update",i.group.id),{preserveState:!0})},T=()=>{confirm("Are you sure you want to delete this group?")&&x.Inertia.delete(route("groups.destroy",i.group.id))},C=()=>{confirm("Are you sure you want to restore this group?")&&x.Inertia.put(route("groups.restore",i.group.id))};return(t,n)=>(a(),d(b,null,[u(e(j),{title:"Update Group"}),r("div",F,[r("div",H,[r("h2",I,[u(e(A),{class:"text-indigo-400 hover:text-indigo-600",href:t.route("groups")},{default:f(()=>[p(l(t.trans("GroupsList")),1)]),_:1},8,["href"]),O,p(" "+l(e(s).name),1)])]),r("div",R,[c.group.deleted_at?(a(),y(E,{key:0,class:"mb-6",onRestore:e(C)},{default:f(()=>[p(l(t.trans("This")+" "+t.trans("Group")+" "+t.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):h("",!0),r("form",{onSubmit:n[1]||(n[1]=L((...o)=>e(g)&&e(g)(...o),["prevent"]))},[r("div",{class:D(["p-5 -mr-6 -mb-5 flex flex-wrap",c.locale=="ar"?"rtl text-right":"ltr text-left"])},[u(N,{modelValue:e(s).name,"onUpdate:modelValue":n[0]||(n[0]=o=>e(s).name=o),error:e(s).errors.name,direction:"ltr",class:"pr-6 pb-4 w-full",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),r("div",z,[r("span",M,l(t.trans("Tests"))+":",1)]),e(s).groupTests.length>0?(a(),d("div",$,[r("div",Q,[(a(!0),d(b,null,V(e(s).groupTests,(o,_)=>(a(),d("div",{class:"p-1 min-w-fit max-w-fit mt-1",key:_},[r("label",{title:"Remove Test",onClick:te=>e(k)(o.id,o.short_name,_),class:"pt-0.5 btn-remove border border-green-400",for:o.short_name},l(_+1)+" - "+l(o.short_name),9,q)]))),128))])])):(a(),d("div",J,P)),e(s).errors.tests_count?(a(),d("div",W,l(e(s).errors.tests_count),1)):h("",!0),r("div",X,[u(U,{class:"pr-6 pb-2 w-full sm:w-1/2",options:m,onSelect:e(w),CloseOnSelect:!1,fixedLabel:!0,label:"Select Test",searchBy:"short_name",placeholder:"Search for tests"},null,8,["options","onSelect"])])],2),e(s).isDirty?(a(),d("div",Y,"There are unsaved form changes.")):h("",!0),r("div",Z,[u(v,{loading:e(s).processing,class:"mr-4 px-3 py-2 flex btn-green",onClick:e(g)},{default:f(()=>[p(l(t.trans("Update")+" "+t.trans("Group")),1)]),_:1},8,["loading","onClick"]),c.group.deleted_at?h("",!0):(a(),y(v,{key:0,class:"mr-4 text-red-600 px-3 py-2 flex",onClick:e(T)},{default:f(()=>[p(l(t.trans("Delete")+" "+t.trans("Group")),1)]),_:1},8,["onClick"]))])],32)])])],64))}});export{ue as default};
