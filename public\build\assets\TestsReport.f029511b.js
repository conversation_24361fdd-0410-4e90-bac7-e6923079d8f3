import{Q as g,o as i,c as m,b as p,u as n,a as t,E as b,t as e,K as _,S as h,B as y,d,z as w,F as T,D as v,H as S}from"./app.5bf25e6f.js";import{_ as k}from"./StatisticsNav.0926be50.js";import{A as D}from"./AppLayout.14f8c8f6.js";import{L as A}from"./LoadingButton.c8fb65b2.js";import{u as R,w as B,a as E}from"./xlsx.0799a57e.js";import{h as F}from"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const M={class:"p-2 xl:p-3"},C={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4"},V={class:"flex flex-wrap"},L={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},N={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},Y={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},j={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},z={id:"reportToPrint",class:"flex overflow-hidden max-w-3xl"},H={class:"flex w-full"},O={id:"table",class:"w-full shadow-lg"},P={class:"ttr"},U={colspan:"6",class:"tth text-center"},$={key:0},G={class:"text-yellow-500"},I={class:"text-yellow-500"},K=d(" )"),Q={key:1},q=d("( "),J={class:"text-yellow-500"},W=d(" )"),X={class:"ttr"},Z={class:"ttr"},tt={colspan:"1",class:"tth text-center"},et={colspan:"1",class:"tth text-center"},st={class:"tth text-left"},ot={class:"ttd bg-gray-300"},rt={layout:D},pt=Object.assign(rt,{__name:"TestsReport",props:{errors:Object,locale:String,ReportDateAndTime:String,start_date:String,end_date:String,tests_array:Array},setup(a){let l=g({start_date:null,end_date:null}),u=()=>{l.post(route("tests_report/fetch"),{preserveState:!0,onSuccess:()=>{}})},x=()=>{setTimeout(function(){window.print()},50)},f=(s,r)=>{var o=document.getElementById("table"),c=R.table_to_book(o,{sheet:"sheet1"});return r?B(c,{bookType:type,bookSST:!0,type:"base64"}):E(c,s||"MySheetName."+(type||"xlsx"))};return(s,r)=>(i(),m("div",M,[p(n(S),{title:"TestsReport"}),t("div",C,[t("form",{onSubmit:r[4]||(r[4]=b((...o)=>n(u)&&n(u)(...o),["prevent"]))},[t("div",V,[t("div",L,[t("span",N,e(s.trans("From"))+": ",1),_(t("input",{"onUpdate:modelValue":r[0]||(r[0]=o=>n(l).start_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,n(l).start_date]])]),t("div",Y,[t("span",j,e(s.trans("To"))+": ",1),_(t("input",{"onUpdate:modelValue":r[1]||(r[1]=o=>n(l).end_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,n(l).end_date]])]),p(A,{loading:n(l).processing,type:"submit",class:"btn-indigo flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3"},{default:y(()=>[d(e(s.trans("Go")),1)]),_:1},8,["loading"]),t("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:r[2]||(r[2]=o=>n(f)("TestsReport.xlsx"))},e(s.trans("ExportToExcel")),1),t("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:r[3]||(r[3]=(...o)=>n(x)&&n(x)(...o))},e(s.trans("Print")),1)])],32),p(k)]),t("div",z,[t("div",H,[t("table",O,[t("tr",P,[t("th",U,[d(e(s.trans("TestsReport"))+" - ",1),a.start_date&&a.end_date?(i(),m("span",$,[d("( "+e(s.trans("From"))+" ",1),t("span",G,e(a.start_date),1),d(" "+e(s.trans("To"))+" ",1),t("span",I,e(a.end_date),1),K])):(i(),m("span",Q,[q,t("span",J,e(s.trans("ThisMonth")),1),W]))])]),t("tr",X,[t("th",{class:w(["tth",a.locale=="ar"?"rtl text-right":"ltr text-left"]),locale:"",colspan:"3"},e(s.trans("ReportDateAndTime"))+": "+e(n(F)(a.ReportDateAndTime).format("YYYY-MM-DD h:m A")),3)]),t("tr",Z,[t("th",tt,e(s.trans("Test")),1),t("th",et,e(s.trans("Count")),1)]),(i(!0),m(T,null,v(a.tests_array,(o,c)=>(i(),m("tr",{class:"ttr",key:o.name},[t("th",st,e(c+1+" - "+o.name),1),t("td",ot,e(o.count),1)]))),128))])])])]))}});export{pt as default};
