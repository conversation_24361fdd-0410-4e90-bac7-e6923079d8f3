<template>
  <Head title="Create Doctor" />
  <div class="p-2 sm:p-4">
    <h2 class="mb-2 font-bold text-2xl">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('doctors')">{{ trans('DoctorsList') }}</Link>
      <span class="text-indigo-400 font-medium"> /</span> {{ trans('Create') }}
    </h2>
    <div class="bg-white rounded-md shadow overflow-hidden max-w-xl">
      <form @submit.prevent="submit2">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" direction="rtl" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Name" :autofocus="true" />

          <select-input v-model="form.gender" :error="form.errors.gender" class="pr-6 pb-8 w-full lg:w-1/2" label="Gender">
            <option :value='null'></option>
            <option value="1">Male</option>
            <option value="2">Female</option>
          </select-input>

          <text-input v-model="form.mobile" :error="form.errors.mobile" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Mobile" />
          <text-input v-model="form.specialty" :error="form.errors.specialty" direction="rtl" class="pr-6 pb-8 w-full lg:w-1/2" type="text" label="Specialty" />
        </div>
        <div class="flex px-4 py-2 bg-gray-100 border-t border-gray-100  justify-start items-center">
          <loading-button :loading="form.processing" class="mr-4 px-3 py-2 btn-green" @click="submit2">{{trans("CreateAndCreateAnother")}}</loading-button>
          <loading-button :loading="form.processing" class="px-3 py-2 btn-green" @click="submit">{{trans('Create') + ' ' + trans('Doctor')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
  locale: String,
});

  let form = useForm({
    createAnother: false,
    name: null,
    gender: null,
    mobile: null,
    specialty: null,
  });

 let submit2 = () => {
    form.createAnother = true;
    store();
  };

  let submit = () => {
    form.createAnother = false;
    store();
  };

  let store = () => {
    form.post(route('doctors.store'),{
      preserveState: true,
      onSuccess: () => { 
          form.reset();
        },
    })
  };
 

</script>
