<template>
  <Head title="Print Result" />
  <div v-if="isPrinting" id="section-to-print" class="w-full tracking-wide bg-white font-Scheherazade">
    <!-- <hr class="border border-blue-300"> -->
      <header class="z-30">
        <img :src="'/storage/'+header_photo_path" class="w-full h-full border-b-2 border-blue-600">
        <!-- <hr class="border border-blue-500"> -->
        <div class="grid grid-cols-12 items-center justify-between mt-1 text-xl font-semibold text-gray-800 py-1 rtl px-6">
          <div v-if="visit.doctor" class="col-span-6 text-right">
            <span v-if="visit.doctor.gender == 1"> الدكتور : </span>
            <span v-if="visit.doctor.gender == 2"> الدكتورة : </span>
            <span> {{ visit.doctor.name }} </span> 
            <span v-if="visit.doctor.gender == 1"> المحترم </span>
            <span v-if="visit.doctor.gender == 2"> المحترمة </span>
          </div>
          <div v-else class="col-span-6 text-right">
            <span> الدكتور : </span>
          </div>
          <div class="col-span-6 text-left">
            Visit Date: {{moment(visit.created_at).format('YYYY/MM/DD h:mm a')}}
          </div>
          <div class="col-span-5 text-right">
              اسم المراجع  :  {{ visit.patient.name}}
          </div>

          <div class="col-span-2 text-right">
            <span v-if="visit.patient.age>0">العمر : </span>
            <span v-if="visit.patient.age>0">{{ visit.patient.age }} </span>
            <span v-if="visit.patient.age>0 && visit.patient.age_type == 3"> سنة </span>
            <span v-if="visit.patient.age>0 && visit.patient.age_type == 2"> شهر </span>
            <span v-if="visit.patient.age>0 && visit.patient.age_type == 1"> يوم </span>

              <!-- العمر  :  {{ visit.patient.age + ' ' + visit.patient.age_type}} -->
              <!-- العمر  :  {{ visit.patient.age + ' ' + visit.patient.age_type}} -->
          </div>

          <!-- <option :value="3" selected>{{trans('Year')}}</option>
          <option :value="2">{{trans('Month')}}</option>
          <option :value="1">{{trans('Day')}}</option> -->

          <div class="col-span-5 text-left">
            Print Date: {{moment().format('YYYY/MM/DD h:mm a')}}
          </div>
        </div>
      </header>
      <!-- <hr class="border border-blue-200"> -->
      <div class="min-w-full z-20 px-6 mt-3" style="height: fit-content;">
        <div class="text-xl m-0" v-for="cat in categoriesForPrint" :key="cat.category_name">
          <table class="w-full text-center content-block" v-if="cat.printable">
            <!-- <tr class="min-w-max flex text-xl justify-start items-center mt-2 font-semibold">
                <td class="px-4 py-1 bg-yellow-100 shadow-sm rounded-sm text-pink-700">
                  <span>{{cat.category_name}} </span>
                  <span v-if="cat.device">  By - {{cat.device}}</span> 
                </td>
            </tr> -->

            <tr class="whitespace-nowrap text-xl mt-1 p-0">
              <th colspan="4" class="px-0 text-left rounded-sm text-pink-700">
                <div v-if="cat.device" class="bg-yellow-100 w-auto max-w-fit px-2 py-0">{{cat.category_name}} By - {{cat.device}}</div>
                <div v-else class="bg-yellow-100 w-auto max-w-fit px-2 py-0">{{cat.category_name}} </div>
              </th>
            </tr>
            <tr colspan="4" class="rounded-sm text-indigo-700" style="background-color: #8EECF0; min-width: 100%;"> 
              <th colspan="1" class="w-4/12 pr-1 pl-4 py-1 text-left font-semibold">Test</th>
              <th colspan="1" class="w-2/12 px-1 py-1 text-left font-semibold">Result</th> 
              <th colspan="1" class="w-2/12 px-1 py-1 text-left font-semibold" v-if="showFlags"><span> </span></th> 
              <th colspan="1" class="w-6/12 px-1 py-1 text-left font-semibold"> Normal Range</th> 
            </tr>
            <tr colspan="4" v-for="(test, index) in cat.category_tests" class="content-block text-2xl" :key="index" :class="{ 'border-b border-indigo-200' : index != cat.category_tests.length -1  }">

              <td colspan="1" v-if="test.isPrintable" class="w-4/12 min-w-fit px-1 py-1 text-left text-xl" :class="allowTextWrap? '' : 'whitespace-nowrap'">
                <span class="">&#10148;</span> {{ test.short_name}}
              </td>

              <td colspan="1" v-if="test.isPrintable" class="w-2/12 px-1 py-3 text-left text-xl font-semibold">
                <span v-if="test.result" class="border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap" :style="{'color': `${test.color}`}">{{test.result}}
                </span>
              </td>

              <td v-if="showFlags && test.isPrintable" colspan="1" class="w-2/12  px-1 py-3 text-left text-xl font-semibold">
                <span v-if="test.flag_id" class="border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap">
                  {{ourFlags[ourFlags.findIndex(ourFlag => ourFlag.id === test.flag_id)].name}}
                </span>
              </td>

              <td colspan="1" v-if="test.isPrintable" class="w-6/12 px-1 py-1 text-left text-sm font-semibold ql-editor">
              <span v-if="test.normal_range" v-html="test.normal_range.pivot.normal_range">
                </span>
              </td>

            </tr>
          </table>
          
        </div>
        <hr class="border border-indigo-300">
        <p  v-if="showNotes"  class="w-full font-Amiri py-1 px-3 bg-orange-100 mt-2 text-gray-900 shadow-md" v-html="note"></p>
      </div>

      <footer class="z-10">
        <img :src="'/storage/'+footer_photo_path" class="">
      </footer>
  </div>

  <div v-if="!isPrinting" class="px-2 py-2 max-w-4xl ml-4 mt-4 tracking-wide bg-white">
    <div class="flex items-center justify-between pb-1 w-full">
      <span v-if="visit.isItTodysVisit"  class="bg-purple-500 text-gray-50 px-1 py-px rounded-sm text-lg font-normal">Visit date: {{moment(visit.created_at).format('YYYY/MM/DD')}}</span>
      <span v-if="!visit.isItTodysVisit" class="bg-red-500 text-yellow-200 px-1 py-0.5 rounded-sm">Visit date: {{moment(visit.created_at).format('YYYY/MM/DD')}}</span>
      <span v-if="!visit.isItTodysVisit" class="text-orange-500 px-2 py-0.5">{{trans('NotTodysVisit')}}</span>
      <h1 class="bg-purple-500 text-gray-50 px-1 py-px rounded-sm text-lg font-normal"> {{visit.patient.name}} </h1>
    </div>
    <table class="min-w-full w-full border-blue-400 h-full mt-2 overflow-hidden" v-for="cat in categoriesForPrint" :key="cat.category_name" style="width: 100%;">
      <tr class="bg-green-500 w-full p-px">
        <div class="min-w-max flex text-xl justify-start items-center text-orange-600">
          <input class="p-2 mx-2" v-model="cat.printable" :checked="cat.printable" type="checkbox" />
          <span class="p-2">{{cat.category_name}} 
            <!-- <span v-if="cat.id != 15 && cat.id != 10">Tests</span> -->
              By - 
          </span>
          <select class="px-2 py-0.5" v-model="cat.device">
            <option :value="null" />
            <option v-for="device in ourDevices" :key="device.name" :value="device.name"> {{ device.name }}</option>
          </select>
        </div>
      </tr>
      <div class="w-full" v-if="cat.printable">
        <tr class="text-xl bg-cyan-300 rounded-sm text-black font-extrabold"> 
          <th class="w-4/12 px-1 py-1 text-left font-semibold">Test</th>
          <th class="w-2/12 px-1 py-1 text-left font-semibold">Result</th> 
          <th class="w-36   px-1 py-1 text-left font-semibold">Color</th> 
          <th class="w-2/12 px-1 py-1 text-left font-semibold" v-if="showFlags"><span> </span></th> 
          <th class="w-6/12 px-1 py-1 text-left font-semibold"> Normal Range</th> 
        </tr>
        <!-- <tr v-for="(test, index) in cat.category_tests" class="content-block text-2xl" :key="index" :class="{ 'border-b border-gray-200' : index != cat.category_tests.length -1  }" style="min-width: 56rem/* 896px */;">
          <td colspan="1" v-if="test.isPrintable" class="w-4/12 px-1 py-1 text-left text-2xl font-semibold">
            <span class="">&#10148;</span> {{ test.short_name}}
          </td>
          <td colspan="1" v-if="test.isPrintable" class="w-2/12 px-1 py-3 text-left text-xl font-semibold">
            <span v-if="test.result" class="border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap" :style="{'color': `${test.color}`}">{{test.result}}
            </span>
          </td>
          <td class="w-1/12 p-0.5 text-left text-sm font-semibold">
            <input type="color" v-model="test.color" />
          </td>
          <td v-if="showFlags && test.isPrintable" colspan="1" class="w-2/12  px-1 py-3 text-left text-xl font-semibold">
            <span v-if="test.flag_id" class="border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap">
              {{ourFlags[ourFlags.findIndex(ourFlag => ourFlag.id === test.flag_id)].name}}
            </span>
          </td>

          <td colspan="1" v-if="test.isPrintable" class="w-6/12 px-1 py-1 text-left text-sm font-semibold ql-editor">
          <span v-if="test.normal_range" v-html="test.normal_range.pivot.normal_range">
            </span>
          </td>

        </tr> -->
        <tr v-for="(test, index) in cat.category_tests" class="content-block text-2xl" :key="index" :class="{ 'border-b border-indigo-200' : index != cat.category_tests.length -1  }" style="min-width: 56rem/* 896px */;">
          <td colspan="1" class="w-4/12 px-1 py-1 text-left text-2xl font-semibold" :class="allowTextWrap? '' : 'whitespace-nowrap'">
            <input class="p-2 mx-2" v-model="test.isPrintable" :checked="test.isPrintable" type="checkbox" />
            <span class="text-xl">&#10148;</span> {{ test.short_name}}
          </td>
          <td colspan="1" class="w-2/12 px-1 py-3 text-left text-xl font-semibold">
            <span v-if="test.result" class="border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap" :style="{'color': `${test.color}`}">{{test.result}}
            </span>
          </td>
          <td class="w-1/12 p-0.5 text-left text-sm font-semibold">
            <input type="color" v-model="test.color" />
          </td>
          <td v-if="showFlags" colspan="1" class="w-2/12 px-1 py-3 text-left text-xl font-semibold">
            <span v-if="test.flag_id" class="border rounded-sm shadow-md bg-orange-100 px-2 py-0 whitespace-nowrap">
              {{ourFlags[ourFlags.findIndex(ourFlag => ourFlag.id === test.flag_id)].name}}
            </span>
          </td>
          <td colspan="1" class="w-6/12 px-1 py-1 text-left text-sm font-semibold ql-editor">
            <span v-if="test.normal_range" v-html="test.normal_range.pivot.normal_range"></span>
          </td>
        </tr>
      </div>
    </table>
    <div class="flex justify-start items-center space-x-3 px-4 py-2 font-Roboto bg-green-500">
      <button class="btn-indigo cursor-pointer" @click="print">{{ trans("Print")}}</button>
      
      <input id="flag" v-model="showFlags" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
      <label for="flag" class="text-sm text-gray-900">{{trans("Flags")}}</label>
      <input id="Notes" v-model="showNotes" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
      <label for="Notes" class="text-sm text-gray-900">{{trans("Notes")}}</label>
      <input id="TextWrap" v-model="allowTextWrap" type="checkbox" class=" text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
      <label for="TextWrap" class="text-sm text-gray-900">{{trans("AllowTextWrap")}}</label>

      <Link class="btn-indigo" :class="{'text-red-600': ourVisit.deleted_at}" :href="route('patientsVisits.editFull', visit.id)" tabindex="-1">
        Edit Visit Tests
      </Link>
    </div>
    <div v-if="showNotes" class="flex justify-start items-center space-x-4 px-4 py-2 font-Roboto bg-indigo-500">
        <input class="w-full" type="text" v-model="note">
    </div>

  </div>
</template>

<script>
  export default {
  layout: AppLayout}
</script>
<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import DropdownSearch from '@/MyComponents/DropdownSearch.vue' 
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { computed, reactive, ref } from '@vue/reactivity'
import { Inertia } from '@inertiajs/inertia'
import moment from 'moment'
import '@vueup/vue-quill/dist/vue-quill.snow.css';

  const props = defineProps({
    categories: Array,
    devices: Array,
    visit: Object,
    visitTests: Array,
    header_photo_path: String,
    footer_photo_path: String,
    flags: Array,
  });

  let note = ref('');
  

  const ourVisit = reactive(props.visit);
  const ourDevices = reactive(props.devices);
  const ourVisitTests = reactive(props.visitTests);
  const ourCategories = reactive(props.categories);
  const ourFlags = reactive(props.flags);

  let isPrinting = ref(false);
  let showFlags  = ref(false);
  let showNotes  = ref(false);
  let allowTextWrap = ref(false);
  
  let color = ref('');
  let categoriesForPrint = reactive([]);

  let setCategoriesForPrint = () => {
    ourCategories.forEach((cat) => {
      let catTests = ourVisitTests.filter(ourTest => ourTest.category_id === cat.id);
      if(catTests.length > 0){
        categoriesForPrint.push({
          id: cat.id,
          category_name: cat.name,
          category_tests: catTests,
          printable: true,
          device: cat.default_device
        })
      }
    })
  }

setCategoriesForPrint();

  // let setPrintTests = () => {
  //   ourVisitTests.forEach((test) => { 
  //       testsForPrint.push({
  //           id:           test.id,
  //           short_name:   test.short_name,
  //           category_id:  test.category_id,
  //           result:       test.result,
  //           flag_id:      test.flag_id,
  //           normal_range: test.normal_range ? test.normal_range.pivot.normal_range : ''
  //       });
  //   });
  // };

  // setPrintTests();

  // let togglePrint = (test_id) => {
  //   if(testsForPrint.some(test => test.id === test_id)){
  //       testsForPrint.splice(testsForPrint.findIndex(test2 => test2.id === test_id), 1);
  //   } else {
  //       let test = ourVisitTests[ourVisitTests.findIndex(test => test.id === test_id)]
  //       testsForPrint.push({
  //           id:           test.id,
  //           short_name:   test.short_name,
  //           category_id:  test.category_id,
  //           result:       test.result,
  //           flag_id:      test.flag_id,
  //           normal_range: test.normal_range ?  test.normal_range.pivot.normal_range : ''
  //       });
  //   }
  // };

  // let selectCategoryForPrint = () => {
  //     testsForPrint = [];
  //     ourVisitTests.forEach((test) => { 
  //       if(test.category_id == category_id){
  //           testsForPrint.push({
  //               id:           test.id,
  //               short_name:   test.short_name,
  //               category_id:  test.category_id,
  //               result:       test.result,
  //               flag:         test.flag,
  //               normal_range: test.normal_range ? test.normal_range.pivot.normal_range : ''
  //           });
  //       }
  //     });
  // };

  // let isPrintable = (test_id) => {
  //     if(testsForPrint.some(test => test.id === test_id)){
  //       return true
  //     }else{
  //       return false
  //     }
  // };

  // let printTests = () => {
  //   isPrinting.value = true;
  //   setTimeout(() => print(), 100);
  // };

  let print = () => {
    isPrinting.value = true;
    setTimeout(function(){
      window.print();
      isPrinting.value = false;
    } , 100);
  };

  let print2 = () => {
    window.print();
    isPrinting.value = false;
    //update();
    // console.log("Printing ended33");
    //setTimeout(function () { window.close(); }, 100);
    // window.onafterprint = (event) => {console.log('After print');;
    // window.addEventListener("beforeprint", function() {console.log('beforeprint');});
    // window.onbeforeprint = function(event) {console.log('beforeprint');};}
    // window.onafterprint = function(){console.log("Printing completed..."); }
  };
 
  let storeFull = () => {
    if(form.paid_amount === null){
      form.paid_amount = form.final_cost;
      form.remaining_amount = 0;
    }
    if(form.discount === null){
      form.discount = 0;
    }
    form.post(route('patientsVisits.storeFull'),{
      preserveState: true,
      onError: () => { 
        // form.reset('remaining_amount');
      },
      onSuccess: () => { 
        form.reset();
      },
    })
  };

</script>
