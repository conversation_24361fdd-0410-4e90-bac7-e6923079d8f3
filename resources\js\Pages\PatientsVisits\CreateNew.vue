<template>
  <Head title="Create Visit" />
  <div class="px-4 py-2">
    <div class="mb-2 flex justify-start max-w-xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('patients')">{{ trans('PatientsList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
         {{ trans('RegisterANewVisit') }}
      </h2>
    </div>
    <div class="">
      <form @submit.prevent="storeNew">
        <div class="flex justify-start space-x-2">
          <div class="bg-orange-400 rounded-md shadow max-w-xs" style="max-width: 270px;">
            <div class="p-3 -mr-3 -mb-5 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
              <text-input v-model="form.name" v-on:keydown.enter.prevent :error="form.errors.name" direction="rtl" class="pr-3 pb-3 w-full" type="text" label="Name" :autofocus="true" />
              <select-input v-model.number="form.gender" v-on:keydown.enter.prevent :error="form.errors.gender" direction="ltr" class="pr-3 pb-3 w-1/3" label="Gender">
                <option :value="null" />
                <option value="1">Male</option>
                <option value="2">Female</option>
              </select-input>
              <text-input v-model.number="form.age" v-on:keydown.enter.prevent :error="form.errors.age" direction="ltr" class="pr-3 pb-3 w-1/3" type="number" label="Age" /> 
              <select-input v-model.number="form.age_type" v-on:keydown.enter.prevent :error="form.errors.age_type" class="pr-3 pb-3 w-1/3" label="Age Type">
                <option :value="3" selected>{{trans('Year')}}</option>
                <option :value="2">{{trans('Month')}}</option>
                <option :value="1">{{trans('Day')}}</option>
              </select-input>
              <text-input v-model="form.mobile" v-on:keydown.enter.prevent :error="form.errors.mobile" direction="ltr" class="pr-3 pb-3 w-1/2" type="number" label="Mobile" />
              <select-input v-model.number="form.referred_by" v-on:keydown.enter.prevent :error="form.errors.referred_by" @Change="setReferredBy" class="pr-3 pb-3 w-1/2" label="ReferredBy">
                <option :value="3">{{trans('OutPatient')}}</option>
                <option :value="2">{{trans('Doctor')}}</option>
                <option :value="1">{{trans('SideLab')}}</option>
              </select-input>
              <div v-if="form.referred_by === 2" class="pr-3 pb-3 w-full ltr">
                <label for="doctor" class="form-label" :class="locale == 'ar'? 'rtl' : 'ltr'">{{trans("Doctor")}}:</label>
                <dropdown-search id="doctor" :error="form.errors.doctor_id" direction="rtl" v-on:keydown.enter.prevent :options="ourDoctors" @select="selectDoctor" 
                :CloseOnSelect="true" :fixedLabel="false" label="Select Doctor" searchBy="name" placeholder="بحث عن الاطباء">
                </dropdown-search>
              </div>
              <div v-if="form.referred_by === 1" class="pr-3 pb-3 w-full ltr">
                <label for="lab_id" class="form-label" :class="locale == 'ar'? 'rtl' : 'ltr'">{{trans("SideLab")}}:</label>
                <dropdown-search id="lab_id" :error="form.errors.lab_id" direction="rtl" v-on:keydown.enter.prevent :options="ourSideLabs" @select="selectSideLab" 
                :CloseOnSelect="true" :fixedLabel="false" label="Select Side Lab" searchBy="name" placeholder="بحث عن المختبرات الجانبية">
                </dropdown-search>
              </div>
              <text-input v-model.number="form.tests_cost" :error="form.errors.tests_cost" direction="ltr" class="pr-3 pb-3 w-1/2" disabled="disabled" type="number" label="TestsCost" /> 
              <text-input v-model.number="form.discount" v-on:keydown.enter.prevent :error="form.errors.discount" direction="ltr" @input="compute()" class="pr-3 pb-3 w-1/2" type="number" label="Discount" />
              <text-input v-model.number="form.final_cost" :error="form.errors.final_cost" direction="ltr" class="pr-3 pb-3 w-1/2" disabled="disabled" type="number" label="FinalCost" /> 
              <text-input v-model.number="form.paid_amount" v-on:keydown.enter.prevent :error="form.errors.paid_amount" direction="ltr" @input="compute()" class="pr-3 pb-3 w-1/2" type="number" label="PaidAmount" />
              <text-input v-model.number="form.remaining_amount" :error="form.errors.remaining_amount" direction="ltr" class="pr-3 pb-3 w-full" disabled="disabled" type="number" label="RemainingAmount" />
            </div>
            <div class="flex px-1 mx-auto justify-between py-2 max-w-full">
                <button type="button" class="btn-green" @click="fullyPaid">{{ trans('FullyPaid')}}</button>
                <loading-button :loading="form.processing" class="btn-indigo" @click="storeNew">{{trans('Save') + ' & ' + trans('Print')}}</loading-button>
            </div>
          </div>
          <div class="bg-orange-400 rounded-md shadow w-full max-w-6xl px-3 py-4 flex flex-wrap">
              <div class="px-1 md:px-2 lg:px-2 pb-3 w-1/3">
                <dropdown-search class="bg-white rounded-md" :options="ourTests" @select="selectTest" 
                :CloseOnSelect="false" :fixedLabel="true" label="Select Test" searchBy="short_name" placeholder="Search for tests">
                </dropdown-search>
              </div>
              <div class="px-1 md:px-2 lg:px-2 pb-3 w-1/3">
                <dropdown-search class="bg-white rounded-md" :options="ourGroups" @select="selectGroup" 
                :CloseOnSelect="false" :fixedLabel="false" label="Select Group" searchBy="name" placeholder="Search for groups">
                </dropdown-search>
              </div>
              <div class="px-1 md:px-2 lg:px-2 pb-3 w-1/3">
                <dropdown-search class="bg-white rounded-md" :options="ourOffers" @select="selectOffer" 
                :CloseOnSelect="true" :fixedLabel="false" label="Select Offer" searchBy="name" placeholder="Search for Offers">
                </dropdown-search>
              </div>
              <div class="min-h-full min-w-full">

              
              <div v-if="form_visitTests.length>0" class="min-w-full px-1 lg:px-2">
                <div class="inline-flex w-2/4 items-center justify-start">
                  <span class="shadow px-2 bg-indigo-600 text-white max-h-8">Requested Tests:</span>
                </div>
                <div v-for="(test, index) in form_visitTests" :key="index" class="flex justify-start items-start">
                    <div class="min-w-full mt-1 flex rounded-md shadow-sm">
                        <span class="max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50"
                         style="background-color: #3B3799; border-color: #6F6AE6; min-width: 30%; width: 35%">
                            {{index+1 + ' - ' + test.short_name}}:
                        </span>
                        <input v-if="test.result_type === 0" v-on:keydown.enter.prevent type="text" v-model="test.value" class="max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 
                        flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 18%; max-width: 25%; width: 25%;" />

                        <select v-if="test.result_type === 1" v-model="test.value" class="max-h-8 text-sm py-1 px-1 focus:ring-indigo-500
                         focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 18%; max-width: 25%; width: 25%;">
                         <option v-for="result in test.results" :key="result.id" :value="result.name">{{result.name}}</option>
                        </select>
                        <span class="max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50"
                         style="background-color: #3B3799; border-color: #6F6AE6; min-width: 4%;">
                            Flag:
                        </span>
                        <select v-model="test.flag_id" class="max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 
                        flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 8%;">
                            <option class="p-2" v-for="flag in ourFlags" :key="flag.id" :value="flag.id">{{flag.name}}</option>
                        </select>
                        <span class="max-h-8 text-sm  inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50"
                         style="background-color: #3B3799; border-color: #6F6AE6; min-width: 6%;">
                            Device:
                        </span>
                        <select v-model="test.device_id" class="max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 
                        flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 8%;">
                            <option v-for="device in test.devices" :key="device.id" :value="device.pivot.device_id">{{device.name}}</option>
                        </select>
                        <span class="hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-r-0 border-gray-300 bg-white
                         text-gray-500" style="border-color: #6F6AE6; width: 18%;" 
                         v-html="test.devices[test.devices.findIndex(device => device.pivot.device_id === test.device_id)]? test.devices[test.devices.findIndex(device => device.pivot.device_id === test.device_id)].pivot.normal_range : ''">
                        </span>
                        <span class="max-h-8 inline-flex items-center text-xs px-px font-medium border border-r-0 border-gray-300 text-gray-700"
                         style="background-color: #B1F56C; border-color: #6F6AE6; width: 7%;">
                            {{form.referred_by === 1 ? test.lab_to_lab_price : test.lab_to_patient_price}} IQD
                        </span>
                        <span title="Remove Test" @click="removeTest(test, index)" class="max-h-8 text-sm inline-flex items-center px-2 rounded-r-md border border-l-0
                         bg-red-600 hover:bg-red-500 cursor-pointer" style="border-color: #6F6AE6; width: 3%;">
                            <icon name="x" class="h-4 w-4" />
                        </span>
                    </div>
                </div>
              </div>

              <div v-if="form_visitOffers.length>0" class="min-w-full px-1 lg:px-2">
                <div class="inline-flex w-2/4 items-center justify-start mt-1">
                  <span class="shadow px-2 bg-indigo-600 text-white max-h-8">Requested Offers:</span>
                </div>
                <div v-for="(offer, index) in form_visitOffers" :key="index">
                  <div class="inline-flex w-2/4 mt-1 items-center justify-start">
                    <span class="shadow px-2 py-1 bg-indigo-600 text-white max-h-8">{{offer.name}}</span>
                    <span class="shadow bg-yellow-300 px-2 py-1 text-gray-700 max-h-8">{{offer.price  + ' IQD'}}</span>
                    <span title="Remove Offer" @click="removeOffer(offer, index)" class="shadow max-h-8 text-sm px-2 py-1
                      bg-red-600 hover:bg-red-500 cursor-pointer">
                        <icon name="x" class="h-6 w-6" />
                    </span>
                  </div>
                  <div v-for="(test, index) in offer.tests" :key="index" class="min-w-full flex items-start">
                    <div class="min-w-full mt-1 flex rounded-md shadow-sm">
                        <span class="max-h-8 text-sm inline-flex items-center px-2 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-50"
                         style="background-color: #3B3799; border-color: #6F6AE6; min-width: 30%; width: 30%;">
                            {{index+1 + ' - ' + test.short_name}}:
                        </span>
                        <input v-if="test.result_type === 0" v-on:keydown.enter.prevent type="text" v-model="test.value" class="max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 
                        flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 18%; max-width: 25%; width: 25%;" />

                        <select v-if="test.result_type === 1" v-model="test.value" class="max-h-8 text-sm py-1 px-1 focus:ring-indigo-500
                         focus:border-indigo-500 flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 18%; max-width: 25%; width: 25%;">
                         <option v-for="result in test.results" :key="result.id" :value="result.name">{{result.name}}</option>
                        </select>
                        <span class="max-h-8 text-sm inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50"
                         style="background-color: #3B3799; border-color: #6F6AE6; min-width: 4%;">
                            Flag:
                        </span>
                        <select v-model="test.flag_id" class="max-h-8 text-sm py-1 px-1 focus:ring-indigo-500 focus:border-indigo-500 
                        flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 8%;">
                            <option class="p-2" v-for="flag in ourFlags" :key="flag.id" :value="flag.id">{{flag.name}}</option>
                        </select>
                        <span class="max-h-8 text-sm  inline-flex items-center px-1 border border-r-0 border-gray-300 bg-gray-50 text-gray-50"
                         style="background-color: #3B3799; border-color: #6F6AE6; min-width: 6%;">
                            Device:
                        </span>
                        <select v-model="test.device_id" class="max-h-8 text-sm py-0.5 px-1 focus:ring-indigo-500 focus:border-indigo-500 
                        flex-1 block sm:text-sm border-gray-300" style="border-color: #6F6AE6; min-width: 8%;">
                            <option v-for="device in test.devices" :key="device.id" :value="device.pivot.device_id">{{device.name}}</option>
                        </select>
                        <span class="hidden xl:inline-flex max-h-8 items-center px-1 py-px overflow-y-auto text-xs border border-gray-300 bg-white
                         text-gray-500" style="border-color: #6F6AE6; width: 18%;" 
                         v-html="test.devices[test.devices.findIndex(device => device.pivot.device_id === test.device_id)]? test.devices[test.devices.findIndex(device => device.pivot.device_id=== test.device_id)].pivot.normal_range : ''">
                        </span>
                    </div>
                </div>
              </div>
            </div>
            <div class="flex items-center my-2 ml-2">
                <input id="calcLDLAndVLDL" v-model="form.calcLDLAndVLDL" type="checkbox" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                <label for="calcLDLAndVLDL" class="ml-2 block text-sm  text-gray-50">
                  Calculate LDL & VLDL
                </label>
              </div>
          </div>
        </div>
        </div>
      </form>
    </div>


    <div id="confirmBox" hidden class="relative z-50">
  <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
  <div class="fixed z-10 inset-0 overflow-y-auto">
    <div class="flex items-end sm:items-center justify-center min-h-full p-4 text-center sm:p-0">
      <div class="relative bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full rtl" >
        <div class="bg-white">
          <!-- <div class="sm:flex sm:items-start bg-red-800"> -->
            <!-- <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
              <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div> -->
            <div class="bg-orange-300 w-full">
              <!-- <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">Deactivate account</h3> -->
              <div class="bg-gray-50 p-2">
                <p class="text-xl text-gray-800 sm:text-right">
                  المبلغ المدفوع لا يساوي التكلفة النهائية!
                </p>
              </div>
              <div class="flex p-3 mt-2 text-right rtl">
                <text-input v-model.number="form.final_cost" :error="form.errors.final_cost" direction="ltr" class="pr-3 pb-3 w-1/2" disabled="disabled" type="number" label="التكلفة النهائية" /> 
                <text-input v-model.number="form.paid_amount" v-on:keydown.enter.prevent direction="ltr" :error="form.errors.paid_amount" @input="compute()" class="pr-3 pb-3 w-1/2" type="number" label="المبلغ المدفوع " />
              </div>
            </div>
            
          </div>
        <!-- </div> -->
        <div class="bg-gray-50 p-2 sm:flex sm:flex-row-reverse space-x-2">
          <button type="button" @click="payFullAndContinue()"  class="btn-green2">مساواة واستمرار</button>
          <button type="button" @click="isConfirm(true)"  class="btn-green2">استمرار</button>
          <button type="button" @click="isConfirm(false)" class="btn-indigo2">الغاء</button>
        </div>
      </div>
    </div>
  </div>
</div>


</div>
</template>

<script>
  export default {
  layout: AppLayout}
</script>
<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import DropdownSearch from '@/MyComponents/DropdownSearch.vue' 
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'
import { reactive } from '@vue/reactivity'

  const props = defineProps({
    locale: String,
    patient: Object,  
    doctors: Array,
    sidelabs: Array,
    tests: Array,
    groups: Array,
    offers: Array,
    flags: Array,
  });

  const ourDoctors  = props.doctors;
  const ourSideLabs = props.sidelabs;
  // let ourTests       = props.tests;
  // let ourOffers      = props.offers;
  let ourTests      = reactive(props.tests.slice());
  let ourOffers     = reactive(props.offers.slice());
  const ourGroups   = props.groups;
  const ourFlags    = props.flags;
  
  let form = useForm({
    // _token: document.head.querySelector('meta[name="csrf_token"]').content,
    name: props.patient.name,
    calcLDLAndVLDL: false,
    gender: props.patient.gender,
    age: props.patient.age,
    age_type: props.patient.age_type??3,
    mobile: props.patient.mobile,
    doctor_id: null,
    lab_id: null,
    referred_by: 3,
    tests_cost: 0,
    discount: null,
    final_cost: 0,
    paid_amount: null,
    remaining_amount: 0,
    visitTests: [],
    visitOffers: [],
  });

  let form_visitTests = reactive([]);
  let form_visitOffers = [];

  let selectTest = (selectedTest) => {
    //form_visitTests.push(selectedTest);
    form_visitTests.push({
        id: selectedTest.id,
        device_id: selectedTest.device_id,
        flag_id: selectedTest.flag_id,
        
        result_type: selectedTest.result_type,
        short_name: selectedTest.short_name,
        lab_to_lab_price: selectedTest.lab_to_lab_price,
        lab_to_patient_price: selectedTest.lab_to_patient_price,
        devices: selectedTest.devices,
        results: selectedTest.results,
        value: selectedTest.value,
      });
    ourTests.splice(ourTests.findIndex(test => test.id === selectedTest.id), 1);
    compute();
  };
  
  let removeTest = (test, index) => {
    ourTests.push(test);
    form_visitTests.splice(index, 1);
    compute();
  };

  let selectGroup = (selectedGroup) => {
    selectedGroup.tests.forEach((test) =>{
      let testToPush = ourTests[ourTests.findIndex(ourTest => ourTest.id === test.id)];
      if(testToPush){
        form_visitTests.push(testToPush)
        ourTests.splice(ourTests.findIndex(test => test.id === testToPush.id), 1);
      }
    });
    ourGroups.splice(ourGroups.findIndex(group => group.id === selectedGroup.id), 1);
    compute();
  };

  let selectOffer = (selectedOffer) => {
    let check = true;
    selectedOffer.tests.forEach((test) => {
      if(!ourTests.some(ourTest => ourTest.id === test.id)){
        check = false;
       }
    });

  if(!check){
    return alert('Some tests already exist, Please delete them first.');
    }else{
      let offerTests = [];
      selectedOffer.tests.forEach((test) =>{
        let testToPush = ourTests[ourTests.findIndex(ourTest => ourTest.id === test.id)];
        offerTests.push(testToPush);
        ourTests.splice(ourTests.findIndex(ourTest => ourTest.id === test.id), 1);
      });

      form_visitOffers.push({
        id: selectedOffer.id,
        name: selectedOffer.name,
        price: selectedOffer.price,
        tests: offerTests,
      });
      ourOffers.splice(ourOffers.findIndex(ourOffer => ourOffer.id === selectedOffer.id), 1);
    }
    compute();
  };

  let removeOffer = (offer, index) => {
    offer.tests.forEach((test) =>{
      ourTests.push(test);
    })
    ourOffers.push(offer);
    form_visitOffers.splice(index, 1);
    compute();
  }

  let setReferredBy = () => {
    if(form.referred_by === 1){
      form.doctor_id = null;
    }else if (form.referred_by === 2){
      form.lab_id = null;
    }else{
      form.doctor_id = null;
      form.lab_id = null;
    }
    compute();
  };

  let selectDoctor = (selectedDoctor) => {
      form.doctor_id = selectedDoctor.id;
  };

  let selectSideLab = (selectedSideLab) => {
      form.lab_id = selectedSideLab.id;
  };


  let compute = () => {
    let testsCost = 0;
    form_visitOffers.forEach((offer) =>{
      testsCost += offer.price;
    })
    form_visitTests.forEach((test) =>{
      if(form.referred_by === 1){
        testsCost += test.lab_to_lab_price ;
      }else if(form.referred_by === 2 || form.referred_by === 3){
        testsCost += test.lab_to_patient_price ;
      }else{
        return alert ('ReferredBy field is required');
      }
    })
    form.tests_cost = testsCost;
    form.final_cost = form.tests_cost - form.discount;
    form.remaining_amount = form.final_cost - form.paid_amount;
    console.log('computed!')
  }

  let fullyPaid = () => {
      form.paid_amount = form.final_cost;
      form.remaining_amount = 0;
  };

  let payFullAndContinue = () => {
      hideConfirmBox();
      form.paid_amount = form.final_cost;
      form.remaining_amount = 0;
      storeNew2();
  };

  let showConfirmBox = () => {
    document.getElementById("confirmBox").hidden = false;
  };

  let hideConfirmBox = () => {
    document.getElementById("confirmBox").hidden = true;
  };

  let isConfirm = (answer) =>{
    if (answer) {
      storeNew2();
    } 
    hideConfirmBox();
  }

  let storeNew = () => {
    if (form.paid_amount != form.final_cost) {
      showConfirmBox();
    }else{
      storeNew2();
    }
  }

  let storeNew2 = () => {
    // if(form.paid_amount === null){
    //   form.paid_amount = form.final_cost;
    //   form.remaining_amount = 0;
    // }
    if(form.discount === null || form.discount == ''){
      form.discount = 0;
    }

    form_visitTests.forEach((test) =>{
      form.visitTests.push({
          id: test.id,
          device_id: test.device_id,
          flag_id: test.flag_id,
          value: test.value,
      });
    });

    form_visitOffers.forEach((offer) => {
      let offerTests = [];
      offer.tests.forEach((test) => {
        offerTests.push({
          id: test.id,
          device_id: test.device_id,
          flag_id: test.flag_id,
          value: test.value,
          offer_id: offer.id
        });
      })
      form.visitOffers.push({
          id: offer.id,
          tests: offerTests
      });
    });

    form.post(route('patientsVisits.storeNew', props.patient.id),{
      preserveState: true,
      onError: () => { 
        form.reset('visitTests', 'visitOffers');
      },
      onSuccess: () => { 
        form.reset();
      },
    })
  };

</script>
