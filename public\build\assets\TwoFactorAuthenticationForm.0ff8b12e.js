import{b as F,a as S}from"./DialogModal.3b39e601.js";import{_ as $}from"./Button.de389ba7.js";import{k,v as T,o as s,c as r,a,A as j,b as n,B as o,d as i,t as _,X as A,z as b,x as R,n as B,r as l,y as g,C as u,F as P,D as V}from"./app.5bf25e6f.js";import{_ as E}from"./Input.f95445aa.js";import{_ as q}from"./InputError.2a9befad.js";import{_ as x}from"./SecondaryButton.fcd49457.js";import{_ as J}from"./DangerButton.177534fb.js";import{_ as D}from"./plugin-vue_export-helper.21dcd24c.js";const N={class:"mt-4"},K=i(" Cancel "),L={__name:"ConfirmsPassword",props:{title:{type:String,default:"Confirm Password"},content:{type:String,default:"For your security, please confirm your password to continue."},button:{type:String,default:"Confirm"}},emits:["confirmed"],setup(e,{emit:w}){const m=k(!1),t=T({password:"",error:"",processing:!1}),f=k(null),v=()=>{axios.get(route("password.confirmation")).then(d=>{d.data.confirmed?w("confirmed"):(m.value=!0,setTimeout(()=>f.value.focus(),250))})},p=()=>{t.processing=!0,axios.post(route("password.confirm"),{password:t.password}).then(()=>{t.processing=!1,c(),R().then(()=>w("confirmed"))}).catch(d=>{t.processing=!1,t.error=d.response.data.errors.password[0],f.value.focus()})},c=()=>{m.value=!1,t.password="",t.error=""};return(d,h)=>(s(),r("span",null,[a("span",{onClick:v},[j(d.$slots,"default")]),n(F,{show:m.value,onClose:c},{title:o(()=>[i(_(e.title),1)]),content:o(()=>[i(_(e.content)+" ",1),a("div",N,[n(E,{ref_key:"passwordInput",ref:f,modelValue:t.password,"onUpdate:modelValue":h[0]||(h[0]=y=>t.password=y),type:"password",class:"mt-1 block w-3/4",placeholder:"Password",onKeyup:A(p,["enter"])},null,8,["modelValue","onKeyup"]),n(q,{message:t.error,class:"mt-2"},null,8,["message"])])]),footer:o(()=>[n(x,{onClick:c},{default:o(()=>[K]),_:1}),n($,{class:b(["ml-3",{"opacity-25":t.processing}]),disabled:t.processing,onClick:p},{default:o(()=>[i(_(e.button),1)]),_:1},8,["class","disabled"])]),_:1},8,["show"])]))}},M=B({components:{JetActionSection:S,JetButton:$,JetConfirmsPassword:L,JetDangerButton:J,JetSecondaryButton:x},data(){return{enabling:!1,disabling:!1,qrCode:null,recoveryCodes:[]}},methods:{enableTwoFactorAuthentication(){this.enabling=!0,this.$inertia.post("/user/two-factor-authentication",{},{preserveScroll:!0,onSuccess:()=>Promise.all([this.showQrCode(),this.showRecoveryCodes()]),onFinish:()=>this.enabling=!1})},showQrCode(){return axios.get("/user/two-factor-qr-code").then(e=>{this.qrCode=e.data.svg})},showRecoveryCodes(){return axios.get("/user/two-factor-recovery-codes").then(e=>{this.recoveryCodes=e.data})},regenerateRecoveryCodes(){axios.post("/user/two-factor-recovery-codes").then(e=>{this.showRecoveryCodes()})},disableTwoFactorAuthentication(){this.disabling=!0,this.$inertia.delete("/user/two-factor-authentication",{preserveScroll:!0,onSuccess:()=>this.disabling=!1})}},computed:{twoFactorEnabled(){return!this.enabling&&this.$page.props.user.two_factor_enabled}}}),Q=i(" Two Factor Authentication "),Y=i(" Add additional security to your account using two factor authentication. "),z={key:0,class:"text-lg font-medium text-gray-900"},H={key:1,class:"text-lg font-medium text-gray-900"},I=a("div",{class:"mt-3 max-w-xl text-sm text-gray-600"},[a("p",null," When two factor authentication is enabled, you will be prompted for a secure, random token during authentication. You may retrieve this token from your phone's Google Authenticator application. ")],-1),G={key:2},U={key:0},W=a("div",{class:"mt-4 max-w-xl text-sm text-gray-600"},[a("p",{class:"font-semibold"}," Two factor authentication is now enabled. Scan the following QR code using your phone's authenticator application. ")],-1),X=["innerHTML"],O={key:1},Z=a("div",{class:"mt-4 max-w-xl text-sm text-gray-600"},[a("p",{class:"font-semibold"}," Store these recovery codes in a secure password manager. They can be used to recover access to your account if your two factor authentication device is lost. ")],-1),ee={class:"grid gap-1 max-w-xl mt-4 px-4 py-4 font-mono text-sm bg-gray-100 rounded-lg"},oe={class:"mt-5"},te={key:0},se=i(" Enable "),ne={key:1},ae=i(" Regenerate Recovery Codes "),re=i(" Show Recovery Codes "),ie=i(" Disable ");function ce(e,w,m,t,f,v){const p=l("jet-button"),c=l("jet-confirms-password"),d=l("jet-secondary-button"),h=l("jet-danger-button"),y=l("jet-action-section");return s(),g(y,null,{title:o(()=>[Q]),description:o(()=>[Y]),content:o(()=>[e.twoFactorEnabled?(s(),r("h3",z," You have enabled two factor authentication. ")):(s(),r("h3",H," You have not enabled two factor authentication. ")),I,e.twoFactorEnabled?(s(),r("div",G,[e.qrCode?(s(),r("div",U,[W,a("div",{class:"mt-4",innerHTML:e.qrCode},null,8,X)])):u("",!0),e.recoveryCodes.length>0?(s(),r("div",O,[Z,a("div",ee,[(s(!0),r(P,null,V(e.recoveryCodes,C=>(s(),r("div",{key:C},_(C),1))),128))])])):u("",!0)])):u("",!0),a("div",oe,[e.twoFactorEnabled?(s(),r("div",ne,[n(c,{onConfirmed:e.regenerateRecoveryCodes},{default:o(()=>[e.recoveryCodes.length>0?(s(),g(d,{key:0,class:"mr-3"},{default:o(()=>[ae]),_:1})):u("",!0)]),_:1},8,["onConfirmed"]),n(c,{onConfirmed:e.showRecoveryCodes},{default:o(()=>[e.recoveryCodes.length===0?(s(),g(d,{key:0,class:"mr-3"},{default:o(()=>[re]),_:1})):u("",!0)]),_:1},8,["onConfirmed"]),n(c,{onConfirmed:e.disableTwoFactorAuthentication},{default:o(()=>[n(h,{class:b({"opacity-25":e.disabling}),disabled:e.disabling},{default:o(()=>[ie]),_:1},8,["class","disabled"])]),_:1},8,["onConfirmed"])])):(s(),r("div",te,[n(c,{onConfirmed:e.enableTwoFactorAuthentication},{default:o(()=>[n(p,{type:"button",class:b({"opacity-25":e.enabling}),disabled:e.enabling},{default:o(()=>[se]),_:1},8,["class","disabled"])]),_:1},8,["onConfirmed"])]))])]),_:1})}var we=D(M,[["render",ce]]);export{we as default};
