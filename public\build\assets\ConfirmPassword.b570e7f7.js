import{n as _,r as o,o as f,c as b,b as t,B as n,F as h,H as w,a,z as j,E as g,d as v}from"./app.5bf25e6f.js";import{J as $}from"./AuthenticationCard.d2e642d8.js";import{J as C}from"./AuthenticationCardLogo.c737ac3b.js";import{_ as J}from"./Button.de389ba7.js";import{_ as V}from"./Input.f95445aa.js";import{_ as y}from"./Label.a34a8f2d.js";import{_ as B}from"./ValidationErrors.40b6029f.js";import{_ as H}from"./plugin-vue_export-helper.21dcd24c.js";const k=_({components:{Head:w,JetAuthenticationCard:$,JetAuthenticationCardLogo:C,JetButton:J,JetInput:V,JetLabel:y,JetValidationErrors:B},data(){return{form:this.$inertia.form({password:""})}},methods:{submit(){this.form.post(this.route("password.confirm"),{onFinish:()=>this.form.reset()})}}}),A=a("div",{class:"mb-4 text-sm text-gray-600"}," This is a secure area of the application. Please confirm your password before continuing. ",-1),E={class:"flex justify-end mt-4"},F=v(" Confirm ");function N(e,s,P,x,z,L){const i=o("Head"),m=o("jet-authentication-card-logo"),d=o("jet-validation-errors"),c=o("jet-label"),l=o("jet-input"),p=o("jet-button"),u=o("jet-authentication-card");return f(),b(h,null,[t(i,{title:"Secure Area"}),t(u,null,{logo:n(()=>[t(m)]),default:n(()=>[A,t(d,{class:"mb-4"}),a("form",{onSubmit:s[1]||(s[1]=g((...r)=>e.submit&&e.submit(...r),["prevent"]))},[a("div",null,[t(c,{for:"password",value:"Password"}),t(l,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:e.form.password,"onUpdate:modelValue":s[0]||(s[0]=r=>e.form.password=r),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"])]),a("div",E,[t(p,{class:j(["ml-4",{"opacity-25":e.form.processing}]),disabled:e.form.processing},{default:n(()=>[F]),_:1},8,["class","disabled"])])],32)]),_:1})],64)}var K=H(k,[["render",N]]);export{K as default};
