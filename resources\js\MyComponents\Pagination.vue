<template>
  <div v-if="links.length > 3">
    <div class="flex -mb-1 items-center justify-start">
      <template v-for="(link, key) in links" :key="key">
        <div v-if="link.url === null" class="bg-white mr-1 mb-1 px-4 py-3 text-sm leading-4 text-gray-400 border rounded border-indigo-500 shadow-md hover:cursor-not-allowed" v-html="link.label" />
        <Link v-else  class="bg-white mr-1 mb-1 px-4 py-3 text-sm leading-4 border rounded hover:bg-indigo-500 border-indigo-600 text-indigo-600  hover:border-gray-50 hover:text-gray-50 shadow-md" 
        :class="{ 'border-orange-600 text-orange-600': link.active }" :href="link.url" v-html="link.label" />
      </template>

      <div class="ml-2">
         <p class="text-sm text-indigo-700 font-medium"> Showing <span class="font-medium">{{from}}</span> to <span class="font-medium">{{to}}</span> of <span class="font-medium">{{total}}</span> results </p>
      </div>
    </div>
  </div>
</template>

<script>
import { Link } from '@inertiajs/inertia-vue3';
import { defineComponent } from 'vue'
export default defineComponent({
  components: { Link},
  props: {
    links: Array,
    from: Number,
    to: Number,
    total: Number,
  },
})
</script>
