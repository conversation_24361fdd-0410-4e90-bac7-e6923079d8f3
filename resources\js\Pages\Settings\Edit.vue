<template>
  <Head title="Settings" />
  <div class="p-2 sm:p-4 lg:p-6 max-w-4xl">
    <div class="bg-white rounded-md shadow max-w-xl">
      <form @submit.prevent="update">
        <div class="p-6">
          <label for="headerPhoto2">Header Photo</label>
          <input id="headerPhoto2" type="file" @change="previewHeaderPhoto" ref="headerPhoto" class="px-2 py-1 w-full mt-1 mb-2 border rounded-md
            focus:outline-none focus:ring-1 focus:ring-blue-600"/>
          <div v-if="form.errors.header_photo_path" class="font-bold text-red-600"> {{ form.errors.header_photo_path }} </div>
          <img v-if="headerPhotoUrl" :src="headerPhotoUrl" class="w-full h-1/2 mb-4 border border-gray-900" />
          <img v-else-if="form.header_photo_path" :src="'/storage/'+form.header_photo_path" class="w-full h-1/2 mb-4 border border-gray-900" />

          <label for="footerPhoto2">Footer Photo</label>
          <input id="footerPhoto2" type="file" @change="previewFooterPhoto" ref="footerPhoto" class="px-4 py-2 w-full mt-1 mb-4 border rounded-md 
          focus:outline-none focus:ring-1 focus:ring-blue-600"/>
          <div v-if="form.errors.footer_photo_path" class="font-bold text-red-600"> {{ form.errors.footer_photo_path }} </div>
          <img v-if="footerPhotoUrl" :src="footerPhotoUrl" class="w-full h-1/2 mb-4 border border-gray-900" />
          <img v-else-if="form.footer_photo_path" :src="'/storage/'+form.footer_photo_path" class="w-full h-1/2 mb-4 border border-gray-900" />
        </div>
        <div class="px-4 py-2 text-red-600" v-if="form.isDirty">There are unsaved form changes.</div>
         <div class="flex px-4 py-2 bg-gray-100 border-t border-gray-100  justify-between items-center">
            <loading-button :loading="form.processing" class="mr-4 px-3 py-2 flex btn-indigo" @click="update">{{trans('Update') + ' ' + trans('Settings')}}</loading-button>
        </div>
      </form>
    </div>
  </div>
</template>
<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { computed, reactive, ref } from '@vue/reactivity'
import { Inertia } from '@inertiajs/inertia'
import moment from 'moment'

  const props = defineProps({
    settings: Object,
    errors: Object,
  });

  let form = useForm({
    _method: 'put',
    header_photo_path: props.settings.header_photo_path, 
    footer_photo_path: props.settings.footer_photo_path,
  })
  
  let headerPhotoUrl = ref(null);
  let footerPhotoUrl = ref(null);

  const headerPhoto = ref(null);
  const footerPhoto = ref(null);

  let previewHeaderPhoto = (e) => {
    const file = e.target.files[0];
    headerPhotoUrl.value = URL.createObjectURL(file);
  };

  let previewFooterPhoto = (e) => {
    const file = e.target.files[0];
    footerPhotoUrl.value = URL.createObjectURL(file);
  };

  let showImage = () => {
    return "/storage/";
  };
  
  let update = () => {
    console.log(headerPhoto.value.files[0])
    if (headerPhoto.value) {
        form.header_photo_path = headerPhoto.value.files[0];
    }
    if (footerPhoto.value) {
        form.footer_photo_path = footerPhoto.value.files[0];
    }
    form.post(route('settings.update', props.settings.id),{
      preserveState: true,
      onError: () => { 
        // form.reset('remaining_amount');
      },
      onSuccess: () => { 
        form.reset();
      },
    })
  };
</script>
<script>
  export default {
  layout: AppLayout}
</script>