import{V as ye,Z as We,_ as Be,$ as Ce,n as Ge,l as Ye,a0 as Qe,k as Re,q as je,h as Xe,x as Je}from"./app.5bf25e6f.js";var Fe={exports:{}};/*!
 * Quill Editor v1.3.7
 * https://quilljs.com/
 * Copyright (c) 2014, <PERSON>
 * Copyright (c) 2013, salesforce.com
 */(function(P,D){(function(d,c){P.exports=c()})(typeof self!="undefined"?self:ye,function(){return function(y){var d={};function c(k){if(d[k])return d[k].exports;var m=d[k]={i:k,l:!1,exports:{}};return y[k].call(m.exports,m,m.exports,c),m.l=!0,m.exports}return c.m=y,c.c=d,c.d=function(k,m,b){c.o(k,m)||Object.defineProperty(k,m,{configurable:!1,enumerable:!0,get:b})},c.n=function(k){var m=k&&k.__esModule?function(){return k.default}:function(){return k};return c.d(m,"a",m),m},c.o=function(k,m){return Object.prototype.hasOwnProperty.call(k,m)},c.p="",c(c.s=109)}([function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(17),m=c(18),b=c(19),v=c(45),p=c(46),s=c(47),o=c(48),e=c(49),t=c(12),u=c(32),l=c(33),a=c(31),r=c(1),i={Scope:r.Scope,create:r.create,find:r.find,query:r.query,register:r.register,Container:k.default,Format:m.default,Leaf:b.default,Embed:o.default,Scroll:v.default,Block:s.default,Inline:p.default,Text:e.default,Attributor:{Attribute:t.default,Class:u.default,Style:l.default,Store:a.default}};d.default=i},function(y,d,c){var k=this&&this.__extends||function(){var a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var f in i)i.hasOwnProperty(f)&&(r[f]=i[f])};return function(r,i){a(r,i);function f(){this.constructor=r}r.prototype=i===null?Object.create(i):(f.prototype=i.prototype,new f)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=function(a){k(r,a);function r(i){var f=this;return i="[Parchment] "+i,f=a.call(this,i)||this,f.message=i,f.name=f.constructor.name,f}return r}(Error);d.ParchmentError=m;var b={},v={},p={},s={};d.DATA_KEY="__blot";var o;(function(a){a[a.TYPE=3]="TYPE",a[a.LEVEL=12]="LEVEL",a[a.ATTRIBUTE=13]="ATTRIBUTE",a[a.BLOT=14]="BLOT",a[a.INLINE=7]="INLINE",a[a.BLOCK=11]="BLOCK",a[a.BLOCK_BLOT=10]="BLOCK_BLOT",a[a.INLINE_BLOT=6]="INLINE_BLOT",a[a.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",a[a.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",a[a.ANY=15]="ANY"})(o=d.Scope||(d.Scope={}));function e(a,r){var i=u(a);if(i==null)throw new m("Unable to create "+a+" blot");var f=i,n=a instanceof Node||a.nodeType===Node.TEXT_NODE?a:f.create(r);return new f(n,r)}d.create=e;function t(a,r){return r===void 0&&(r=!1),a==null?null:a[d.DATA_KEY]!=null?a[d.DATA_KEY].blot:r?t(a.parentNode,r):null}d.find=t;function u(a,r){r===void 0&&(r=o.ANY);var i;if(typeof a=="string")i=s[a]||b[a];else if(a instanceof Text||a.nodeType===Node.TEXT_NODE)i=s.text;else if(typeof a=="number")a&o.LEVEL&o.BLOCK?i=s.block:a&o.LEVEL&o.INLINE&&(i=s.inline);else if(a instanceof HTMLElement){var f=(a.getAttribute("class")||"").split(/\s+/);for(var n in f)if(i=v[f[n]],i)break;i=i||p[a.tagName]}return i==null?null:r&o.LEVEL&i.scope&&r&o.TYPE&i.scope?i:null}d.query=u;function l(){for(var a=[],r=0;r<arguments.length;r++)a[r]=arguments[r];if(a.length>1)return a.map(function(n){return l(n)});var i=a[0];if(typeof i.blotName!="string"&&typeof i.attrName!="string")throw new m("Invalid definition");if(i.blotName==="abstract")throw new m("Cannot register abstract class");if(s[i.blotName||i.attrName]=i,typeof i.keyName=="string")b[i.keyName]=i;else if(i.className!=null&&(v[i.className]=i),i.tagName!=null){Array.isArray(i.tagName)?i.tagName=i.tagName.map(function(n){return n.toUpperCase()}):i.tagName=i.tagName.toUpperCase();var f=Array.isArray(i.tagName)?i.tagName:[i.tagName];f.forEach(function(n){(p[n]==null||i.className==null)&&(p[n]=i)})}return i}d.register=l},function(y,d,c){var k=c(51),m=c(11),b=c(3),v=c(20),p=String.fromCharCode(0),s=function(o){Array.isArray(o)?this.ops=o:o!=null&&Array.isArray(o.ops)?this.ops=o.ops:this.ops=[]};s.prototype.insert=function(o,e){var t={};return o.length===0?this:(t.insert=o,e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t))},s.prototype.delete=function(o){return o<=0?this:this.push({delete:o})},s.prototype.retain=function(o,e){if(o<=0)return this;var t={retain:o};return e!=null&&typeof e=="object"&&Object.keys(e).length>0&&(t.attributes=e),this.push(t)},s.prototype.push=function(o){var e=this.ops.length,t=this.ops[e-1];if(o=b(!0,{},o),typeof t=="object"){if(typeof o.delete=="number"&&typeof t.delete=="number")return this.ops[e-1]={delete:t.delete+o.delete},this;if(typeof t.delete=="number"&&o.insert!=null&&(e-=1,t=this.ops[e-1],typeof t!="object"))return this.ops.unshift(o),this;if(m(o.attributes,t.attributes)){if(typeof o.insert=="string"&&typeof t.insert=="string")return this.ops[e-1]={insert:t.insert+o.insert},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this;if(typeof o.retain=="number"&&typeof t.retain=="number")return this.ops[e-1]={retain:t.retain+o.retain},typeof o.attributes=="object"&&(this.ops[e-1].attributes=o.attributes),this}}return e===this.ops.length?this.ops.push(o):this.ops.splice(e,0,o),this},s.prototype.chop=function(){var o=this.ops[this.ops.length-1];return o&&o.retain&&!o.attributes&&this.ops.pop(),this},s.prototype.filter=function(o){return this.ops.filter(o)},s.prototype.forEach=function(o){this.ops.forEach(o)},s.prototype.map=function(o){return this.ops.map(o)},s.prototype.partition=function(o){var e=[],t=[];return this.forEach(function(u){var l=o(u)?e:t;l.push(u)}),[e,t]},s.prototype.reduce=function(o,e){return this.ops.reduce(o,e)},s.prototype.changeLength=function(){return this.reduce(function(o,e){return e.insert?o+v.length(e):e.delete?o-e.delete:o},0)},s.prototype.length=function(){return this.reduce(function(o,e){return o+v.length(e)},0)},s.prototype.slice=function(o,e){o=o||0,typeof e!="number"&&(e=1/0);for(var t=[],u=v.iterator(this.ops),l=0;l<e&&u.hasNext();){var a;l<o?a=u.next(o-l):(a=u.next(e-l),t.push(a)),l+=v.length(a)}return new s(t)},s.prototype.compose=function(o){var e=v.iterator(this.ops),t=v.iterator(o.ops),u=[],l=t.peek();if(l!=null&&typeof l.retain=="number"&&l.attributes==null){for(var a=l.retain;e.peekType()==="insert"&&e.peekLength()<=a;)a-=e.peekLength(),u.push(e.next());l.retain-a>0&&t.next(l.retain-a)}for(var r=new s(u);e.hasNext()||t.hasNext();)if(t.peekType()==="insert")r.push(t.next());else if(e.peekType()==="delete")r.push(e.next());else{var i=Math.min(e.peekLength(),t.peekLength()),f=e.next(i),n=t.next(i);if(typeof n.retain=="number"){var h={};typeof f.retain=="number"?h.retain=i:h.insert=f.insert;var _=v.attributes.compose(f.attributes,n.attributes,typeof f.retain=="number");if(_&&(h.attributes=_),r.push(h),!t.hasNext()&&m(r.ops[r.ops.length-1],h)){var O=new s(e.rest());return r.concat(O).chop()}}else typeof n.delete=="number"&&typeof f.retain=="number"&&r.push(n)}return r.chop()},s.prototype.concat=function(o){var e=new s(this.ops.slice());return o.ops.length>0&&(e.push(o.ops[0]),e.ops=e.ops.concat(o.ops.slice(1))),e},s.prototype.diff=function(o,e){if(this.ops===o.ops)return new s;var t=[this,o].map(function(i){return i.map(function(f){if(f.insert!=null)return typeof f.insert=="string"?f.insert:p;var n=i===o?"on":"with";throw new Error("diff() called "+n+" non-document")}).join("")}),u=new s,l=k(t[0],t[1],e),a=v.iterator(this.ops),r=v.iterator(o.ops);return l.forEach(function(i){for(var f=i[1].length;f>0;){var n=0;switch(i[0]){case k.INSERT:n=Math.min(r.peekLength(),f),u.push(r.next(n));break;case k.DELETE:n=Math.min(f,a.peekLength()),a.next(n),u.delete(n);break;case k.EQUAL:n=Math.min(a.peekLength(),r.peekLength(),f);var h=a.next(n),_=r.next(n);m(h.insert,_.insert)?u.retain(n,v.attributes.diff(h.attributes,_.attributes)):u.push(_).delete(n);break}f-=n}}),u.chop()},s.prototype.eachLine=function(o,e){e=e||`
`;for(var t=v.iterator(this.ops),u=new s,l=0;t.hasNext();){if(t.peekType()!=="insert")return;var a=t.peek(),r=v.length(a)-t.peekLength(),i=typeof a.insert=="string"?a.insert.indexOf(e,r)-r:-1;if(i<0)u.push(t.next());else if(i>0)u.push(t.next(i));else{if(o(u,t.next(1).attributes||{},l)===!1)return;l+=1,u=new s}}u.length()>0&&o(u,{},l)},s.prototype.transform=function(o,e){if(e=!!e,typeof o=="number")return this.transformPosition(o,e);for(var t=v.iterator(this.ops),u=v.iterator(o.ops),l=new s;t.hasNext()||u.hasNext();)if(t.peekType()==="insert"&&(e||u.peekType()!=="insert"))l.retain(v.length(t.next()));else if(u.peekType()==="insert")l.push(u.next());else{var a=Math.min(t.peekLength(),u.peekLength()),r=t.next(a),i=u.next(a);if(r.delete)continue;i.delete?l.push(i):l.retain(a,v.attributes.transform(r.attributes,i.attributes,e))}return l.chop()},s.prototype.transformPosition=function(o,e){e=!!e;for(var t=v.iterator(this.ops),u=0;t.hasNext()&&u<=o;){var l=t.peekLength(),a=t.peekType();if(t.next(),a==="delete"){o-=Math.min(l,o-u);continue}else a==="insert"&&(u<o||!e)&&(o+=l);u+=l}return o},y.exports=s},function(y,d){var c=Object.prototype.hasOwnProperty,k=Object.prototype.toString,m=Object.defineProperty,b=Object.getOwnPropertyDescriptor,v=function(t){return typeof Array.isArray=="function"?Array.isArray(t):k.call(t)==="[object Array]"},p=function(t){if(!t||k.call(t)!=="[object Object]")return!1;var u=c.call(t,"constructor"),l=t.constructor&&t.constructor.prototype&&c.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!u&&!l)return!1;var a;for(a in t);return typeof a=="undefined"||c.call(t,a)},s=function(t,u){m&&u.name==="__proto__"?m(t,u.name,{enumerable:!0,configurable:!0,value:u.newValue,writable:!0}):t[u.name]=u.newValue},o=function(t,u){if(u==="__proto__")if(c.call(t,u)){if(b)return b(t,u).value}else return;return t[u]};y.exports=function e(){var t,u,l,a,r,i,f=arguments[0],n=1,h=arguments.length,_=!1;for(typeof f=="boolean"&&(_=f,f=arguments[1]||{},n=2),(f==null||typeof f!="object"&&typeof f!="function")&&(f={});n<h;++n)if(t=arguments[n],t!=null)for(u in t)l=o(f,u),a=o(t,u),f!==a&&(_&&a&&(p(a)||(r=v(a)))?(r?(r=!1,i=l&&v(l)?l:[]):i=l&&p(l)?l:{},s(f,{name:u,newValue:e(_,i,a)})):typeof a!="undefined"&&s(f,{name:u,newValue:a}));return f}},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.BlockEmbed=d.bubbleFormats=void 0;var k=function(){function g(A,T){for(var x=0;x<T.length;x++){var j=T[x];j.enumerable=j.enumerable||!1,j.configurable=!0,"value"in j&&(j.writable=!0),Object.defineProperty(A,j.key,j)}}return function(A,T,x){return T&&g(A.prototype,T),x&&g(A,x),A}}(),m=function g(A,T,x){A===null&&(A=Function.prototype);var j=Object.getOwnPropertyDescriptor(A,T);if(j===void 0){var F=Object.getPrototypeOf(A);return F===null?void 0:g(F,T,x)}else{if("value"in j)return j.value;var U=j.get;return U===void 0?void 0:U.call(x)}},b=c(3),v=f(b),p=c(2),s=f(p),o=c(0),e=f(o),t=c(16),u=f(t),l=c(6),a=f(l),r=c(7),i=f(r);function f(g){return g&&g.__esModule?g:{default:g}}function n(g,A){if(!(g instanceof A))throw new TypeError("Cannot call a class as a function")}function h(g,A){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A&&(typeof A=="object"||typeof A=="function")?A:g}function _(g,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof A);g.prototype=Object.create(A&&A.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),A&&(Object.setPrototypeOf?Object.setPrototypeOf(g,A):g.__proto__=A)}var O=1,w=function(g){_(A,g);function A(){return n(this,A),h(this,(A.__proto__||Object.getPrototypeOf(A)).apply(this,arguments))}return k(A,[{key:"attach",value:function(){m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"attach",this).call(this),this.attributes=new e.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return new s.default().insert(this.value(),(0,v.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(x,j){var F=e.default.query(x,e.default.Scope.BLOCK_ATTRIBUTE);F!=null&&this.attributes.attribute(F,j)}},{key:"formatAt",value:function(x,j,F,U){this.format(F,U)}},{key:"insertAt",value:function(x,j,F){if(typeof j=="string"&&j.endsWith(`
`)){var U=e.default.create(S.blotName);this.parent.insertBefore(U,x===0?this:this.next),U.insertAt(0,j.slice(0,-1))}else m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertAt",this).call(this,x,j,F)}}]),A}(e.default.Embed);w.scope=e.default.Scope.BLOCK_BLOT;var S=function(g){_(A,g);function A(T){n(this,A);var x=h(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,T));return x.cache={},x}return k(A,[{key:"delta",value:function(){return this.cache.delta==null&&(this.cache.delta=this.descendants(e.default.Leaf).reduce(function(x,j){return j.length()===0?x:x.insert(j.value(),E(j))},new s.default).insert(`
`,E(this))),this.cache.delta}},{key:"deleteAt",value:function(x,j){m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"deleteAt",this).call(this,x,j),this.cache={}}},{key:"formatAt",value:function(x,j,F,U){j<=0||(e.default.query(F,e.default.Scope.BLOCK)?x+j===this.length()&&this.format(F,U):m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"formatAt",this).call(this,x,Math.min(j,this.length()-x-1),F,U),this.cache={})}},{key:"insertAt",value:function(x,j,F){if(F!=null)return m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertAt",this).call(this,x,j,F);if(j.length!==0){var U=j.split(`
`),W=U.shift();W.length>0&&(x<this.length()-1||this.children.tail==null?m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertAt",this).call(this,Math.min(x,this.length()-1),W):this.children.tail.insertAt(this.children.tail.length(),W),this.cache={});var B=this;U.reduce(function(M,N){return B=B.split(M,!0),B.insertAt(0,N),N.length},x+W.length)}}},{key:"insertBefore",value:function(x,j){var F=this.children.head;m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"insertBefore",this).call(this,x,j),F instanceof u.default&&F.remove(),this.cache={}}},{key:"length",value:function(){return this.cache.length==null&&(this.cache.length=m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"length",this).call(this)+O),this.cache.length}},{key:"moveChildren",value:function(x,j){m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"moveChildren",this).call(this,x,j),this.cache={}}},{key:"optimize",value:function(x){m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"optimize",this).call(this,x),this.cache={}}},{key:"path",value:function(x){return m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"path",this).call(this,x,!0)}},{key:"removeChild",value:function(x){m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"removeChild",this).call(this,x),this.cache={}}},{key:"split",value:function(x){var j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(j&&(x===0||x>=this.length()-O)){var F=this.clone();return x===0?(this.parent.insertBefore(F,this),this):(this.parent.insertBefore(F,this.next),F)}else{var U=m(A.prototype.__proto__||Object.getPrototypeOf(A.prototype),"split",this).call(this,x,j);return this.cache={},U}}}]),A}(e.default.Block);S.blotName="block",S.tagName="P",S.defaultChild="break",S.allowedChildren=[a.default,e.default.Embed,i.default];function E(g){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return g==null||(typeof g.formats=="function"&&(A=(0,v.default)(A,g.formats())),g.parent==null||g.parent.blotName=="scroll"||g.parent.statics.scope!==g.statics.scope)?A:E(g.parent,A)}d.bubbleFormats=E,d.BlockEmbed=w,d.default=S},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.overload=d.expandConfig=void 0;var k=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(B){return typeof B}:function(B){return B&&typeof Symbol=="function"&&B.constructor===Symbol&&B!==Symbol.prototype?"symbol":typeof B},m=function(){function B(M,N){var q=[],R=!0,H=!1,C=void 0;try{for(var L=M[Symbol.iterator](),I;!(R=(I=L.next()).done)&&(q.push(I.value),!(N&&q.length===N));R=!0);}catch(z){H=!0,C=z}finally{try{!R&&L.return&&L.return()}finally{if(H)throw C}}return q}return function(M,N){if(Array.isArray(M))return M;if(Symbol.iterator in Object(M))return B(M,N);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function B(M,N){for(var q=0;q<N.length;q++){var R=N[q];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(M,R.key,R)}}return function(M,N,q){return N&&B(M.prototype,N),q&&B(M,q),M}}();c(50);var v=c(2),p=E(v),s=c(14),o=E(s),e=c(8),t=E(e),u=c(9),l=E(u),a=c(0),r=E(a),i=c(15),f=E(i),n=c(3),h=E(n),_=c(10),O=E(_),w=c(34),S=E(w);function E(B){return B&&B.__esModule?B:{default:B}}function g(B,M,N){return M in B?Object.defineProperty(B,M,{value:N,enumerable:!0,configurable:!0,writable:!0}):B[M]=N,B}function A(B,M){if(!(B instanceof M))throw new TypeError("Cannot call a class as a function")}var T=(0,O.default)("quill"),x=function(){b(B,null,[{key:"debug",value:function(N){N===!0&&(N="log"),O.default.level(N)}},{key:"find",value:function(N){return N.__quill||r.default.find(N)}},{key:"import",value:function(N){return this.imports[N]==null&&T.error("Cannot import "+N+". Are you sure it was registered?"),this.imports[N]}},{key:"register",value:function(N,q){var R=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(typeof N!="string"){var C=N.attrName||N.blotName;typeof C=="string"?this.register("formats/"+C,N,q):Object.keys(N).forEach(function(L){R.register(L,N[L],q)})}else this.imports[N]!=null&&!H&&T.warn("Overwriting "+N+" with",q),this.imports[N]=q,(N.startsWith("blots/")||N.startsWith("formats/"))&&q.blotName!=="abstract"?r.default.register(q):N.startsWith("modules")&&typeof q.register=="function"&&q.register()}}]);function B(M){var N=this,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(A(this,B),this.options=j(M,q),this.container=this.options.container,this.container==null)return T.error("Invalid Quill container",M);this.options.debug&&B.debug(this.options.debug);var R=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new t.default,this.scroll=r.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new o.default(this.scroll),this.selection=new f.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(t.default.events.EDITOR_CHANGE,function(C){C===t.default.events.TEXT_CHANGE&&N.root.classList.toggle("ql-blank",N.editor.isBlank())}),this.emitter.on(t.default.events.SCROLL_UPDATE,function(C,L){var I=N.selection.lastRange,z=I&&I.length===0?I.index:void 0;F.call(N,function(){return N.editor.update(null,L,z)},C)});var H=this.clipboard.convert(`<div class='ql-editor' style="white-space: normal;">`+R+"<p><br></p></div>");this.setContents(H),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return b(B,[{key:"addContainer",value:function(N){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;if(typeof N=="string"){var R=N;N=document.createElement("div"),N.classList.add(R)}return this.container.insertBefore(N,q),N}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(N,q,R){var H=this,C=U(N,q,R),L=m(C,4);return N=L[0],q=L[1],R=L[3],F.call(this,function(){return H.editor.deleteText(N,q)},R,N,-1*q)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.scroll.enable(N),this.container.classList.toggle("ql-disabled",!N)}},{key:"focus",value:function(){var N=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=N,this.scrollIntoView()}},{key:"format",value:function(N,q){var R=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:t.default.sources.API;return F.call(this,function(){var C=R.getSelection(!0),L=new p.default;if(C==null)return L;if(r.default.query(N,r.default.Scope.BLOCK))L=R.editor.formatLine(C.index,C.length,g({},N,q));else{if(C.length===0)return R.selection.format(N,q),L;L=R.editor.formatText(C.index,C.length,g({},N,q))}return R.setSelection(C,t.default.sources.SILENT),L},H)}},{key:"formatLine",value:function(N,q,R,H,C){var L=this,I=void 0,z=U(N,q,R,H,C),K=m(z,4);return N=K[0],q=K[1],I=K[2],C=K[3],F.call(this,function(){return L.editor.formatLine(N,q,I)},C,N,0)}},{key:"formatText",value:function(N,q,R,H,C){var L=this,I=void 0,z=U(N,q,R,H,C),K=m(z,4);return N=K[0],q=K[1],I=K[2],C=K[3],F.call(this,function(){return L.editor.formatText(N,q,I)},C,N,0)}},{key:"getBounds",value:function(N){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,R=void 0;typeof N=="number"?R=this.selection.getBounds(N,q):R=this.selection.getBounds(N.index,N.length);var H=this.container.getBoundingClientRect();return{bottom:R.bottom-H.top,height:R.height,left:R.left-H.left,right:R.right-H.left,top:R.top-H.top,width:R.width}}},{key:"getContents",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-N,R=U(N,q),H=m(R,2);return N=H[0],q=H[1],this.editor.getContents(N,q)}},{key:"getFormat",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.getSelection(!0),q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return typeof N=="number"?this.editor.getFormat(N,q):this.editor.getFormat(N.index,N.length)}},{key:"getIndex",value:function(N){return N.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(N){return this.scroll.leaf(N)}},{key:"getLine",value:function(N){return this.scroll.line(N)}},{key:"getLines",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE;return typeof N!="number"?this.scroll.lines(N.index,N.length):this.scroll.lines(N,q)}},{key:"getModule",value:function(N){return this.theme.modules[N]}},{key:"getSelection",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;return N&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.getLength()-N,R=U(N,q),H=m(R,2);return N=H[0],q=H[1],this.editor.getText(N,q)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(N,q,R){var H=this,C=arguments.length>3&&arguments[3]!==void 0?arguments[3]:B.sources.API;return F.call(this,function(){return H.editor.insertEmbed(N,q,R)},C,N)}},{key:"insertText",value:function(N,q,R,H,C){var L=this,I=void 0,z=U(N,0,R,H,C),K=m(z,4);return N=K[0],I=K[2],C=K[3],F.call(this,function(){return L.editor.insertText(N,q,I)},C,N,q.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(N,q,R){this.clipboard.dangerouslyPasteHTML(N,q,R)}},{key:"removeFormat",value:function(N,q,R){var H=this,C=U(N,q,R),L=m(C,4);return N=L[0],q=L[1],R=L[3],F.call(this,function(){return H.editor.removeFormat(N,q)},R,N)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(N){var q=this,R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return F.call(this,function(){N=new p.default(N);var H=q.getLength(),C=q.editor.deleteText(0,H),L=q.editor.applyDelta(N),I=L.ops[L.ops.length-1];I!=null&&typeof I.insert=="string"&&I.insert[I.insert.length-1]===`
`&&(q.editor.deleteText(q.getLength()-1,1),L.delete(1));var z=C.compose(L);return z},R)}},{key:"setSelection",value:function(N,q,R){if(N==null)this.selection.setRange(null,q||B.sources.API);else{var H=U(N,q,R),C=m(H,4);N=C[0],q=C[1],R=C[3],this.selection.setRange(new i.Range(N,q),R),R!==t.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(N){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API,R=new p.default().insert(N);return this.setContents(R,q)}},{key:"update",value:function(){var N=arguments.length>0&&arguments[0]!==void 0?arguments[0]:t.default.sources.USER,q=this.scroll.update(N);return this.selection.update(N),q}},{key:"updateContents",value:function(N){var q=this,R=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t.default.sources.API;return F.call(this,function(){return N=new p.default(N),q.editor.applyDelta(N,R)},R,!0)}}]),B}();x.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},x.events=t.default.events,x.sources=t.default.sources,x.version="1.3.7",x.imports={delta:p.default,parchment:r.default,"core/module":l.default,"core/theme":S.default};function j(B,M){if(M=(0,h.default)(!0,{container:B,modules:{clipboard:!0,keyboard:!0,history:!0}},M),!M.theme||M.theme===x.DEFAULTS.theme)M.theme=S.default;else if(M.theme=x.import("themes/"+M.theme),M.theme==null)throw new Error("Invalid theme "+M.theme+". Did you register it?");var N=(0,h.default)(!0,{},M.theme.DEFAULTS);[N,M].forEach(function(H){H.modules=H.modules||{},Object.keys(H.modules).forEach(function(C){H.modules[C]===!0&&(H.modules[C]={})})});var q=Object.keys(N.modules).concat(Object.keys(M.modules)),R=q.reduce(function(H,C){var L=x.import("modules/"+C);return L==null?T.error("Cannot load "+C+" module. Are you sure you registered it?"):H[C]=L.DEFAULTS||{},H},{});return M.modules!=null&&M.modules.toolbar&&M.modules.toolbar.constructor!==Object&&(M.modules.toolbar={container:M.modules.toolbar}),M=(0,h.default)(!0,{},x.DEFAULTS,{modules:R},N,M),["bounds","container","scrollingContainer"].forEach(function(H){typeof M[H]=="string"&&(M[H]=document.querySelector(M[H]))}),M.modules=Object.keys(M.modules).reduce(function(H,C){return M.modules[C]&&(H[C]=M.modules[C]),H},{}),M}function F(B,M,N,q){if(this.options.strict&&!this.isEnabled()&&M===t.default.sources.USER)return new p.default;var R=N==null?null:this.getSelection(),H=this.editor.delta,C=B();if(R!=null&&(N===!0&&(N=R.index),q==null?R=W(R,C,M):q!==0&&(R=W(R,N,q,M)),this.setSelection(R,t.default.sources.SILENT)),C.length()>0){var L,I=[t.default.events.TEXT_CHANGE,C,H,M];if((L=this.emitter).emit.apply(L,[t.default.events.EDITOR_CHANGE].concat(I)),M!==t.default.sources.SILENT){var z;(z=this.emitter).emit.apply(z,I)}}return C}function U(B,M,N,q,R){var H={};return typeof B.index=="number"&&typeof B.length=="number"?typeof M!="number"?(R=q,q=N,N=M,M=B.length,B=B.index):(M=B.length,B=B.index):typeof M!="number"&&(R=q,q=N,N=M,M=0),(typeof N=="undefined"?"undefined":k(N))==="object"?(H=N,R=q):typeof N=="string"&&(q!=null?H[N]=q:R=N),R=R||t.default.sources.API,[B,M,H,R]}function W(B,M,N,q){if(B==null)return null;var R=void 0,H=void 0;if(M instanceof p.default){var C=[B.index,B.index+B.length].map(function(K){return M.transformPosition(K,q!==t.default.sources.USER)}),L=m(C,2);R=L[0],H=L[1]}else{var I=[B.index,B.index+B.length].map(function(K){return K<M||K===M&&q===t.default.sources.USER?K:N>=0?K+N:Math.max(M,K+N)}),z=m(I,2);R=z[0],H=z[1]}return new i.Range(R,H-R)}d.expandConfig=j,d.overload=U,d.default=x},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var _=n.get;return _===void 0?void 0:_.call(f)}},b=c(7),v=o(b),p=c(0),s=o(p);function o(a){return a&&a.__esModule?a:{default:a}}function e(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function t(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function u(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var l=function(a){u(r,a);function r(){return e(this,r),t(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return k(r,[{key:"formatAt",value:function(f,n,h,_){if(r.compare(this.statics.blotName,h)<0&&s.default.query(h,s.default.Scope.BLOT)){var O=this.isolate(f,n);_&&O.wrap(h,_)}else m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"formatAt",this).call(this,f,n,h,_)}},{key:"optimize",value:function(f){if(m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"optimize",this).call(this,f),this.parent instanceof r&&r.compare(this.statics.blotName,this.parent.statics.blotName)>0){var n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}}],[{key:"compare",value:function(f,n){var h=r.order.indexOf(f),_=r.order.indexOf(n);return h>=0||_>=0?h-_:f===n?0:f<n?-1:1}}]),r}(s.default.Inline);l.allowedChildren=[l,s.default.Embed,v.default],l.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],d.default=l},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(0),m=b(k);function b(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return v(this,t),p(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default.Text);d.default=o},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function i(f,n){for(var h=0;h<n.length;h++){var _=n[h];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(f,_.key,_)}}return function(f,n,h){return n&&i(f.prototype,n),h&&i(f,h),f}}(),m=function i(f,n,h){f===null&&(f=Function.prototype);var _=Object.getOwnPropertyDescriptor(f,n);if(_===void 0){var O=Object.getPrototypeOf(f);return O===null?void 0:i(O,n,h)}else{if("value"in _)return _.value;var w=_.get;return w===void 0?void 0:w.call(h)}},b=c(54),v=o(b),p=c(10),s=o(p);function o(i){return i&&i.__esModule?i:{default:i}}function e(i,f){if(!(i instanceof f))throw new TypeError("Cannot call a class as a function")}function t(i,f){if(!i)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return f&&(typeof f=="object"||typeof f=="function")?f:i}function u(i,f){if(typeof f!="function"&&f!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof f);i.prototype=Object.create(f&&f.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),f&&(Object.setPrototypeOf?Object.setPrototypeOf(i,f):i.__proto__=f)}var l=(0,s.default)("quill:events"),a=["selectionchange","mousedown","mouseup","click"];a.forEach(function(i){document.addEventListener(i,function(){for(var f=arguments.length,n=Array(f),h=0;h<f;h++)n[h]=arguments[h];[].slice.call(document.querySelectorAll(".ql-container")).forEach(function(_){if(_.__quill&&_.__quill.emitter){var O;(O=_.__quill.emitter).handleDOM.apply(O,n)}})})});var r=function(i){u(f,i);function f(){e(this,f);var n=t(this,(f.__proto__||Object.getPrototypeOf(f)).call(this));return n.listeners={},n.on("error",l.error),n}return k(f,[{key:"emit",value:function(){l.log.apply(l,arguments),m(f.prototype.__proto__||Object.getPrototypeOf(f.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(h){for(var _=arguments.length,O=Array(_>1?_-1:0),w=1;w<_;w++)O[w-1]=arguments[w];(this.listeners[h.type]||[]).forEach(function(S){var E=S.node,g=S.handler;(h.target===E||E.contains(h.target))&&g.apply(void 0,[h].concat(O))})}},{key:"listenDOM",value:function(h,_,O){this.listeners[h]||(this.listeners[h]=[]),this.listeners[h].push({node:_,handler:O})}}]),f}(v.default);r.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},r.sources={API:"api",SILENT:"silent",USER:"user"},d.default=r},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});function k(b,v){if(!(b instanceof v))throw new TypeError("Cannot call a class as a function")}var m=function b(v){var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};k(this,b),this.quill=v,this.options=p};m.DEFAULTS={},d.default=m},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=["error","warn","log","info"],m="warn";function b(p){if(k.indexOf(p)<=k.indexOf(m)){for(var s,o=arguments.length,e=Array(o>1?o-1:0),t=1;t<o;t++)e[t-1]=arguments[t];(s=console)[p].apply(s,e)}}function v(p){return k.reduce(function(s,o){return s[o]=b.bind(console,o,p),s},{})}b.level=v.level=function(p){m=p},d.default=v},function(y,d,c){var k=Array.prototype.slice,m=c(52),b=c(53),v=y.exports=function(e,t,u){return u||(u={}),e===t?!0:e instanceof Date&&t instanceof Date?e.getTime()===t.getTime():!e||!t||typeof e!="object"&&typeof t!="object"?u.strict?e===t:e==t:o(e,t,u)};function p(e){return e==null}function s(e){return!(!e||typeof e!="object"||typeof e.length!="number"||typeof e.copy!="function"||typeof e.slice!="function"||e.length>0&&typeof e[0]!="number")}function o(e,t,u){var l,a;if(p(e)||p(t)||e.prototype!==t.prototype)return!1;if(b(e))return b(t)?(e=k.call(e),t=k.call(t),v(e,t,u)):!1;if(s(e)){if(!s(t)||e.length!==t.length)return!1;for(l=0;l<e.length;l++)if(e[l]!==t[l])return!1;return!0}try{var r=m(e),i=m(t)}catch{return!1}if(r.length!=i.length)return!1;for(r.sort(),i.sort(),l=r.length-1;l>=0;l--)if(r[l]!=i[l])return!1;for(l=r.length-1;l>=0;l--)if(a=r[l],!v(e[a],t[a],u))return!1;return typeof e==typeof t}},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(1),m=function(){function b(v,p,s){s===void 0&&(s={}),this.attrName=v,this.keyName=p;var o=k.Scope.TYPE&k.Scope.ATTRIBUTE;s.scope!=null?this.scope=s.scope&k.Scope.LEVEL|o:this.scope=k.Scope.ATTRIBUTE,s.whitelist!=null&&(this.whitelist=s.whitelist)}return b.keys=function(v){return[].map.call(v.attributes,function(p){return p.name})},b.prototype.add=function(v,p){return this.canAdd(v,p)?(v.setAttribute(this.keyName,p),!0):!1},b.prototype.canAdd=function(v,p){var s=k.query(v,k.Scope.BLOT&(this.scope|k.Scope.TYPE));return s==null?!1:this.whitelist==null?!0:typeof p=="string"?this.whitelist.indexOf(p.replace(/["']/g,""))>-1:this.whitelist.indexOf(p)>-1},b.prototype.remove=function(v){v.removeAttribute(this.keyName)},b.prototype.value=function(v){var p=v.getAttribute(this.keyName);return this.canAdd(v,p)&&p?p:""},b}();d.default=m},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.Code=void 0;var k=function(){function w(S,E){var g=[],A=!0,T=!1,x=void 0;try{for(var j=S[Symbol.iterator](),F;!(A=(F=j.next()).done)&&(g.push(F.value),!(E&&g.length===E));A=!0);}catch(U){T=!0,x=U}finally{try{!A&&j.return&&j.return()}finally{if(T)throw x}}return g}return function(S,E){if(Array.isArray(S))return S;if(Symbol.iterator in Object(S))return w(S,E);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function w(S,E){for(var g=0;g<E.length;g++){var A=E[g];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(S,A.key,A)}}return function(S,E,g){return E&&w(S.prototype,E),g&&w(S,g),S}}(),b=function w(S,E,g){S===null&&(S=Function.prototype);var A=Object.getOwnPropertyDescriptor(S,E);if(A===void 0){var T=Object.getPrototypeOf(S);return T===null?void 0:w(T,E,g)}else{if("value"in A)return A.value;var x=A.get;return x===void 0?void 0:x.call(g)}},v=c(2),p=i(v),s=c(0),o=i(s),e=c(4),t=i(e),u=c(6),l=i(u),a=c(7),r=i(a);function i(w){return w&&w.__esModule?w:{default:w}}function f(w,S){if(!(w instanceof S))throw new TypeError("Cannot call a class as a function")}function n(w,S){if(!w)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return S&&(typeof S=="object"||typeof S=="function")?S:w}function h(w,S){if(typeof S!="function"&&S!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof S);w.prototype=Object.create(S&&S.prototype,{constructor:{value:w,enumerable:!1,writable:!0,configurable:!0}}),S&&(Object.setPrototypeOf?Object.setPrototypeOf(w,S):w.__proto__=S)}var _=function(w){h(S,w);function S(){return f(this,S),n(this,(S.__proto__||Object.getPrototypeOf(S)).apply(this,arguments))}return S}(l.default);_.blotName="code",_.tagName="CODE";var O=function(w){h(S,w);function S(){return f(this,S),n(this,(S.__proto__||Object.getPrototypeOf(S)).apply(this,arguments))}return m(S,[{key:"delta",value:function(){var g=this,A=this.domNode.textContent;return A.endsWith(`
`)&&(A=A.slice(0,-1)),A.split(`
`).reduce(function(T,x){return T.insert(x).insert(`
`,g.formats())},new p.default)}},{key:"format",value:function(g,A){if(!(g===this.statics.blotName&&A)){var T=this.descendant(r.default,this.length()-1),x=k(T,1),j=x[0];j!=null&&j.deleteAt(j.length()-1,1),b(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"format",this).call(this,g,A)}}},{key:"formatAt",value:function(g,A,T,x){if(A!==0&&!(o.default.query(T,o.default.Scope.BLOCK)==null||T===this.statics.blotName&&x===this.statics.formats(this.domNode))){var j=this.newlineIndex(g);if(!(j<0||j>=g+A)){var F=this.newlineIndex(g,!0)+1,U=j-F+1,W=this.isolate(F,U),B=W.next;W.format(T,x),B instanceof S&&B.formatAt(0,g-F+A-U,T,x)}}}},{key:"insertAt",value:function(g,A,T){if(T==null){var x=this.descendant(r.default,g),j=k(x,2),F=j[0],U=j[1];F.insertAt(U,A)}}},{key:"length",value:function(){var g=this.domNode.textContent.length;return this.domNode.textContent.endsWith(`
`)?g:g+1}},{key:"newlineIndex",value:function(g){var A=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(A)return this.domNode.textContent.slice(0,g).lastIndexOf(`
`);var T=this.domNode.textContent.slice(g).indexOf(`
`);return T>-1?g+T:-1}},{key:"optimize",value:function(g){this.domNode.textContent.endsWith(`
`)||this.appendChild(o.default.create("text",`
`)),b(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"optimize",this).call(this,g);var A=this.next;A!=null&&A.prev===this&&A.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===A.statics.formats(A.domNode)&&(A.optimize(g),A.moveChildren(this),A.remove())}},{key:"replace",value:function(g){b(S.prototype.__proto__||Object.getPrototypeOf(S.prototype),"replace",this).call(this,g),[].slice.call(this.domNode.querySelectorAll("*")).forEach(function(A){var T=o.default.find(A);T==null?A.parentNode.removeChild(A):T instanceof o.default.Embed?T.remove():T.unwrap()})}}],[{key:"create",value:function(g){var A=b(S.__proto__||Object.getPrototypeOf(S),"create",this).call(this,g);return A.setAttribute("spellcheck",!1),A}},{key:"formats",value:function(){return!0}}]),S}(t.default);O.blotName="code-block",O.tagName="PRE",O.TAB="  ",d.Code=_,d.default=O},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(B){return typeof B}:function(B){return B&&typeof Symbol=="function"&&B.constructor===Symbol&&B!==Symbol.prototype?"symbol":typeof B},m=function(){function B(M,N){var q=[],R=!0,H=!1,C=void 0;try{for(var L=M[Symbol.iterator](),I;!(R=(I=L.next()).done)&&(q.push(I.value),!(N&&q.length===N));R=!0);}catch(z){H=!0,C=z}finally{try{!R&&L.return&&L.return()}finally{if(H)throw C}}return q}return function(M,N){if(Array.isArray(M))return M;if(Symbol.iterator in Object(M))return B(M,N);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function B(M,N){for(var q=0;q<N.length;q++){var R=N[q];R.enumerable=R.enumerable||!1,R.configurable=!0,"value"in R&&(R.writable=!0),Object.defineProperty(M,R.key,R)}}return function(M,N,q){return N&&B(M.prototype,N),q&&B(M,q),M}}(),v=c(2),p=A(v),s=c(20),o=A(s),e=c(0),t=A(e),u=c(13),l=A(u),a=c(24),r=A(a),i=c(4),f=A(i),n=c(16),h=A(n),_=c(21),O=A(_),w=c(11),S=A(w),E=c(3),g=A(E);function A(B){return B&&B.__esModule?B:{default:B}}function T(B,M,N){return M in B?Object.defineProperty(B,M,{value:N,enumerable:!0,configurable:!0,writable:!0}):B[M]=N,B}function x(B,M){if(!(B instanceof M))throw new TypeError("Cannot call a class as a function")}var j=/^[ -~]*$/,F=function(){function B(M){x(this,B),this.scroll=M,this.delta=this.getDelta()}return b(B,[{key:"applyDelta",value:function(N){var q=this,R=!1;this.scroll.update();var H=this.scroll.length();return this.scroll.batchStart(),N=W(N),N.reduce(function(C,L){var I=L.retain||L.delete||L.insert.length||1,z=L.attributes||{};if(L.insert!=null){if(typeof L.insert=="string"){var K=L.insert;K.endsWith(`
`)&&R&&(R=!1,K=K.slice(0,-1)),C>=H&&!K.endsWith(`
`)&&(R=!0),q.scroll.insertAt(C,K);var Z=q.scroll.line(C),X=m(Z,2),J=X[0],ee=X[1],ie=(0,g.default)({},(0,i.bubbleFormats)(J));if(J instanceof f.default){var oe=J.descendant(t.default.Leaf,ee),se=m(oe,1),ue=se[0];ie=(0,g.default)(ie,(0,i.bubbleFormats)(ue))}z=o.default.attributes.diff(ie,z)||{}}else if(k(L.insert)==="object"){var V=Object.keys(L.insert)[0];if(V==null)return C;q.scroll.insertAt(C,V,L.insert[V])}H+=I}return Object.keys(z).forEach(function($){q.scroll.formatAt(C,I,$,z[$])}),C+I},0),N.reduce(function(C,L){return typeof L.delete=="number"?(q.scroll.deleteAt(C,L.delete),C):C+(L.retain||L.insert.length||1)},0),this.scroll.batchEnd(),this.update(N)}},{key:"deleteText",value:function(N,q){return this.scroll.deleteAt(N,q),this.update(new p.default().retain(N).delete(q))}},{key:"formatLine",value:function(N,q){var R=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.scroll.update(),Object.keys(H).forEach(function(C){if(!(R.scroll.whitelist!=null&&!R.scroll.whitelist[C])){var L=R.scroll.lines(N,Math.max(q,1)),I=q;L.forEach(function(z){var K=z.length();if(!(z instanceof l.default))z.format(C,H[C]);else{var Z=N-z.offset(R.scroll),X=z.newlineIndex(Z+I)-Z+1;z.formatAt(Z,X,C,H[C])}I-=K})}}),this.scroll.optimize(),this.update(new p.default().retain(N).retain(q,(0,O.default)(H)))}},{key:"formatText",value:function(N,q){var R=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return Object.keys(H).forEach(function(C){R.scroll.formatAt(N,q,C,H[C])}),this.update(new p.default().retain(N).retain(q,(0,O.default)(H)))}},{key:"getContents",value:function(N,q){return this.delta.slice(N,N+q)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce(function(N,q){return N.concat(q.delta())},new p.default)}},{key:"getFormat",value:function(N){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,R=[],H=[];q===0?this.scroll.path(N).forEach(function(L){var I=m(L,1),z=I[0];z instanceof f.default?R.push(z):z instanceof t.default.Leaf&&H.push(z)}):(R=this.scroll.lines(N,q),H=this.scroll.descendants(t.default.Leaf,N,q));var C=[R,H].map(function(L){if(L.length===0)return{};for(var I=(0,i.bubbleFormats)(L.shift());Object.keys(I).length>0;){var z=L.shift();if(z==null)return I;I=U((0,i.bubbleFormats)(z),I)}return I});return g.default.apply(g.default,C)}},{key:"getText",value:function(N,q){return this.getContents(N,q).filter(function(R){return typeof R.insert=="string"}).map(function(R){return R.insert}).join("")}},{key:"insertEmbed",value:function(N,q,R){return this.scroll.insertAt(N,q,R),this.update(new p.default().retain(N).insert(T({},q,R)))}},{key:"insertText",value:function(N,q){var R=this,H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return q=q.replace(/\r\n/g,`
`).replace(/\r/g,`
`),this.scroll.insertAt(N,q),Object.keys(H).forEach(function(C){R.scroll.formatAt(N,q.length,C,H[C])}),this.update(new p.default().retain(N).insert(q,(0,O.default)(H)))}},{key:"isBlank",value:function(){if(this.scroll.children.length==0)return!0;if(this.scroll.children.length>1)return!1;var N=this.scroll.children.head;return N.statics.blotName!==f.default.blotName||N.children.length>1?!1:N.children.head instanceof h.default}},{key:"removeFormat",value:function(N,q){var R=this.getText(N,q),H=this.scroll.line(N+q),C=m(H,2),L=C[0],I=C[1],z=0,K=new p.default;L!=null&&(L instanceof l.default?z=L.newlineIndex(I)-I+1:z=L.length()-I,K=L.delta().slice(I,I+z-1).insert(`
`));var Z=this.getContents(N,q+z),X=Z.diff(new p.default().insert(R).concat(K)),J=new p.default().retain(N).concat(X);return this.applyDelta(J)}},{key:"update",value:function(N){var q=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],R=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0,H=this.delta;if(q.length===1&&q[0].type==="characterData"&&q[0].target.data.match(j)&&t.default.find(q[0].target)){var C=t.default.find(q[0].target),L=(0,i.bubbleFormats)(C),I=C.offset(this.scroll),z=q[0].oldValue.replace(r.default.CONTENTS,""),K=new p.default().insert(z),Z=new p.default().insert(C.value()),X=new p.default().retain(I).concat(K.diff(Z,R));N=X.reduce(function(J,ee){return ee.insert?J.insert(ee.insert,L):J.push(ee)},new p.default),this.delta=H.compose(N)}else this.delta=this.getDelta(),(!N||!(0,S.default)(H.compose(N),this.delta))&&(N=H.diff(this.delta,R));return N}}]),B}();function U(B,M){return Object.keys(M).reduce(function(N,q){return B[q]==null||(M[q]===B[q]?N[q]=M[q]:Array.isArray(M[q])?M[q].indexOf(B[q])<0&&(N[q]=M[q].concat([B[q]])):N[q]=[M[q],B[q]]),N},{})}function W(B){return B.reduce(function(M,N){if(N.insert===1){var q=(0,O.default)(N.attributes);return delete q.image,M.insert({image:N.attributes.image},q)}if(N.attributes!=null&&(N.attributes.list===!0||N.attributes.bullet===!0)&&(N=(0,O.default)(N),N.attributes.list?N.attributes.list="ordered":(N.attributes.list="bullet",delete N.attributes.bullet)),typeof N.insert=="string"){var R=N.insert.replace(/\r\n/g,`
`).replace(/\r/g,`
`);return M.insert(R,N.attributes)}return M.push(N)},new p.default)}d.default=F},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.Range=void 0;var k=function(){function w(S,E){var g=[],A=!0,T=!1,x=void 0;try{for(var j=S[Symbol.iterator](),F;!(A=(F=j.next()).done)&&(g.push(F.value),!(E&&g.length===E));A=!0);}catch(U){T=!0,x=U}finally{try{!A&&j.return&&j.return()}finally{if(T)throw x}}return g}return function(S,E){if(Array.isArray(S))return S;if(Symbol.iterator in Object(S))return w(S,E);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function w(S,E){for(var g=0;g<E.length;g++){var A=E[g];A.enumerable=A.enumerable||!1,A.configurable=!0,"value"in A&&(A.writable=!0),Object.defineProperty(S,A.key,A)}}return function(S,E,g){return E&&w(S.prototype,E),g&&w(S,g),S}}(),b=c(0),v=r(b),p=c(21),s=r(p),o=c(11),e=r(o),t=c(8),u=r(t),l=c(10),a=r(l);function r(w){return w&&w.__esModule?w:{default:w}}function i(w){if(Array.isArray(w)){for(var S=0,E=Array(w.length);S<w.length;S++)E[S]=w[S];return E}else return Array.from(w)}function f(w,S){if(!(w instanceof S))throw new TypeError("Cannot call a class as a function")}var n=(0,a.default)("quill:selection"),h=function w(S){var E=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;f(this,w),this.index=S,this.length=E},_=function(){function w(S,E){var g=this;f(this,w),this.emitter=E,this.scroll=S,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=v.default.create("cursor",this),this.lastRange=this.savedRange=new h(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,function(){g.mouseDown||setTimeout(g.update.bind(g,u.default.sources.USER),1)}),this.emitter.on(u.default.events.EDITOR_CHANGE,function(A,T){A===u.default.events.TEXT_CHANGE&&T.length()>0&&g.update(u.default.sources.SILENT)}),this.emitter.on(u.default.events.SCROLL_BEFORE_UPDATE,function(){if(!!g.hasFocus()){var A=g.getNativeRange();A!=null&&A.start.node!==g.cursor.textNode&&g.emitter.once(u.default.events.SCROLL_UPDATE,function(){try{g.setNativeRange(A.start.node,A.start.offset,A.end.node,A.end.offset)}catch{}})}}),this.emitter.on(u.default.events.SCROLL_OPTIMIZE,function(A,T){if(T.range){var x=T.range,j=x.startNode,F=x.startOffset,U=x.endNode,W=x.endOffset;g.setNativeRange(j,F,U,W)}}),this.update(u.default.sources.SILENT)}return m(w,[{key:"handleComposition",value:function(){var E=this;this.root.addEventListener("compositionstart",function(){E.composing=!0}),this.root.addEventListener("compositionend",function(){if(E.composing=!1,E.cursor.parent){var g=E.cursor.restore();if(!g)return;setTimeout(function(){E.setNativeRange(g.startNode,g.startOffset,g.endNode,g.endOffset)},1)}})}},{key:"handleDragging",value:function(){var E=this;this.emitter.listenDOM("mousedown",document.body,function(){E.mouseDown=!0}),this.emitter.listenDOM("mouseup",document.body,function(){E.mouseDown=!1,E.update(u.default.sources.USER)})}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(E,g){if(!(this.scroll.whitelist!=null&&!this.scroll.whitelist[E])){this.scroll.update();var A=this.getNativeRange();if(!(A==null||!A.native.collapsed||v.default.query(E,v.default.Scope.BLOCK))){if(A.start.node!==this.cursor.textNode){var T=v.default.find(A.start.node,!1);if(T==null)return;if(T instanceof v.default.Leaf){var x=T.split(A.start.offset);T.parent.insertBefore(this.cursor,x)}else T.insertBefore(this.cursor,A.start.node);this.cursor.attach()}this.cursor.format(E,g),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(E){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,A=this.scroll.length();E=Math.min(E,A-1),g=Math.min(E+g,A-1)-E;var T=void 0,x=this.scroll.leaf(E),j=k(x,2),F=j[0],U=j[1];if(F==null)return null;var W=F.position(U,!0),B=k(W,2);T=B[0],U=B[1];var M=document.createRange();if(g>0){M.setStart(T,U);var N=this.scroll.leaf(E+g),q=k(N,2);if(F=q[0],U=q[1],F==null)return null;var R=F.position(U,!0),H=k(R,2);return T=H[0],U=H[1],M.setEnd(T,U),M.getBoundingClientRect()}else{var C="left",L=void 0;return T instanceof Text?(U<T.data.length?(M.setStart(T,U),M.setEnd(T,U+1)):(M.setStart(T,U-1),M.setEnd(T,U),C="right"),L=M.getBoundingClientRect()):(L=F.domNode.getBoundingClientRect(),U>0&&(C="right")),{bottom:L.top+L.height,height:L.height,left:L[C],right:L[C],top:L.top,width:0}}}},{key:"getNativeRange",value:function(){var E=document.getSelection();if(E==null||E.rangeCount<=0)return null;var g=E.getRangeAt(0);if(g==null)return null;var A=this.normalizeNative(g);return n.info("getNativeRange",A),A}},{key:"getRange",value:function(){var E=this.getNativeRange();if(E==null)return[null,null];var g=this.normalizedToRange(E);return[g,E]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(E){var g=this,A=[[E.start.node,E.start.offset]];E.native.collapsed||A.push([E.end.node,E.end.offset]);var T=A.map(function(F){var U=k(F,2),W=U[0],B=U[1],M=v.default.find(W,!0),N=M.offset(g.scroll);return B===0?N:M instanceof v.default.Container?N+M.length():N+M.index(W,B)}),x=Math.min(Math.max.apply(Math,i(T)),this.scroll.length()-1),j=Math.min.apply(Math,[x].concat(i(T)));return new h(j,x-j)}},{key:"normalizeNative",value:function(E){if(!O(this.root,E.startContainer)||!E.collapsed&&!O(this.root,E.endContainer))return null;var g={start:{node:E.startContainer,offset:E.startOffset},end:{node:E.endContainer,offset:E.endOffset},native:E};return[g.start,g.end].forEach(function(A){for(var T=A.node,x=A.offset;!(T instanceof Text)&&T.childNodes.length>0;)if(T.childNodes.length>x)T=T.childNodes[x],x=0;else if(T.childNodes.length===x)T=T.lastChild,x=T instanceof Text?T.data.length:T.childNodes.length+1;else break;A.node=T,A.offset=x}),g}},{key:"rangeToNative",value:function(E){var g=this,A=E.collapsed?[E.index]:[E.index,E.index+E.length],T=[],x=this.scroll.length();return A.forEach(function(j,F){j=Math.min(x-1,j);var U=void 0,W=g.scroll.leaf(j),B=k(W,2),M=B[0],N=B[1],q=M.position(N,F!==0),R=k(q,2);U=R[0],N=R[1],T.push(U,N)}),T.length<2&&(T=T.concat(T)),T}},{key:"scrollIntoView",value:function(E){var g=this.lastRange;if(g!=null){var A=this.getBounds(g.index,g.length);if(A!=null){var T=this.scroll.length()-1,x=this.scroll.line(Math.min(g.index,T)),j=k(x,1),F=j[0],U=F;if(g.length>0){var W=this.scroll.line(Math.min(g.index+g.length,T)),B=k(W,1);U=B[0]}if(!(F==null||U==null)){var M=E.getBoundingClientRect();A.top<M.top?E.scrollTop-=M.top-A.top:A.bottom>M.bottom&&(E.scrollTop+=A.bottom-M.bottom)}}}}},{key:"setNativeRange",value:function(E,g){var A=arguments.length>2&&arguments[2]!==void 0?arguments[2]:E,T=arguments.length>3&&arguments[3]!==void 0?arguments[3]:g,x=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!1;if(n.info("setNativeRange",E,g,A,T),!(E!=null&&(this.root.parentNode==null||E.parentNode==null||A.parentNode==null))){var j=document.getSelection();if(j!=null)if(E!=null){this.hasFocus()||this.root.focus();var F=(this.getNativeRange()||{}).native;if(F==null||x||E!==F.startContainer||g!==F.startOffset||A!==F.endContainer||T!==F.endOffset){E.tagName=="BR"&&(g=[].indexOf.call(E.parentNode.childNodes,E),E=E.parentNode),A.tagName=="BR"&&(T=[].indexOf.call(A.parentNode.childNodes,A),A=A.parentNode);var U=document.createRange();U.setStart(E,g),U.setEnd(A,T),j.removeAllRanges(),j.addRange(U)}}else j.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(E){var g=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,A=arguments.length>2&&arguments[2]!==void 0?arguments[2]:u.default.sources.API;if(typeof g=="string"&&(A=g,g=!1),n.info("setRange",E),E!=null){var T=this.rangeToNative(E);this.setNativeRange.apply(this,i(T).concat([g]))}else this.setNativeRange(null);this.update(A)}},{key:"update",value:function(){var E=arguments.length>0&&arguments[0]!==void 0?arguments[0]:u.default.sources.USER,g=this.lastRange,A=this.getRange(),T=k(A,2),x=T[0],j=T[1];if(this.lastRange=x,this.lastRange!=null&&(this.savedRange=this.lastRange),!(0,e.default)(g,this.lastRange)){var F;!this.composing&&j!=null&&j.native.collapsed&&j.start.node!==this.cursor.textNode&&this.cursor.restore();var U=[u.default.events.SELECTION_CHANGE,(0,s.default)(this.lastRange),(0,s.default)(g),E];if((F=this.emitter).emit.apply(F,[u.default.events.EDITOR_CHANGE].concat(U)),E!==u.default.sources.SILENT){var W;(W=this.emitter).emit.apply(W,U)}}}}]),w}();function O(w,S){try{S.parentNode}catch{return!1}return S instanceof Text&&(S=S.parentNode),w.contains(S)}d.Range=h,d.default=_},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(0),v=p(b);function p(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return k(l,[{key:"insertInto",value:function(r,i){r.children.length===0?m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"insertInto",this).call(this,r,i):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),l}(v.default.Embed);t.blotName="break",t.tagName="BR",d.default=t},function(y,d,c){var k=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var u in t)t.hasOwnProperty(u)&&(e[u]=t[u])};return function(e,t){o(e,t);function u(){this.constructor=e}e.prototype=t===null?Object.create(t):(u.prototype=t.prototype,new u)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(44),b=c(30),v=c(1),p=function(o){k(e,o);function e(t){var u=o.call(this,t)||this;return u.build(),u}return e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){o.prototype.attach.call(this),this.children.forEach(function(t){t.attach()})},e.prototype.build=function(){var t=this;this.children=new m.default,[].slice.call(this.domNode.childNodes).reverse().forEach(function(u){try{var l=s(u);t.insertBefore(l,t.children.head||void 0)}catch(a){if(a instanceof v.ParchmentError)return;throw a}})},e.prototype.deleteAt=function(t,u){if(t===0&&u===this.length())return this.remove();this.children.forEachAt(t,u,function(l,a,r){l.deleteAt(a,r)})},e.prototype.descendant=function(t,u){var l=this.children.find(u),a=l[0],r=l[1];return t.blotName==null&&t(a)||t.blotName!=null&&a instanceof t?[a,r]:a instanceof e?a.descendant(t,r):[null,-1]},e.prototype.descendants=function(t,u,l){u===void 0&&(u=0),l===void 0&&(l=Number.MAX_VALUE);var a=[],r=l;return this.children.forEachAt(u,l,function(i,f,n){(t.blotName==null&&t(i)||t.blotName!=null&&i instanceof t)&&a.push(i),i instanceof e&&(a=a.concat(i.descendants(t,f,r))),r-=n}),a},e.prototype.detach=function(){this.children.forEach(function(t){t.detach()}),o.prototype.detach.call(this)},e.prototype.formatAt=function(t,u,l,a){this.children.forEachAt(t,u,function(r,i,f){r.formatAt(i,f,l,a)})},e.prototype.insertAt=function(t,u,l){var a=this.children.find(t),r=a[0],i=a[1];if(r)r.insertAt(i,u,l);else{var f=l==null?v.create("text",u):v.create(u,l);this.appendChild(f)}},e.prototype.insertBefore=function(t,u){if(this.statics.allowedChildren!=null&&!this.statics.allowedChildren.some(function(l){return t instanceof l}))throw new v.ParchmentError("Cannot insert "+t.statics.blotName+" into "+this.statics.blotName);t.insertInto(this,u)},e.prototype.length=function(){return this.children.reduce(function(t,u){return t+u.length()},0)},e.prototype.moveChildren=function(t,u){this.children.forEach(function(l){t.insertBefore(l,u)})},e.prototype.optimize=function(t){if(o.prototype.optimize.call(this,t),this.children.length===0)if(this.statics.defaultChild!=null){var u=v.create(this.statics.defaultChild);this.appendChild(u),u.optimize(t)}else this.remove()},e.prototype.path=function(t,u){u===void 0&&(u=!1);var l=this.children.find(t,u),a=l[0],r=l[1],i=[[this,t]];return a instanceof e?i.concat(a.path(r,u)):(a!=null&&i.push([a,r]),i)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replace=function(t){t instanceof e&&t.moveChildren(this),o.prototype.replace.call(this,t)},e.prototype.split=function(t,u){if(u===void 0&&(u=!1),!u){if(t===0)return this;if(t===this.length())return this.next}var l=this.clone();return this.parent.insertBefore(l,this.next),this.children.forEachAt(t,this.length(),function(a,r,i){a=a.split(r,u),l.appendChild(a)}),l},e.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},e.prototype.update=function(t,u){var l=this,a=[],r=[];t.forEach(function(i){i.target===l.domNode&&i.type==="childList"&&(a.push.apply(a,i.addedNodes),r.push.apply(r,i.removedNodes))}),r.forEach(function(i){if(!(i.parentNode!=null&&i.tagName!=="IFRAME"&&document.body.compareDocumentPosition(i)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var f=v.find(i);f!=null&&(f.domNode.parentNode==null||f.domNode.parentNode===l.domNode)&&f.detach()}}),a.filter(function(i){return i.parentNode==l.domNode}).sort(function(i,f){return i===f?0:i.compareDocumentPosition(f)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1}).forEach(function(i){var f=null;i.nextSibling!=null&&(f=v.find(i.nextSibling));var n=s(i);(n.next!=f||n.next==null)&&(n.parent!=null&&n.parent.removeChild(l),l.insertBefore(n,f||void 0))})},e}(b.default);function s(o){var e=v.find(o);if(e==null)try{e=v.create(o)}catch{e=v.create(v.Scope.INLINE),[].slice.call(o.childNodes).forEach(function(u){e.domNode.appendChild(u)}),o.parentNode&&o.parentNode.replaceChild(e.domNode,o),e.attach()}return e}d.default=p},function(y,d,c){var k=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var u in t)t.hasOwnProperty(u)&&(e[u]=t[u])};return function(e,t){o(e,t);function u(){this.constructor=e}e.prototype=t===null?Object.create(t):(u.prototype=t.prototype,new u)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(12),b=c(31),v=c(17),p=c(1),s=function(o){k(e,o);function e(t){var u=o.call(this,t)||this;return u.attributes=new b.default(u.domNode),u}return e.formats=function(t){if(typeof this.tagName=="string")return!0;if(Array.isArray(this.tagName))return t.tagName.toLowerCase()},e.prototype.format=function(t,u){var l=p.query(t);l instanceof m.default?this.attributes.attribute(l,u):u&&l!=null&&(t!==this.statics.blotName||this.formats()[t]!==u)&&this.replaceWith(t,u)},e.prototype.formats=function(){var t=this.attributes.values(),u=this.statics.formats(this.domNode);return u!=null&&(t[this.statics.blotName]=u),t},e.prototype.replaceWith=function(t,u){var l=o.prototype.replaceWith.call(this,t,u);return this.attributes.copy(l),l},e.prototype.update=function(t,u){var l=this;o.prototype.update.call(this,t,u),t.some(function(a){return a.target===l.domNode&&a.type==="attributes"})&&this.attributes.build()},e.prototype.wrap=function(t,u){var l=o.prototype.wrap.call(this,t,u);return l instanceof e&&l.statics.scope===this.statics.scope&&this.attributes.move(l),l},e}(v.default);d.default=s},function(y,d,c){var k=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){p(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(30),b=c(1),v=function(p){k(s,p);function s(){return p!==null&&p.apply(this,arguments)||this}return s.value=function(o){return!0},s.prototype.index=function(o,e){return this.domNode===o||this.domNode.compareDocumentPosition(o)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},s.prototype.position=function(o,e){var t=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return o>0&&(t+=1),[this.parent.domNode,t]},s.prototype.value=function(){var o;return o={},o[this.statics.blotName]=this.statics.value(this.domNode)||!0,o},s.scope=b.Scope.INLINE_BLOT,s}(m.default);d.default=v},function(y,d,c){var k=c(11),m=c(3),b={attributes:{compose:function(p,s,o){typeof p!="object"&&(p={}),typeof s!="object"&&(s={});var e=m(!0,{},s);o||(e=Object.keys(e).reduce(function(u,l){return e[l]!=null&&(u[l]=e[l]),u},{}));for(var t in p)p[t]!==void 0&&s[t]===void 0&&(e[t]=p[t]);return Object.keys(e).length>0?e:void 0},diff:function(p,s){typeof p!="object"&&(p={}),typeof s!="object"&&(s={});var o=Object.keys(p).concat(Object.keys(s)).reduce(function(e,t){return k(p[t],s[t])||(e[t]=s[t]===void 0?null:s[t]),e},{});return Object.keys(o).length>0?o:void 0},transform:function(p,s,o){if(typeof p!="object")return s;if(typeof s=="object"){if(!o)return s;var e=Object.keys(s).reduce(function(t,u){return p[u]===void 0&&(t[u]=s[u]),t},{});return Object.keys(e).length>0?e:void 0}}},iterator:function(p){return new v(p)},length:function(p){return typeof p.delete=="number"?p.delete:typeof p.retain=="number"?p.retain:typeof p.insert=="string"?p.insert.length:1}};function v(p){this.ops=p,this.index=0,this.offset=0}v.prototype.hasNext=function(){return this.peekLength()<1/0},v.prototype.next=function(p){p||(p=1/0);var s=this.ops[this.index];if(s){var o=this.offset,e=b.length(s);if(p>=e-o?(p=e-o,this.index+=1,this.offset=0):this.offset+=p,typeof s.delete=="number")return{delete:p};var t={};return s.attributes&&(t.attributes=s.attributes),typeof s.retain=="number"?t.retain=p:typeof s.insert=="string"?t.insert=s.insert.substr(o,p):t.insert=s.insert,t}else return{retain:1/0}},v.prototype.peek=function(){return this.ops[this.index]},v.prototype.peekLength=function(){return this.ops[this.index]?b.length(this.ops[this.index])-this.offset:1/0},v.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},v.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var p=this.offset,s=this.index,o=this.next(),e=this.ops.slice(this.index);return this.offset=p,this.index=s,[o].concat(e)}else return[]},y.exports=b},function(y,d){var c=function(){function k(l,a){return a!=null&&l instanceof a}var m;try{m=Map}catch{m=function(){}}var b;try{b=Set}catch{b=function(){}}var v;try{v=Promise}catch{v=function(){}}function p(l,a,r,i,f){typeof a=="object"&&(r=a.depth,i=a.prototype,f=a.includeNonEnumerable,a=a.circular);var n=[],h=[],_=typeof Buffer!="undefined";typeof a=="undefined"&&(a=!0),typeof r=="undefined"&&(r=1/0);function O(w,S){if(w===null)return null;if(S===0)return w;var E,g;if(typeof w!="object")return w;if(k(w,m))E=new m;else if(k(w,b))E=new b;else if(k(w,v))E=new v(function(M,N){w.then(function(q){M(O(q,S-1))},function(q){N(O(q,S-1))})});else if(p.__isArray(w))E=[];else if(p.__isRegExp(w))E=new RegExp(w.source,u(w)),w.lastIndex&&(E.lastIndex=w.lastIndex);else if(p.__isDate(w))E=new Date(w.getTime());else{if(_&&Buffer.isBuffer(w))return Buffer.allocUnsafe?E=Buffer.allocUnsafe(w.length):E=new Buffer(w.length),w.copy(E),E;k(w,Error)?E=Object.create(w):typeof i=="undefined"?(g=Object.getPrototypeOf(w),E=Object.create(g)):(E=Object.create(i),g=i)}if(a){var A=n.indexOf(w);if(A!=-1)return h[A];n.push(w),h.push(E)}k(w,m)&&w.forEach(function(M,N){var q=O(N,S-1),R=O(M,S-1);E.set(q,R)}),k(w,b)&&w.forEach(function(M){var N=O(M,S-1);E.add(N)});for(var T in w){var x;g&&(x=Object.getOwnPropertyDescriptor(g,T)),!(x&&x.set==null)&&(E[T]=O(w[T],S-1))}if(Object.getOwnPropertySymbols)for(var j=Object.getOwnPropertySymbols(w),T=0;T<j.length;T++){var F=j[T],U=Object.getOwnPropertyDescriptor(w,F);U&&!U.enumerable&&!f||(E[F]=O(w[F],S-1),U.enumerable||Object.defineProperty(E,F,{enumerable:!1}))}if(f)for(var W=Object.getOwnPropertyNames(w),T=0;T<W.length;T++){var B=W[T],U=Object.getOwnPropertyDescriptor(w,B);U&&U.enumerable||(E[B]=O(w[B],S-1),Object.defineProperty(E,B,{enumerable:!1}))}return E}return O(l,r)}p.clonePrototype=function(a){if(a===null)return null;var r=function(){};return r.prototype=a,new r};function s(l){return Object.prototype.toString.call(l)}p.__objToStr=s;function o(l){return typeof l=="object"&&s(l)==="[object Date]"}p.__isDate=o;function e(l){return typeof l=="object"&&s(l)==="[object Array]"}p.__isArray=e;function t(l){return typeof l=="object"&&s(l)==="[object RegExp]"}p.__isRegExp=t;function u(l){var a="";return l.global&&(a+="g"),l.ignoreCase&&(a+="i"),l.multiline&&(a+="m"),a}return p.__getRegExpFlags=u,p}();typeof y=="object"&&y.exports&&(y.exports=c)},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function E(g,A){var T=[],x=!0,j=!1,F=void 0;try{for(var U=g[Symbol.iterator](),W;!(x=(W=U.next()).done)&&(T.push(W.value),!(A&&T.length===A));x=!0);}catch(B){j=!0,F=B}finally{try{!x&&U.return&&U.return()}finally{if(j)throw F}}return T}return function(g,A){if(Array.isArray(g))return g;if(Symbol.iterator in Object(g))return E(g,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function E(g,A){for(var T=0;T<A.length;T++){var x=A[T];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(g,x.key,x)}}return function(g,A,T){return A&&E(g.prototype,A),T&&E(g,T),g}}(),b=function E(g,A,T){g===null&&(g=Function.prototype);var x=Object.getOwnPropertyDescriptor(g,A);if(x===void 0){var j=Object.getPrototypeOf(g);return j===null?void 0:E(j,A,T)}else{if("value"in x)return x.value;var F=x.get;return F===void 0?void 0:F.call(T)}},v=c(0),p=n(v),s=c(8),o=n(s),e=c(4),t=n(e),u=c(16),l=n(u),a=c(13),r=n(a),i=c(25),f=n(i);function n(E){return E&&E.__esModule?E:{default:E}}function h(E,g){if(!(E instanceof g))throw new TypeError("Cannot call a class as a function")}function _(E,g){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:E}function O(E,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);E.prototype=Object.create(g&&g.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(E,g):E.__proto__=g)}function w(E){return E instanceof t.default||E instanceof e.BlockEmbed}var S=function(E){O(g,E);function g(A,T){h(this,g);var x=_(this,(g.__proto__||Object.getPrototypeOf(g)).call(this,A));return x.emitter=T.emitter,Array.isArray(T.whitelist)&&(x.whitelist=T.whitelist.reduce(function(j,F){return j[F]=!0,j},{})),x.domNode.addEventListener("DOMNodeInserted",function(){}),x.optimize(),x.enable(),x}return m(g,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(T,x){var j=this.line(T),F=k(j,2),U=F[0],W=F[1],B=this.line(T+x),M=k(B,1),N=M[0];if(b(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"deleteAt",this).call(this,T,x),N!=null&&U!==N&&W>0){if(U instanceof e.BlockEmbed||N instanceof e.BlockEmbed){this.optimize();return}if(U instanceof r.default){var q=U.newlineIndex(U.length(),!0);if(q>-1&&(U=U.split(q+1),U===N)){this.optimize();return}}else if(N instanceof r.default){var R=N.newlineIndex(0);R>-1&&N.split(R+1)}var H=N.children.head instanceof l.default?null:N.children.head;U.moveChildren(N,H),U.remove()}this.optimize()}},{key:"enable",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;this.domNode.setAttribute("contenteditable",T)}},{key:"formatAt",value:function(T,x,j,F){this.whitelist!=null&&!this.whitelist[j]||(b(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"formatAt",this).call(this,T,x,j,F),this.optimize())}},{key:"insertAt",value:function(T,x,j){if(!(j!=null&&this.whitelist!=null&&!this.whitelist[x])){if(T>=this.length())if(j==null||p.default.query(x,p.default.Scope.BLOCK)==null){var F=p.default.create(this.statics.defaultChild);this.appendChild(F),j==null&&x.endsWith(`
`)&&(x=x.slice(0,-1)),F.insertAt(0,x,j)}else{var U=p.default.create(x,j);this.appendChild(U)}else b(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"insertAt",this).call(this,T,x,j);this.optimize()}}},{key:"insertBefore",value:function(T,x){if(T.statics.scope===p.default.Scope.INLINE_BLOT){var j=p.default.create(this.statics.defaultChild);j.appendChild(T),T=j}b(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"insertBefore",this).call(this,T,x)}},{key:"leaf",value:function(T){return this.path(T).pop()||[null,-1]}},{key:"line",value:function(T){return T===this.length()?this.line(T-1):this.descendant(w,T)}},{key:"lines",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.MAX_VALUE,j=function F(U,W,B){var M=[],N=B;return U.children.forEachAt(W,B,function(q,R,H){w(q)?M.push(q):q instanceof p.default.Container&&(M=M.concat(F(q,R,N))),N-=H}),M};return j(this,T,x)}},{key:"optimize",value:function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],x=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.batch!==!0&&(b(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"optimize",this).call(this,T,x),T.length>0&&this.emitter.emit(o.default.events.SCROLL_OPTIMIZE,T,x))}},{key:"path",value:function(T){return b(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"path",this).call(this,T).slice(1)}},{key:"update",value:function(T){if(this.batch!==!0){var x=o.default.sources.USER;typeof T=="string"&&(x=T),Array.isArray(T)||(T=this.observer.takeRecords()),T.length>0&&this.emitter.emit(o.default.events.SCROLL_BEFORE_UPDATE,x,T),b(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"update",this).call(this,T.concat([])),T.length>0&&this.emitter.emit(o.default.events.SCROLL_UPDATE,x,T)}}}]),g}(p.default.Scroll);S.blotName="scroll",S.className="ql-editor",S.tagName="DIV",S.defaultChild="block",S.allowedChildren=[t.default,e.BlockEmbed,f.default],d.default=S},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.SHORTKEY=d.default=void 0;var k=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(L){return typeof L}:function(L){return L&&typeof Symbol=="function"&&L.constructor===Symbol&&L!==Symbol.prototype?"symbol":typeof L},m=function(){function L(I,z){var K=[],Z=!0,X=!1,J=void 0;try{for(var ee=I[Symbol.iterator](),ie;!(Z=(ie=ee.next()).done)&&(K.push(ie.value),!(z&&K.length===z));Z=!0);}catch(oe){X=!0,J=oe}finally{try{!Z&&ee.return&&ee.return()}finally{if(X)throw J}}return K}return function(I,z){if(Array.isArray(I))return I;if(Symbol.iterator in Object(I))return L(I,z);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function L(I,z){for(var K=0;K<z.length;K++){var Z=z[K];Z.enumerable=Z.enumerable||!1,Z.configurable=!0,"value"in Z&&(Z.writable=!0),Object.defineProperty(I,Z.key,Z)}}return function(I,z,K){return z&&L(I.prototype,z),K&&L(I,K),I}}(),v=c(21),p=E(v),s=c(11),o=E(s),e=c(3),t=E(e),u=c(2),l=E(u),a=c(20),r=E(a),i=c(0),f=E(i),n=c(5),h=E(n),_=c(10),O=E(_),w=c(9),S=E(w);function E(L){return L&&L.__esModule?L:{default:L}}function g(L,I,z){return I in L?Object.defineProperty(L,I,{value:z,enumerable:!0,configurable:!0,writable:!0}):L[I]=z,L}function A(L,I){if(!(L instanceof I))throw new TypeError("Cannot call a class as a function")}function T(L,I){if(!L)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return I&&(typeof I=="object"||typeof I=="function")?I:L}function x(L,I){if(typeof I!="function"&&I!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof I);L.prototype=Object.create(I&&I.prototype,{constructor:{value:L,enumerable:!1,writable:!0,configurable:!0}}),I&&(Object.setPrototypeOf?Object.setPrototypeOf(L,I):L.__proto__=I)}var j=(0,O.default)("quill:keyboard"),F=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",U=function(L){x(I,L),b(I,null,[{key:"match",value:function(K,Z){return Z=C(Z),["altKey","ctrlKey","metaKey","shiftKey"].some(function(X){return!!Z[X]!==K[X]&&Z[X]!==null})?!1:Z.key===(K.which||K.keyCode)}}]);function I(z,K){A(this,I);var Z=T(this,(I.__proto__||Object.getPrototypeOf(I)).call(this,z,K));return Z.bindings={},Object.keys(Z.options.bindings).forEach(function(X){X==="list autofill"&&z.scroll.whitelist!=null&&!z.scroll.whitelist.list||Z.options.bindings[X]&&Z.addBinding(Z.options.bindings[X])}),Z.addBinding({key:I.keys.ENTER,shiftKey:null},q),Z.addBinding({key:I.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},function(){}),/Firefox/i.test(navigator.userAgent)?(Z.addBinding({key:I.keys.BACKSPACE},{collapsed:!0},B),Z.addBinding({key:I.keys.DELETE},{collapsed:!0},M)):(Z.addBinding({key:I.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},B),Z.addBinding({key:I.keys.DELETE},{collapsed:!0,suffix:/^.?$/},M)),Z.addBinding({key:I.keys.BACKSPACE},{collapsed:!1},N),Z.addBinding({key:I.keys.DELETE},{collapsed:!1},N),Z.addBinding({key:I.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},B),Z.listen(),Z}return b(I,[{key:"addBinding",value:function(K){var Z=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},X=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},J=C(K);if(J==null||J.key==null)return j.warn("Attempted to add invalid keyboard binding",J);typeof Z=="function"&&(Z={handler:Z}),typeof X=="function"&&(X={handler:X}),J=(0,t.default)(J,Z,X),this.bindings[J.key]=this.bindings[J.key]||[],this.bindings[J.key].push(J)}},{key:"listen",value:function(){var K=this;this.quill.root.addEventListener("keydown",function(Z){if(!Z.defaultPrevented){var X=Z.which||Z.keyCode,J=(K.bindings[X]||[]).filter(function(ae){return I.match(Z,ae)});if(J.length!==0){var ee=K.quill.getSelection();if(!(ee==null||!K.quill.hasFocus())){var ie=K.quill.getLine(ee.index),oe=m(ie,2),se=oe[0],ue=oe[1],V=K.quill.getLeaf(ee.index),$=m(V,2),Y=$[0],Q=$[1],G=ee.length===0?[Y,Q]:K.quill.getLeaf(ee.index+ee.length),te=m(G,2),ne=te[0],re=te[1],he=Y instanceof f.default.Text?Y.value().slice(0,Q):"",pe=ne instanceof f.default.Text?ne.value().slice(re):"",le={collapsed:ee.length===0,empty:ee.length===0&&se.length()<=1,format:K.quill.getFormat(ee),offset:ue,prefix:he,suffix:pe},Ze=J.some(function(ae){if(ae.collapsed!=null&&ae.collapsed!==le.collapsed||ae.empty!=null&&ae.empty!==le.empty||ae.offset!=null&&ae.offset!==le.offset)return!1;if(Array.isArray(ae.format)){if(ae.format.every(function(ge){return le.format[ge]==null}))return!1}else if(k(ae.format)==="object"&&!Object.keys(ae.format).every(function(ge){return ae.format[ge]===!0?le.format[ge]!=null:ae.format[ge]===!1?le.format[ge]==null:(0,o.default)(ae.format[ge],le.format[ge])}))return!1;return ae.prefix!=null&&!ae.prefix.test(le.prefix)||ae.suffix!=null&&!ae.suffix.test(le.suffix)?!1:ae.handler.call(K,ee,le)!==!0});Ze&&Z.preventDefault()}}}})}}]),I}(S.default);U.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},U.DEFAULTS={bindings:{bold:H("bold"),italic:H("italic"),underline:H("underline"),indent:{key:U.keys.TAB,format:["blockquote","indent","list"],handler:function(I,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","+1",h.default.sources.USER)}},outdent:{key:U.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(I,z){if(z.collapsed&&z.offset!==0)return!0;this.quill.format("indent","-1",h.default.sources.USER)}},"outdent backspace":{key:U.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(I,z){z.format.indent!=null?this.quill.format("indent","-1",h.default.sources.USER):z.format.list!=null&&this.quill.format("list",!1,h.default.sources.USER)}},"indent code-block":R(!0),"outdent code-block":R(!1),"remove tab":{key:U.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(I){this.quill.deleteText(I.index-1,1,h.default.sources.USER)}},tab:{key:U.keys.TAB,handler:function(I){this.quill.history.cutoff();var z=new l.default().retain(I.index).delete(I.length).insert("	");this.quill.updateContents(z,h.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(I.index+1,h.default.sources.SILENT)}},"list empty enter":{key:U.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(I,z){this.quill.format("list",!1,h.default.sources.USER),z.format.indent&&this.quill.format("indent",!1,h.default.sources.USER)}},"checklist enter":{key:U.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(I){var z=this.quill.getLine(I.index),K=m(z,2),Z=K[0],X=K[1],J=(0,t.default)({},Z.formats(),{list:"checked"}),ee=new l.default().retain(I.index).insert(`
`,J).retain(Z.length()-X-1).retain(1,{list:"unchecked"});this.quill.updateContents(ee,h.default.sources.USER),this.quill.setSelection(I.index+1,h.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:U.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(I,z){var K=this.quill.getLine(I.index),Z=m(K,2),X=Z[0],J=Z[1],ee=new l.default().retain(I.index).insert(`
`,z.format).retain(X.length()-J-1).retain(1,{header:null});this.quill.updateContents(ee,h.default.sources.USER),this.quill.setSelection(I.index+1,h.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(I,z){var K=z.prefix.length,Z=this.quill.getLine(I.index),X=m(Z,2),J=X[0],ee=X[1];if(ee>K)return!0;var ie=void 0;switch(z.prefix.trim()){case"[]":case"[ ]":ie="unchecked";break;case"[x]":ie="checked";break;case"-":case"*":ie="bullet";break;default:ie="ordered"}this.quill.insertText(I.index," ",h.default.sources.USER),this.quill.history.cutoff();var oe=new l.default().retain(I.index-ee).delete(K+1).retain(J.length()-2-ee).retain(1,{list:ie});this.quill.updateContents(oe,h.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(I.index-K,h.default.sources.SILENT)}},"code exit":{key:U.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(I){var z=this.quill.getLine(I.index),K=m(z,2),Z=K[0],X=K[1],J=new l.default().retain(I.index+Z.length()-X-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(J,h.default.sources.USER)}},"embed left":W(U.keys.LEFT,!1),"embed left shift":W(U.keys.LEFT,!0),"embed right":W(U.keys.RIGHT,!1),"embed right shift":W(U.keys.RIGHT,!0)}};function W(L,I){var z,K=L===U.keys.LEFT?"prefix":"suffix";return z={key:L,shiftKey:I,altKey:null},g(z,K,/^$/),g(z,"handler",function(X){var J=X.index;L===U.keys.RIGHT&&(J+=X.length+1);var ee=this.quill.getLeaf(J),ie=m(ee,1),oe=ie[0];return oe instanceof f.default.Embed?(L===U.keys.LEFT?I?this.quill.setSelection(X.index-1,X.length+1,h.default.sources.USER):this.quill.setSelection(X.index-1,h.default.sources.USER):I?this.quill.setSelection(X.index,X.length+1,h.default.sources.USER):this.quill.setSelection(X.index+X.length+1,h.default.sources.USER),!1):!0}),z}function B(L,I){if(!(L.index===0||this.quill.getLength()<=1)){var z=this.quill.getLine(L.index),K=m(z,1),Z=K[0],X={};if(I.offset===0){var J=this.quill.getLine(L.index-1),ee=m(J,1),ie=ee[0];if(ie!=null&&ie.length()>1){var oe=Z.formats(),se=this.quill.getFormat(L.index-1,1);X=r.default.attributes.diff(oe,se)||{}}}var ue=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(I.prefix)?2:1;this.quill.deleteText(L.index-ue,ue,h.default.sources.USER),Object.keys(X).length>0&&this.quill.formatLine(L.index-ue,ue,X,h.default.sources.USER),this.quill.focus()}}function M(L,I){var z=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(I.suffix)?2:1;if(!(L.index>=this.quill.getLength()-z)){var K={},Z=0,X=this.quill.getLine(L.index),J=m(X,1),ee=J[0];if(I.offset>=ee.length()-1){var ie=this.quill.getLine(L.index+1),oe=m(ie,1),se=oe[0];if(se){var ue=ee.formats(),V=this.quill.getFormat(L.index,1);K=r.default.attributes.diff(ue,V)||{},Z=se.length()}}this.quill.deleteText(L.index,z,h.default.sources.USER),Object.keys(K).length>0&&this.quill.formatLine(L.index+Z-1,z,K,h.default.sources.USER)}}function N(L){var I=this.quill.getLines(L),z={};if(I.length>1){var K=I[0].formats(),Z=I[I.length-1].formats();z=r.default.attributes.diff(Z,K)||{}}this.quill.deleteText(L,h.default.sources.USER),Object.keys(z).length>0&&this.quill.formatLine(L.index,1,z,h.default.sources.USER),this.quill.setSelection(L.index,h.default.sources.SILENT),this.quill.focus()}function q(L,I){var z=this;L.length>0&&this.quill.scroll.deleteAt(L.index,L.length);var K=Object.keys(I.format).reduce(function(Z,X){return f.default.query(X,f.default.Scope.BLOCK)&&!Array.isArray(I.format[X])&&(Z[X]=I.format[X]),Z},{});this.quill.insertText(L.index,`
`,K,h.default.sources.USER),this.quill.setSelection(L.index+1,h.default.sources.SILENT),this.quill.focus(),Object.keys(I.format).forEach(function(Z){K[Z]==null&&(Array.isArray(I.format[Z])||Z!=="link"&&z.quill.format(Z,I.format[Z],h.default.sources.USER))})}function R(L){return{key:U.keys.TAB,shiftKey:!L,format:{"code-block":!0},handler:function(z){var K=f.default.query("code-block"),Z=z.index,X=z.length,J=this.quill.scroll.descendant(K,Z),ee=m(J,2),ie=ee[0],oe=ee[1];if(ie!=null){var se=this.quill.getIndex(ie),ue=ie.newlineIndex(oe,!0)+1,V=ie.newlineIndex(se+oe+X),$=ie.domNode.textContent.slice(ue,V).split(`
`);oe=0,$.forEach(function(Y,Q){L?(ie.insertAt(ue+oe,K.TAB),oe+=K.TAB.length,Q===0?Z+=K.TAB.length:X+=K.TAB.length):Y.startsWith(K.TAB)&&(ie.deleteAt(ue+oe,K.TAB.length),oe-=K.TAB.length,Q===0?Z-=K.TAB.length:X-=K.TAB.length),oe+=Y.length+1}),this.quill.update(h.default.sources.USER),this.quill.setSelection(Z,X,h.default.sources.SILENT)}}}}function H(L){return{key:L[0].toUpperCase(),shortKey:!0,handler:function(z,K){this.quill.format(L,!K.format[L],h.default.sources.USER)}}}function C(L){if(typeof L=="string"||typeof L=="number")return C({key:L});if((typeof L=="undefined"?"undefined":k(L))==="object"&&(L=(0,p.default)(L,!1)),typeof L.key=="string")if(U.keys[L.key.toUpperCase()]!=null)L.key=U.keys[L.key.toUpperCase()];else if(L.key.length===1)L.key=L.key.toUpperCase().charCodeAt(0);else return null;return L.shortKey&&(L[F]=L.shortKey,delete L.shortKey),L}d.default=U,d.SHORTKEY=F},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function r(i,f){var n=[],h=!0,_=!1,O=void 0;try{for(var w=i[Symbol.iterator](),S;!(h=(S=w.next()).done)&&(n.push(S.value),!(f&&n.length===f));h=!0);}catch(E){_=!0,O=E}finally{try{!h&&w.return&&w.return()}finally{if(_)throw O}}return n}return function(i,f){if(Array.isArray(i))return i;if(Symbol.iterator in Object(i))return r(i,f);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function r(i,f,n){i===null&&(i=Function.prototype);var h=Object.getOwnPropertyDescriptor(i,f);if(h===void 0){var _=Object.getPrototypeOf(i);return _===null?void 0:r(_,f,n)}else{if("value"in h)return h.value;var O=h.get;return O===void 0?void 0:O.call(n)}},b=function(){function r(i,f){for(var n=0;n<f.length;n++){var h=f[n];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(i,h.key,h)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),v=c(0),p=e(v),s=c(7),o=e(s);function e(r){return r&&r.__esModule?r:{default:r}}function t(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}function u(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:r}function l(r,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}var a=function(r){l(i,r),b(i,null,[{key:"value",value:function(){}}]);function i(f,n){t(this,i);var h=u(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,f));return h.selection=n,h.textNode=document.createTextNode(i.CONTENTS),h.domNode.appendChild(h.textNode),h._length=0,h}return b(i,[{key:"detach",value:function(){this.parent!=null&&this.parent.removeChild(this)}},{key:"format",value:function(n,h){if(this._length!==0)return m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"format",this).call(this,n,h);for(var _=this,O=0;_!=null&&_.statics.scope!==p.default.Scope.BLOCK_BLOT;)O+=_.offset(_.parent),_=_.parent;_!=null&&(this._length=i.CONTENTS.length,_.optimize(),_.formatAt(O,i.CONTENTS.length,n,h),this._length=0)}},{key:"index",value:function(n,h){return n===this.textNode?0:m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"index",this).call(this,n,h)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!(this.selection.composing||this.parent==null)){var n=this.textNode,h=this.selection.getNativeRange(),_=void 0,O=void 0,w=void 0;if(h!=null&&h.start.node===n&&h.end.node===n){var S=[n,h.start.offset,h.end.offset];_=S[0],O=S[1],w=S[2]}for(;this.domNode.lastChild!=null&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==i.CONTENTS){var E=this.textNode.data.split(i.CONTENTS).join("");this.next instanceof o.default?(_=this.next.domNode,this.next.insertAt(0,E),this.textNode.data=i.CONTENTS):(this.textNode.data=E,this.parent.insertBefore(p.default.create(this.textNode),this),this.textNode=document.createTextNode(i.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),O!=null){var g=[O,w].map(function(T){return Math.max(0,Math.min(_.data.length,T-1))}),A=k(g,2);return O=A[0],w=A[1],{startNode:_,startOffset:O,endNode:_,endOffset:w}}}}},{key:"update",value:function(n,h){var _=this;if(n.some(function(w){return w.type==="characterData"&&w.target===_.textNode})){var O=this.restore();O&&(h.range=O)}}},{key:"value",value:function(){return""}}]),i}(p.default.Embed);a.blotName="cursor",a.className="ql-cursor",a.tagName="span",a.CONTENTS="\uFEFF",d.default=a},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(0),m=p(k),b=c(4),v=p(b);function p(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return l}(m.default.Container);t.allowedChildren=[v.default,b.BlockEmbed,t],d.default=t},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.ColorStyle=d.ColorClass=d.ColorAttributor=void 0;var k=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var _=n.get;return _===void 0?void 0:_.call(f)}},b=c(0),v=p(b);function p(a){return a&&a.__esModule?a:{default:a}}function s(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function o(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function e(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var t=function(a){e(r,a);function r(){return s(this,r),o(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return k(r,[{key:"value",value:function(f){var n=m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"value",this).call(this,f);return n.startsWith("rgb(")?(n=n.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+n.split(",").map(function(h){return("00"+parseInt(h).toString(16)).slice(-2)}).join("")):n}}]),r}(v.default.Attributor.Style),u=new v.default.Attributor.Class("color","ql-color",{scope:v.default.Scope.INLINE}),l=new t("color","color",{scope:v.default.Scope.INLINE});d.ColorAttributor=t,d.ColorClass=u,d.ColorStyle=l},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.sanitize=d.default=void 0;var k=function(){function l(a,r){for(var i=0;i<r.length;i++){var f=r[i];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,r,i){return r&&l(a.prototype,r),i&&l(a,i),a}}(),m=function l(a,r,i){a===null&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,r);if(f===void 0){var n=Object.getPrototypeOf(a);return n===null?void 0:l(n,r,i)}else{if("value"in f)return f.value;var h=f.get;return h===void 0?void 0:h.call(i)}},b=c(6),v=p(b);function p(l){return l&&l.__esModule?l:{default:l}}function s(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}function o(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:l}function e(l,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}var t=function(l){e(a,l);function a(){return s(this,a),o(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return k(a,[{key:"format",value:function(i,f){if(i!==this.statics.blotName||!f)return m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"format",this).call(this,i,f);f=this.constructor.sanitize(f),this.domNode.setAttribute("href",f)}}],[{key:"create",value:function(i){var f=m(a.__proto__||Object.getPrototypeOf(a),"create",this).call(this,i);return i=this.sanitize(i),f.setAttribute("href",i),f.setAttribute("rel","noopener noreferrer"),f.setAttribute("target","_blank"),f}},{key:"formats",value:function(i){return i.getAttribute("href")}},{key:"sanitize",value:function(i){return u(i,this.PROTOCOL_WHITELIST)?i:this.SANITIZED_URL}}]),a}(v.default);t.blotName="link",t.tagName="A",t.SANITIZED_URL="about:blank",t.PROTOCOL_WHITELIST=["http","https","mailto","tel"];function u(l,a){var r=document.createElement("a");r.href=l;var i=r.href.slice(0,r.href.indexOf(":"));return a.indexOf(i)>-1}d.default=t,d.sanitize=u},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol=="function"&&a.constructor===Symbol&&a!==Symbol.prototype?"symbol":typeof a},m=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),b=c(23),v=o(b),p=c(107),s=o(p);function o(a){return a&&a.__esModule?a:{default:a}}function e(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}var t=0;function u(a,r){a.setAttribute(r,a.getAttribute(r)!=="true")}var l=function(){function a(r){var i=this;e(this,a),this.select=r,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",function(){i.togglePicker()}),this.label.addEventListener("keydown",function(f){switch(f.keyCode){case v.default.keys.ENTER:i.togglePicker();break;case v.default.keys.ESCAPE:i.escape(),f.preventDefault();break}}),this.select.addEventListener("change",this.update.bind(this))}return m(a,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),u(this.label,"aria-expanded"),u(this.options,"aria-hidden")}},{key:"buildItem",value:function(i){var f=this,n=document.createElement("span");return n.tabIndex="0",n.setAttribute("role","button"),n.classList.add("ql-picker-item"),i.hasAttribute("value")&&n.setAttribute("data-value",i.getAttribute("value")),i.textContent&&n.setAttribute("data-label",i.textContent),n.addEventListener("click",function(){f.selectItem(n,!0)}),n.addEventListener("keydown",function(h){switch(h.keyCode){case v.default.keys.ENTER:f.selectItem(n,!0),h.preventDefault();break;case v.default.keys.ESCAPE:f.escape(),h.preventDefault();break}}),n}},{key:"buildLabel",value:function(){var i=document.createElement("span");return i.classList.add("ql-picker-label"),i.innerHTML=s.default,i.tabIndex="0",i.setAttribute("role","button"),i.setAttribute("aria-expanded","false"),this.container.appendChild(i),i}},{key:"buildOptions",value:function(){var i=this,f=document.createElement("span");f.classList.add("ql-picker-options"),f.setAttribute("aria-hidden","true"),f.tabIndex="-1",f.id="ql-picker-options-"+t,t+=1,this.label.setAttribute("aria-controls",f.id),this.options=f,[].slice.call(this.select.options).forEach(function(n){var h=i.buildItem(n);f.appendChild(h),n.selected===!0&&i.selectItem(h)}),this.container.appendChild(f)}},{key:"buildPicker",value:function(){var i=this;[].slice.call(this.select.attributes).forEach(function(f){i.container.setAttribute(f.name,f.value)}),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var i=this;this.close(),setTimeout(function(){return i.label.focus()},1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(i){var f=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,n=this.container.querySelector(".ql-selected");if(i!==n&&(n!=null&&n.classList.remove("ql-selected"),i!=null&&(i.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(i.parentNode.children,i),i.hasAttribute("data-value")?this.label.setAttribute("data-value",i.getAttribute("data-value")):this.label.removeAttribute("data-value"),i.hasAttribute("data-label")?this.label.setAttribute("data-label",i.getAttribute("data-label")):this.label.removeAttribute("data-label"),f))){if(typeof Event=="function")this.select.dispatchEvent(new Event("change"));else if((typeof Event=="undefined"?"undefined":k(Event))==="object"){var h=document.createEvent("Event");h.initEvent("change",!0,!0),this.select.dispatchEvent(h)}this.close()}}},{key:"update",value:function(){var i=void 0;if(this.select.selectedIndex>-1){var f=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];i=this.select.options[this.select.selectedIndex],this.selectItem(f)}else this.selectItem(null);var n=i!=null&&i!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",n)}}]),a}();d.default=l},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(0),m=j(k),b=c(5),v=j(b),p=c(4),s=j(p),o=c(16),e=j(o),t=c(25),u=j(t),l=c(24),a=j(l),r=c(35),i=j(r),f=c(6),n=j(f),h=c(22),_=j(h),O=c(7),w=j(O),S=c(55),E=j(S),g=c(42),A=j(g),T=c(23),x=j(T);function j(F){return F&&F.__esModule?F:{default:F}}v.default.register({"blots/block":s.default,"blots/block/embed":p.BlockEmbed,"blots/break":e.default,"blots/container":u.default,"blots/cursor":a.default,"blots/embed":i.default,"blots/inline":n.default,"blots/scroll":_.default,"blots/text":w.default,"modules/clipboard":E.default,"modules/history":A.default,"modules/keyboard":x.default}),m.default.register(s.default,e.default,a.default,n.default,_.default,w.default),d.default=v.default},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(1),m=function(){function b(v){this.domNode=v,this.domNode[k.DATA_KEY]={blot:this}}return Object.defineProperty(b.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),b.create=function(v){if(this.tagName==null)throw new k.ParchmentError("Blot definition missing tagName");var p;return Array.isArray(this.tagName)?(typeof v=="string"&&(v=v.toUpperCase(),parseInt(v).toString()===v&&(v=parseInt(v))),typeof v=="number"?p=document.createElement(this.tagName[v-1]):this.tagName.indexOf(v)>-1?p=document.createElement(v):p=document.createElement(this.tagName[0])):p=document.createElement(this.tagName),this.className&&p.classList.add(this.className),p},b.prototype.attach=function(){this.parent!=null&&(this.scroll=this.parent.scroll)},b.prototype.clone=function(){var v=this.domNode.cloneNode(!1);return k.create(v)},b.prototype.detach=function(){this.parent!=null&&this.parent.removeChild(this),delete this.domNode[k.DATA_KEY]},b.prototype.deleteAt=function(v,p){var s=this.isolate(v,p);s.remove()},b.prototype.formatAt=function(v,p,s,o){var e=this.isolate(v,p);if(k.query(s,k.Scope.BLOT)!=null&&o)e.wrap(s,o);else if(k.query(s,k.Scope.ATTRIBUTE)!=null){var t=k.create(this.statics.scope);e.wrap(t),t.format(s,o)}},b.prototype.insertAt=function(v,p,s){var o=s==null?k.create("text",p):k.create(p,s),e=this.split(v);this.parent.insertBefore(o,e)},b.prototype.insertInto=function(v,p){p===void 0&&(p=null),this.parent!=null&&this.parent.children.remove(this);var s=null;v.children.insertBefore(this,p),p!=null&&(s=p.domNode),(this.domNode.parentNode!=v.domNode||this.domNode.nextSibling!=s)&&v.domNode.insertBefore(this.domNode,s),this.parent=v,this.attach()},b.prototype.isolate=function(v,p){var s=this.split(v);return s.split(p),s},b.prototype.length=function(){return 1},b.prototype.offset=function(v){return v===void 0&&(v=this.parent),this.parent==null||this==v?0:this.parent.children.offset(this)+this.parent.offset(v)},b.prototype.optimize=function(v){this.domNode[k.DATA_KEY]!=null&&delete this.domNode[k.DATA_KEY].mutations},b.prototype.remove=function(){this.domNode.parentNode!=null&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},b.prototype.replace=function(v){v.parent!=null&&(v.parent.insertBefore(this,v.next),v.remove())},b.prototype.replaceWith=function(v,p){var s=typeof v=="string"?k.create(v,p):v;return s.replace(this),s},b.prototype.split=function(v,p){return v===0?this:this.next},b.prototype.update=function(v,p){},b.prototype.wrap=function(v,p){var s=typeof v=="string"?k.create(v,p):v;return this.parent!=null&&this.parent.insertBefore(s,this.next),s.appendChild(this),s},b.blotName="abstract",b}();d.default=m},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(12),m=c(32),b=c(33),v=c(1),p=function(){function s(o){this.attributes={},this.domNode=o,this.build()}return s.prototype.attribute=function(o,e){e?o.add(this.domNode,e)&&(o.value(this.domNode)!=null?this.attributes[o.attrName]=o:delete this.attributes[o.attrName]):(o.remove(this.domNode),delete this.attributes[o.attrName])},s.prototype.build=function(){var o=this;this.attributes={};var e=k.default.keys(this.domNode),t=m.default.keys(this.domNode),u=b.default.keys(this.domNode);e.concat(t).concat(u).forEach(function(l){var a=v.query(l,v.Scope.ATTRIBUTE);a instanceof k.default&&(o.attributes[a.attrName]=a)})},s.prototype.copy=function(o){var e=this;Object.keys(this.attributes).forEach(function(t){var u=e.attributes[t].value(e.domNode);o.format(t,u)})},s.prototype.move=function(o){var e=this;this.copy(o),Object.keys(this.attributes).forEach(function(t){e.attributes[t].remove(e.domNode)}),this.attributes={}},s.prototype.values=function(){var o=this;return Object.keys(this.attributes).reduce(function(e,t){return e[t]=o.attributes[t].value(o.domNode),e},{})},s}();d.default=p},function(y,d,c){var k=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){p(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(12);function b(p,s){var o=p.getAttribute("class")||"";return o.split(/\s+/).filter(function(e){return e.indexOf(s+"-")===0})}var v=function(p){k(s,p);function s(){return p!==null&&p.apply(this,arguments)||this}return s.keys=function(o){return(o.getAttribute("class")||"").split(/\s+/).map(function(e){return e.split("-").slice(0,-1).join("-")})},s.prototype.add=function(o,e){return this.canAdd(o,e)?(this.remove(o),o.classList.add(this.keyName+"-"+e),!0):!1},s.prototype.remove=function(o){var e=b(o,this.keyName);e.forEach(function(t){o.classList.remove(t)}),o.classList.length===0&&o.removeAttribute("class")},s.prototype.value=function(o){var e=b(o,this.keyName)[0]||"",t=e.slice(this.keyName.length+1);return this.canAdd(o,t)?t:""},s}(m.default);d.default=v},function(y,d,c){var k=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){p(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(12);function b(p){var s=p.split("-"),o=s.slice(1).map(function(e){return e[0].toUpperCase()+e.slice(1)}).join("");return s[0]+o}var v=function(p){k(s,p);function s(){return p!==null&&p.apply(this,arguments)||this}return s.keys=function(o){return(o.getAttribute("style")||"").split(";").map(function(e){var t=e.split(":");return t[0].trim()})},s.prototype.add=function(o,e){return this.canAdd(o,e)?(o.style[b(this.keyName)]=e,!0):!1},s.prototype.remove=function(o){o.style[b(this.keyName)]="",o.getAttribute("style")||o.removeAttribute("style")},s.prototype.value=function(o){var e=o.style[b(this.keyName)];return this.canAdd(o,e)?e:""},s}(m.default);d.default=v},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function v(p,s){for(var o=0;o<s.length;o++){var e=s[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(p,e.key,e)}}return function(p,s,o){return s&&v(p.prototype,s),o&&v(p,o),p}}();function m(v,p){if(!(v instanceof p))throw new TypeError("Cannot call a class as a function")}var b=function(){function v(p,s){m(this,v),this.quill=p,this.options=s,this.modules={}}return k(v,[{key:"init",value:function(){var s=this;Object.keys(this.options.modules).forEach(function(o){s.modules[o]==null&&s.addModule(o)})}},{key:"addModule",value:function(s){var o=this.quill.constructor.import("modules/"+s);return this.modules[s]=new o(this.quill,this.options.modules[s]||{}),this.modules[s]}}]),v}();b.DEFAULTS={modules:{}},b.themes={default:b},d.default=b},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function r(i,f){for(var n=0;n<f.length;n++){var h=f[n];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(i,h.key,h)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),m=function r(i,f,n){i===null&&(i=Function.prototype);var h=Object.getOwnPropertyDescriptor(i,f);if(h===void 0){var _=Object.getPrototypeOf(i);return _===null?void 0:r(_,f,n)}else{if("value"in h)return h.value;var O=h.get;return O===void 0?void 0:O.call(n)}},b=c(0),v=o(b),p=c(7),s=o(p);function o(r){return r&&r.__esModule?r:{default:r}}function e(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}function t(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:r}function u(r,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}var l="\uFEFF",a=function(r){u(i,r);function i(f){e(this,i);var n=t(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,f));return n.contentNode=document.createElement("span"),n.contentNode.setAttribute("contenteditable",!1),[].slice.call(n.domNode.childNodes).forEach(function(h){n.contentNode.appendChild(h)}),n.leftGuard=document.createTextNode(l),n.rightGuard=document.createTextNode(l),n.domNode.appendChild(n.leftGuard),n.domNode.appendChild(n.contentNode),n.domNode.appendChild(n.rightGuard),n}return k(i,[{key:"index",value:function(n,h){return n===this.leftGuard?0:n===this.rightGuard?1:m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"index",this).call(this,n,h)}},{key:"restore",value:function(n){var h=void 0,_=void 0,O=n.data.split(l).join("");if(n===this.leftGuard)if(this.prev instanceof s.default){var w=this.prev.length();this.prev.insertAt(w,O),h={startNode:this.prev.domNode,startOffset:w+O.length}}else _=document.createTextNode(O),this.parent.insertBefore(v.default.create(_),this),h={startNode:_,startOffset:O.length};else n===this.rightGuard&&(this.next instanceof s.default?(this.next.insertAt(0,O),h={startNode:this.next.domNode,startOffset:O.length}):(_=document.createTextNode(O),this.parent.insertBefore(v.default.create(_),this.next),h={startNode:_,startOffset:O.length}));return n.data=l,h}},{key:"update",value:function(n,h){var _=this;n.forEach(function(O){if(O.type==="characterData"&&(O.target===_.leftGuard||O.target===_.rightGuard)){var w=_.restore(O.target);w&&(h.range=w)}})}}]),i}(v.default.Embed);d.default=a},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.AlignStyle=d.AlignClass=d.AlignAttribute=void 0;var k=c(0),m=b(k);function b(e){return e&&e.__esModule?e:{default:e}}var v={scope:m.default.Scope.BLOCK,whitelist:["right","center","justify"]},p=new m.default.Attributor.Attribute("align","align",v),s=new m.default.Attributor.Class("align","ql-align",v),o=new m.default.Attributor.Style("align","text-align",v);d.AlignAttribute=p,d.AlignClass=s,d.AlignStyle=o},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.BackgroundStyle=d.BackgroundClass=void 0;var k=c(0),m=v(k),b=c(26);function v(o){return o&&o.__esModule?o:{default:o}}var p=new m.default.Attributor.Class("background","ql-bg",{scope:m.default.Scope.INLINE}),s=new b.ColorAttributor("background","background-color",{scope:m.default.Scope.INLINE});d.BackgroundClass=p,d.BackgroundStyle=s},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.DirectionStyle=d.DirectionClass=d.DirectionAttribute=void 0;var k=c(0),m=b(k);function b(e){return e&&e.__esModule?e:{default:e}}var v={scope:m.default.Scope.BLOCK,whitelist:["rtl"]},p=new m.default.Attributor.Attribute("direction","dir",v),s=new m.default.Attributor.Class("direction","ql-direction",v),o=new m.default.Attributor.Style("direction","direction",v);d.DirectionAttribute=p,d.DirectionClass=s,d.DirectionStyle=o},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.FontClass=d.FontStyle=void 0;var k=function(){function r(i,f){for(var n=0;n<f.length;n++){var h=f[n];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(i,h.key,h)}}return function(i,f,n){return f&&r(i.prototype,f),n&&r(i,n),i}}(),m=function r(i,f,n){i===null&&(i=Function.prototype);var h=Object.getOwnPropertyDescriptor(i,f);if(h===void 0){var _=Object.getPrototypeOf(i);return _===null?void 0:r(_,f,n)}else{if("value"in h)return h.value;var O=h.get;return O===void 0?void 0:O.call(n)}},b=c(0),v=p(b);function p(r){return r&&r.__esModule?r:{default:r}}function s(r,i){if(!(r instanceof i))throw new TypeError("Cannot call a class as a function")}function o(r,i){if(!r)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return i&&(typeof i=="object"||typeof i=="function")?i:r}function e(r,i){if(typeof i!="function"&&i!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof i);r.prototype=Object.create(i&&i.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),i&&(Object.setPrototypeOf?Object.setPrototypeOf(r,i):r.__proto__=i)}var t={scope:v.default.Scope.INLINE,whitelist:["serif","monospace"]},u=new v.default.Attributor.Class("font","ql-font",t),l=function(r){e(i,r);function i(){return s(this,i),o(this,(i.__proto__||Object.getPrototypeOf(i)).apply(this,arguments))}return k(i,[{key:"value",value:function(n){return m(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"value",this).call(this,n).replace(/["']/g,"")}}]),i}(v.default.Attributor.Style),a=new l("font","font-family",t);d.FontStyle=a,d.FontClass=u},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.SizeStyle=d.SizeClass=void 0;var k=c(0),m=b(k);function b(s){return s&&s.__esModule?s:{default:s}}var v=new m.default.Attributor.Class("size","ql-size",{scope:m.default.Scope.INLINE,whitelist:["small","large","huge"]}),p=new m.default.Attributor.Style("size","font-size",{scope:m.default.Scope.INLINE,whitelist:["10px","18px","32px"]});d.SizeClass=v,d.SizeStyle=p},function(y,d,c){y.exports={align:{"":c(76),center:c(77),right:c(78),justify:c(79)},background:c(80),blockquote:c(81),bold:c(82),clean:c(83),code:c(58),"code-block":c(58),color:c(84),direction:{"":c(85),rtl:c(86)},float:{center:c(87),full:c(88),left:c(89),right:c(90)},formula:c(91),header:{1:c(92),2:c(93)},italic:c(94),image:c(95),indent:{"+1":c(96),"-1":c(97)},link:c(98),list:{ordered:c(99),bullet:c(100),check:c(101)},script:{sub:c(102),super:c(103)},strike:c(104),underline:c(105),video:c(106)}},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.getLastChangeIndex=d.default=void 0;var k=function(){function f(n,h){for(var _=0;_<h.length;_++){var O=h[_];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(n,O.key,O)}}return function(n,h,_){return h&&f(n.prototype,h),_&&f(n,_),n}}(),m=c(0),b=e(m),v=c(5),p=e(v),s=c(9),o=e(s);function e(f){return f&&f.__esModule?f:{default:f}}function t(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}function u(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:f}function l(f,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}var a=function(f){l(n,f);function n(h,_){t(this,n);var O=u(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,h,_));return O.lastRecorded=0,O.ignoreChange=!1,O.clear(),O.quill.on(p.default.events.EDITOR_CHANGE,function(w,S,E,g){w!==p.default.events.TEXT_CHANGE||O.ignoreChange||(!O.options.userOnly||g===p.default.sources.USER?O.record(S,E):O.transform(S))}),O.quill.keyboard.addBinding({key:"Z",shortKey:!0},O.undo.bind(O)),O.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},O.redo.bind(O)),/Win/i.test(navigator.platform)&&O.quill.keyboard.addBinding({key:"Y",shortKey:!0},O.redo.bind(O)),O}return k(n,[{key:"change",value:function(_,O){if(this.stack[_].length!==0){var w=this.stack[_].pop();this.stack[O].push(w),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(w[_],p.default.sources.USER),this.ignoreChange=!1;var S=i(w[_]);this.quill.setSelection(S)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(_,O){if(_.ops.length!==0){this.stack.redo=[];var w=this.quill.getContents().diff(O),S=Date.now();if(this.lastRecorded+this.options.delay>S&&this.stack.undo.length>0){var E=this.stack.undo.pop();w=w.compose(E.undo),_=E.redo.compose(_)}else this.lastRecorded=S;this.stack.undo.push({redo:_,undo:w}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(_){this.stack.undo.forEach(function(O){O.undo=_.transform(O.undo,!0),O.redo=_.transform(O.redo,!0)}),this.stack.redo.forEach(function(O){O.undo=_.transform(O.undo,!0),O.redo=_.transform(O.redo,!0)})}},{key:"undo",value:function(){this.change("undo","redo")}}]),n}(o.default);a.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1};function r(f){var n=f.ops[f.ops.length-1];return n==null?!1:n.insert!=null?typeof n.insert=="string"&&n.insert.endsWith(`
`):n.attributes!=null?Object.keys(n.attributes).some(function(h){return b.default.query(h,b.default.Scope.BLOCK)!=null}):!1}function i(f){var n=f.reduce(function(_,O){return _+=O.delete||0,_},0),h=f.length()-n;return r(f)&&(h-=1),h}d.default=a,d.getLastChangeIndex=i},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.BaseTooltip=void 0;var k=function(){function q(R,H){for(var C=0;C<H.length;C++){var L=H[C];L.enumerable=L.enumerable||!1,L.configurable=!0,"value"in L&&(L.writable=!0),Object.defineProperty(R,L.key,L)}}return function(R,H,C){return H&&q(R.prototype,H),C&&q(R,C),R}}(),m=function q(R,H,C){R===null&&(R=Function.prototype);var L=Object.getOwnPropertyDescriptor(R,H);if(L===void 0){var I=Object.getPrototypeOf(R);return I===null?void 0:q(I,H,C)}else{if("value"in L)return L.value;var z=L.get;return z===void 0?void 0:z.call(C)}},b=c(3),v=S(b),p=c(2),s=S(p),o=c(8),e=S(o),t=c(23),u=S(t),l=c(34),a=S(l),r=c(59),i=S(r),f=c(60),n=S(f),h=c(28),_=S(h),O=c(61),w=S(O);function S(q){return q&&q.__esModule?q:{default:q}}function E(q,R){if(!(q instanceof R))throw new TypeError("Cannot call a class as a function")}function g(q,R){if(!q)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return R&&(typeof R=="object"||typeof R=="function")?R:q}function A(q,R){if(typeof R!="function"&&R!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof R);q.prototype=Object.create(R&&R.prototype,{constructor:{value:q,enumerable:!1,writable:!0,configurable:!0}}),R&&(Object.setPrototypeOf?Object.setPrototypeOf(q,R):q.__proto__=R)}var T=[!1,"center","right","justify"],x=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],j=[!1,"serif","monospace"],F=["1","2","3",!1],U=["small",!1,"large","huge"],W=function(q){A(R,q);function R(H,C){E(this,R);var L=g(this,(R.__proto__||Object.getPrototypeOf(R)).call(this,H,C)),I=function z(K){if(!document.body.contains(H.root))return document.body.removeEventListener("click",z);L.tooltip!=null&&!L.tooltip.root.contains(K.target)&&document.activeElement!==L.tooltip.textbox&&!L.quill.hasFocus()&&L.tooltip.hide(),L.pickers!=null&&L.pickers.forEach(function(Z){Z.container.contains(K.target)||Z.close()})};return H.emitter.listenDOM("click",document.body,I),L}return k(R,[{key:"addModule",value:function(C){var L=m(R.prototype.__proto__||Object.getPrototypeOf(R.prototype),"addModule",this).call(this,C);return C==="toolbar"&&this.extendToolbar(L),L}},{key:"buildButtons",value:function(C,L){C.forEach(function(I){var z=I.getAttribute("class")||"";z.split(/\s+/).forEach(function(K){if(!!K.startsWith("ql-")&&(K=K.slice(3),L[K]!=null))if(K==="direction")I.innerHTML=L[K][""]+L[K].rtl;else if(typeof L[K]=="string")I.innerHTML=L[K];else{var Z=I.value||"";Z!=null&&L[K][Z]&&(I.innerHTML=L[K][Z])}})})}},{key:"buildPickers",value:function(C,L){var I=this;this.pickers=C.map(function(K){if(K.classList.contains("ql-align"))return K.querySelector("option")==null&&N(K,T),new n.default(K,L.align);if(K.classList.contains("ql-background")||K.classList.contains("ql-color")){var Z=K.classList.contains("ql-background")?"background":"color";return K.querySelector("option")==null&&N(K,x,Z==="background"?"#ffffff":"#000000"),new i.default(K,L[Z])}else return K.querySelector("option")==null&&(K.classList.contains("ql-font")?N(K,j):K.classList.contains("ql-header")?N(K,F):K.classList.contains("ql-size")&&N(K,U)),new _.default(K)});var z=function(){I.pickers.forEach(function(Z){Z.update()})};this.quill.on(e.default.events.EDITOR_CHANGE,z)}}]),R}(a.default);W.DEFAULTS=(0,v.default)(!0,{},a.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var R=this,H=this.container.querySelector("input.ql-image[type=file]");H==null&&(H=document.createElement("input"),H.setAttribute("type","file"),H.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),H.classList.add("ql-image"),H.addEventListener("change",function(){if(H.files!=null&&H.files[0]!=null){var C=new FileReader;C.onload=function(L){var I=R.quill.getSelection(!0);R.quill.updateContents(new s.default().retain(I.index).delete(I.length).insert({image:L.target.result}),e.default.sources.USER),R.quill.setSelection(I.index+1,e.default.sources.SILENT),H.value=""},C.readAsDataURL(H.files[0])}}),this.container.appendChild(H)),H.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var B=function(q){A(R,q);function R(H,C){E(this,R);var L=g(this,(R.__proto__||Object.getPrototypeOf(R)).call(this,H,C));return L.textbox=L.root.querySelector('input[type="text"]'),L.listen(),L}return k(R,[{key:"listen",value:function(){var C=this;this.textbox.addEventListener("keydown",function(L){u.default.match(L,"enter")?(C.save(),L.preventDefault()):u.default.match(L,"escape")&&(C.cancel(),L.preventDefault())})}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var C=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"link",L=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),L!=null?this.textbox.value=L:C!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+C)||""),this.root.setAttribute("data-mode",C)}},{key:"restoreFocus",value:function(){var C=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=C}},{key:"save",value:function(){var C=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":{var L=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",C,e.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",C,e.default.sources.USER)),this.quill.root.scrollTop=L;break}case"video":C=M(C);case"formula":{if(!C)break;var I=this.quill.getSelection(!0);if(I!=null){var z=I.index+I.length;this.quill.insertEmbed(z,this.root.getAttribute("data-mode"),C,e.default.sources.USER),this.root.getAttribute("data-mode")==="formula"&&this.quill.insertText(z+1," ",e.default.sources.USER),this.quill.setSelection(z+2,e.default.sources.USER)}break}}this.textbox.value="",this.hide()}}]),R}(w.default);function M(q){var R=q.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||q.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return R?(R[1]||"https")+"://www.youtube.com/embed/"+R[2]+"?showinfo=0":(R=q.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(R[1]||"https")+"://player.vimeo.com/video/"+R[2]+"/":q}function N(q,R){var H=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;R.forEach(function(C){var L=document.createElement("option");C===H?L.setAttribute("selected","selected"):L.setAttribute("value",C),q.appendChild(L)})}d.BaseTooltip=B,d.default=W},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function m(){this.head=this.tail=null,this.length=0}return m.prototype.append=function(){for(var b=[],v=0;v<arguments.length;v++)b[v]=arguments[v];this.insertBefore(b[0],null),b.length>1&&this.append.apply(this,b.slice(1))},m.prototype.contains=function(b){for(var v,p=this.iterator();v=p();)if(v===b)return!0;return!1},m.prototype.insertBefore=function(b,v){!b||(b.next=v,v!=null?(b.prev=v.prev,v.prev!=null&&(v.prev.next=b),v.prev=b,v===this.head&&(this.head=b)):this.tail!=null?(this.tail.next=b,b.prev=this.tail,this.tail=b):(b.prev=null,this.head=this.tail=b),this.length+=1)},m.prototype.offset=function(b){for(var v=0,p=this.head;p!=null;){if(p===b)return v;v+=p.length(),p=p.next}return-1},m.prototype.remove=function(b){!this.contains(b)||(b.prev!=null&&(b.prev.next=b.next),b.next!=null&&(b.next.prev=b.prev),b===this.head&&(this.head=b.next),b===this.tail&&(this.tail=b.prev),this.length-=1)},m.prototype.iterator=function(b){return b===void 0&&(b=this.head),function(){var v=b;return b!=null&&(b=b.next),v}},m.prototype.find=function(b,v){v===void 0&&(v=!1);for(var p,s=this.iterator();p=s();){var o=p.length();if(b<o||v&&b===o&&(p.next==null||p.next.length()!==0))return[p,b];b-=o}return[null,0]},m.prototype.forEach=function(b){for(var v,p=this.iterator();v=p();)b(v)},m.prototype.forEachAt=function(b,v,p){if(!(v<=0))for(var s=this.find(b),o=s[0],e=s[1],t,u=b-e,l=this.iterator(o);(t=l())&&u<b+v;){var a=t.length();b>u?p(t,b-u,Math.min(v,u+a-b)):p(t,0,Math.min(a,b+v-u)),u+=a}},m.prototype.map=function(b){return this.reduce(function(v,p){return v.push(b(p)),v},[])},m.prototype.reduce=function(b,v){for(var p,s=this.iterator();p=s();)v=b(v,p);return v},m}();d.default=k},function(y,d,c){var k=this&&this.__extends||function(){var o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var u in t)t.hasOwnProperty(u)&&(e[u]=t[u])};return function(e,t){o(e,t);function u(){this.constructor=e}e.prototype=t===null?Object.create(t):(u.prototype=t.prototype,new u)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(17),b=c(1),v={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},p=100,s=function(o){k(e,o);function e(t){var u=o.call(this,t)||this;return u.scroll=u,u.observer=new MutationObserver(function(l){u.update(l)}),u.observer.observe(u.domNode,v),u.attach(),u}return e.prototype.detach=function(){o.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(t,u){this.update(),t===0&&u===this.length()?this.children.forEach(function(l){l.remove()}):o.prototype.deleteAt.call(this,t,u)},e.prototype.formatAt=function(t,u,l,a){this.update(),o.prototype.formatAt.call(this,t,u,l,a)},e.prototype.insertAt=function(t,u,l){this.update(),o.prototype.insertAt.call(this,t,u,l)},e.prototype.optimize=function(t,u){var l=this;t===void 0&&(t=[]),u===void 0&&(u={}),o.prototype.optimize.call(this,u);for(var a=[].slice.call(this.observer.takeRecords());a.length>0;)t.push(a.pop());for(var r=function(h,_){_===void 0&&(_=!0),!(h==null||h===l)&&h.domNode.parentNode!=null&&(h.domNode[b.DATA_KEY].mutations==null&&(h.domNode[b.DATA_KEY].mutations=[]),_&&r(h.parent))},i=function(h){h.domNode[b.DATA_KEY]==null||h.domNode[b.DATA_KEY].mutations==null||(h instanceof m.default&&h.children.forEach(i),h.optimize(u))},f=t,n=0;f.length>0;n+=1){if(n>=p)throw new Error("[Parchment] Maximum optimize iterations reached");for(f.forEach(function(h){var _=b.find(h.target,!0);_!=null&&(_.domNode===h.target&&(h.type==="childList"?(r(b.find(h.previousSibling,!1)),[].forEach.call(h.addedNodes,function(O){var w=b.find(O,!1);r(w,!1),w instanceof m.default&&w.children.forEach(function(S){r(S,!1)})})):h.type==="attributes"&&r(_.prev)),r(_))}),this.children.forEach(i),f=[].slice.call(this.observer.takeRecords()),a=f.slice();a.length>0;)t.push(a.pop())}},e.prototype.update=function(t,u){var l=this;u===void 0&&(u={}),t=t||this.observer.takeRecords(),t.map(function(a){var r=b.find(a.target,!0);return r==null?null:r.domNode[b.DATA_KEY].mutations==null?(r.domNode[b.DATA_KEY].mutations=[a],r):(r.domNode[b.DATA_KEY].mutations.push(a),null)}).forEach(function(a){a==null||a===l||a.domNode[b.DATA_KEY]==null||a.update(a.domNode[b.DATA_KEY].mutations||[],u)}),this.domNode[b.DATA_KEY].mutations!=null&&o.prototype.update.call(this,this.domNode[b.DATA_KEY].mutations,u),this.optimize(t,u)},e.blotName="scroll",e.defaultChild="block",e.scope=b.Scope.BLOCK_BLOT,e.tagName="DIV",e}(m.default);d.default=s},function(y,d,c){var k=this&&this.__extends||function(){var s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,e){o.__proto__=e}||function(o,e){for(var t in e)e.hasOwnProperty(t)&&(o[t]=e[t])};return function(o,e){s(o,e);function t(){this.constructor=o}o.prototype=e===null?Object.create(e):(t.prototype=e.prototype,new t)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(18),b=c(1);function v(s,o){if(Object.keys(s).length!==Object.keys(o).length)return!1;for(var e in s)if(s[e]!==o[e])return!1;return!0}var p=function(s){k(o,s);function o(){return s!==null&&s.apply(this,arguments)||this}return o.formats=function(e){if(e.tagName!==o.tagName)return s.formats.call(this,e)},o.prototype.format=function(e,t){var u=this;e===this.statics.blotName&&!t?(this.children.forEach(function(l){l instanceof m.default||(l=l.wrap(o.blotName,!0)),u.attributes.copy(l)}),this.unwrap()):s.prototype.format.call(this,e,t)},o.prototype.formatAt=function(e,t,u,l){if(this.formats()[u]!=null||b.query(u,b.Scope.ATTRIBUTE)){var a=this.isolate(e,t);a.format(u,l)}else s.prototype.formatAt.call(this,e,t,u,l)},o.prototype.optimize=function(e){s.prototype.optimize.call(this,e);var t=this.formats();if(Object.keys(t).length===0)return this.unwrap();var u=this.next;u instanceof o&&u.prev===this&&v(t,u.formats())&&(u.moveChildren(this),u.remove())},o.blotName="inline",o.scope=b.Scope.INLINE_BLOT,o.tagName="SPAN",o}(m.default);d.default=p},function(y,d,c){var k=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){p(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(18),b=c(1),v=function(p){k(s,p);function s(){return p!==null&&p.apply(this,arguments)||this}return s.formats=function(o){var e=b.query(s.blotName).tagName;if(o.tagName!==e)return p.formats.call(this,o)},s.prototype.format=function(o,e){b.query(o,b.Scope.BLOCK)!=null&&(o===this.statics.blotName&&!e?this.replaceWith(s.blotName):p.prototype.format.call(this,o,e))},s.prototype.formatAt=function(o,e,t,u){b.query(t,b.Scope.BLOCK)!=null?this.format(t,u):p.prototype.formatAt.call(this,o,e,t,u)},s.prototype.insertAt=function(o,e,t){if(t==null||b.query(e,b.Scope.INLINE)!=null)p.prototype.insertAt.call(this,o,e,t);else{var u=this.split(o),l=b.create(e,t);u.parent.insertBefore(l,u)}},s.prototype.update=function(o,e){navigator.userAgent.match(/Trident/)?this.build():p.prototype.update.call(this,o,e)},s.blotName="block",s.scope=b.Scope.BLOCK_BLOT,s.tagName="P",s}(m.default);d.default=v},function(y,d,c){var k=this&&this.__extends||function(){var v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(p,s){p.__proto__=s}||function(p,s){for(var o in s)s.hasOwnProperty(o)&&(p[o]=s[o])};return function(p,s){v(p,s);function o(){this.constructor=p}p.prototype=s===null?Object.create(s):(o.prototype=s.prototype,new o)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(19),b=function(v){k(p,v);function p(){return v!==null&&v.apply(this,arguments)||this}return p.formats=function(s){},p.prototype.format=function(s,o){v.prototype.formatAt.call(this,0,this.length(),s,o)},p.prototype.formatAt=function(s,o,e,t){s===0&&o===this.length()?this.format(e,t):v.prototype.formatAt.call(this,s,o,e,t)},p.prototype.formats=function(){return this.statics.formats(this.domNode)},p}(m.default);d.default=b},function(y,d,c){var k=this&&this.__extends||function(){var p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,o){s.__proto__=o}||function(s,o){for(var e in o)o.hasOwnProperty(e)&&(s[e]=o[e])};return function(s,o){p(s,o);function e(){this.constructor=s}s.prototype=o===null?Object.create(o):(e.prototype=o.prototype,new e)}}();Object.defineProperty(d,"__esModule",{value:!0});var m=c(19),b=c(1),v=function(p){k(s,p);function s(o){var e=p.call(this,o)||this;return e.text=e.statics.value(e.domNode),e}return s.create=function(o){return document.createTextNode(o)},s.value=function(o){var e=o.data;return e.normalize&&(e=e.normalize()),e},s.prototype.deleteAt=function(o,e){this.domNode.data=this.text=this.text.slice(0,o)+this.text.slice(o+e)},s.prototype.index=function(o,e){return this.domNode===o?e:-1},s.prototype.insertAt=function(o,e,t){t==null?(this.text=this.text.slice(0,o)+e+this.text.slice(o),this.domNode.data=this.text):p.prototype.insertAt.call(this,o,e,t)},s.prototype.length=function(){return this.text.length},s.prototype.optimize=function(o){p.prototype.optimize.call(this,o),this.text=this.statics.value(this.domNode),this.text.length===0?this.remove():this.next instanceof s&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},s.prototype.position=function(o,e){return[this.domNode,o]},s.prototype.split=function(o,e){if(e===void 0&&(e=!1),!e){if(o===0)return this;if(o===this.length())return this.next}var t=b.create(this.domNode.splitText(o));return this.parent.insertBefore(t,this.next),this.text=this.statics.value(this.domNode),t},s.prototype.update=function(o,e){var t=this;o.some(function(u){return u.type==="characterData"&&u.target===t.domNode})&&(this.text=this.statics.value(this.domNode))},s.prototype.value=function(){return this.text},s.blotName="text",s.scope=b.Scope.INLINE_BLOT,s}(m.default);d.default=v},function(y,d,c){var k=document.createElement("div");if(k.classList.toggle("test-class",!1),k.classList.contains("test-class")){var m=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(b,v){return arguments.length>1&&!this.contains(b)==!v?v:m.call(this,b)}}String.prototype.startsWith||(String.prototype.startsWith=function(b,v){return v=v||0,this.substr(v,b.length)===b}),String.prototype.endsWith||(String.prototype.endsWith=function(b,v){var p=this.toString();(typeof v!="number"||!isFinite(v)||Math.floor(v)!==v||v>p.length)&&(v=p.length),v-=b.length;var s=p.indexOf(b,v);return s!==-1&&s===v}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(v){if(this===null)throw new TypeError("Array.prototype.find called on null or undefined");if(typeof v!="function")throw new TypeError("predicate must be a function");for(var p=Object(this),s=p.length>>>0,o=arguments[1],e,t=0;t<s;t++)if(e=p[t],v.call(o,e,t,p))return e}}),document.addEventListener("DOMContentLoaded",function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)})},function(y,d){var c=-1,k=1,m=0;function b(n,h,_){if(n==h)return n?[[m,n]]:[];(_<0||n.length<_)&&(_=null);var O=o(n,h),w=n.substring(0,O);n=n.substring(O),h=h.substring(O),O=e(n,h);var S=n.substring(n.length-O);n=n.substring(0,n.length-O),h=h.substring(0,h.length-O);var E=v(n,h);return w&&E.unshift([m,w]),S&&E.push([m,S]),u(E),_!=null&&(E=r(E,_)),E=i(E),E}function v(n,h){var _;if(!n)return[[k,h]];if(!h)return[[c,n]];var O=n.length>h.length?n:h,w=n.length>h.length?h:n,S=O.indexOf(w);if(S!=-1)return _=[[k,O.substring(0,S)],[m,w],[k,O.substring(S+w.length)]],n.length>h.length&&(_[0][0]=_[2][0]=c),_;if(w.length==1)return[[c,n],[k,h]];var E=t(n,h);if(E){var g=E[0],A=E[1],T=E[2],x=E[3],j=E[4],F=b(g,T),U=b(A,x);return F.concat([[m,j]],U)}return p(n,h)}function p(n,h){for(var _=n.length,O=h.length,w=Math.ceil((_+O)/2),S=w,E=2*w,g=new Array(E),A=new Array(E),T=0;T<E;T++)g[T]=-1,A[T]=-1;g[S+1]=0,A[S+1]=0;for(var x=_-O,j=x%2!=0,F=0,U=0,W=0,B=0,M=0;M<w;M++){for(var N=-M+F;N<=M-U;N+=2){var q=S+N,R;N==-M||N!=M&&g[q-1]<g[q+1]?R=g[q+1]:R=g[q-1]+1;for(var H=R-N;R<_&&H<O&&n.charAt(R)==h.charAt(H);)R++,H++;if(g[q]=R,R>_)U+=2;else if(H>O)F+=2;else if(j){var C=S+x-N;if(C>=0&&C<E&&A[C]!=-1){var L=_-A[C];if(R>=L)return s(n,h,R,H)}}}for(var I=-M+W;I<=M-B;I+=2){var C=S+I,L;I==-M||I!=M&&A[C-1]<A[C+1]?L=A[C+1]:L=A[C-1]+1;for(var z=L-I;L<_&&z<O&&n.charAt(_-L-1)==h.charAt(O-z-1);)L++,z++;if(A[C]=L,L>_)B+=2;else if(z>O)W+=2;else if(!j){var q=S+x-I;if(q>=0&&q<E&&g[q]!=-1){var R=g[q],H=S+R-q;if(L=_-L,R>=L)return s(n,h,R,H)}}}}return[[c,n],[k,h]]}function s(n,h,_,O){var w=n.substring(0,_),S=h.substring(0,O),E=n.substring(_),g=h.substring(O),A=b(w,S),T=b(E,g);return A.concat(T)}function o(n,h){if(!n||!h||n.charAt(0)!=h.charAt(0))return 0;for(var _=0,O=Math.min(n.length,h.length),w=O,S=0;_<w;)n.substring(S,w)==h.substring(S,w)?(_=w,S=_):O=w,w=Math.floor((O-_)/2+_);return w}function e(n,h){if(!n||!h||n.charAt(n.length-1)!=h.charAt(h.length-1))return 0;for(var _=0,O=Math.min(n.length,h.length),w=O,S=0;_<w;)n.substring(n.length-w,n.length-S)==h.substring(h.length-w,h.length-S)?(_=w,S=_):O=w,w=Math.floor((O-_)/2+_);return w}function t(n,h){var _=n.length>h.length?n:h,O=n.length>h.length?h:n;if(_.length<4||O.length*2<_.length)return null;function w(U,W,B){for(var M=U.substring(B,B+Math.floor(U.length/4)),N=-1,q="",R,H,C,L;(N=W.indexOf(M,N+1))!=-1;){var I=o(U.substring(B),W.substring(N)),z=e(U.substring(0,B),W.substring(0,N));q.length<z+I&&(q=W.substring(N-z,N)+W.substring(N,N+I),R=U.substring(0,B-z),H=U.substring(B+I),C=W.substring(0,N-z),L=W.substring(N+I))}return q.length*2>=U.length?[R,H,C,L,q]:null}var S=w(_,O,Math.ceil(_.length/4)),E=w(_,O,Math.ceil(_.length/2)),g;if(!S&&!E)return null;E?S?g=S[4].length>E[4].length?S:E:g=E:g=S;var A,T,x,j;n.length>h.length?(A=g[0],T=g[1],x=g[2],j=g[3]):(x=g[0],j=g[1],A=g[2],T=g[3]);var F=g[4];return[A,T,x,j,F]}function u(n){n.push([m,""]);for(var h=0,_=0,O=0,w="",S="",E;h<n.length;)switch(n[h][0]){case k:O++,S+=n[h][1],h++;break;case c:_++,w+=n[h][1],h++;break;case m:_+O>1?(_!==0&&O!==0&&(E=o(S,w),E!==0&&(h-_-O>0&&n[h-_-O-1][0]==m?n[h-_-O-1][1]+=S.substring(0,E):(n.splice(0,0,[m,S.substring(0,E)]),h++),S=S.substring(E),w=w.substring(E)),E=e(S,w),E!==0&&(n[h][1]=S.substring(S.length-E)+n[h][1],S=S.substring(0,S.length-E),w=w.substring(0,w.length-E))),_===0?n.splice(h-O,_+O,[k,S]):O===0?n.splice(h-_,_+O,[c,w]):n.splice(h-_-O,_+O,[c,w],[k,S]),h=h-_-O+(_?1:0)+(O?1:0)+1):h!==0&&n[h-1][0]==m?(n[h-1][1]+=n[h][1],n.splice(h,1)):h++,O=0,_=0,w="",S="";break}n[n.length-1][1]===""&&n.pop();var g=!1;for(h=1;h<n.length-1;)n[h-1][0]==m&&n[h+1][0]==m&&(n[h][1].substring(n[h][1].length-n[h-1][1].length)==n[h-1][1]?(n[h][1]=n[h-1][1]+n[h][1].substring(0,n[h][1].length-n[h-1][1].length),n[h+1][1]=n[h-1][1]+n[h+1][1],n.splice(h-1,1),g=!0):n[h][1].substring(0,n[h+1][1].length)==n[h+1][1]&&(n[h-1][1]+=n[h+1][1],n[h][1]=n[h][1].substring(n[h+1][1].length)+n[h+1][1],n.splice(h+1,1),g=!0)),h++;g&&u(n)}var l=b;l.INSERT=k,l.DELETE=c,l.EQUAL=m,y.exports=l;function a(n,h){if(h===0)return[m,n];for(var _=0,O=0;O<n.length;O++){var w=n[O];if(w[0]===c||w[0]===m){var S=_+w[1].length;if(h===S)return[O+1,n];if(h<S){n=n.slice();var E=h-_,g=[w[0],w[1].slice(0,E)],A=[w[0],w[1].slice(E)];return n.splice(O,1,g,A),[O+1,n]}else _=S}}throw new Error("cursor_pos is out of bounds!")}function r(n,h){var _=a(n,h),O=_[1],w=_[0],S=O[w],E=O[w+1];if(S==null)return n;if(S[0]!==m)return n;if(E!=null&&S[1]+E[1]===E[1]+S[1])return O.splice(w,2,E,S),f(O,w,2);if(E!=null&&E[1].indexOf(S[1])===0){O.splice(w,2,[E[0],S[1]],[0,S[1]]);var g=E[1].slice(S[1].length);return g.length>0&&O.splice(w+2,0,[E[0],g]),f(O,w,3)}else return n}function i(n){for(var h=!1,_=function(E){return E.charCodeAt(0)>=56320&&E.charCodeAt(0)<=57343},O=function(E){return E.charCodeAt(E.length-1)>=55296&&E.charCodeAt(E.length-1)<=56319},w=2;w<n.length;w+=1)n[w-2][0]===m&&O(n[w-2][1])&&n[w-1][0]===c&&_(n[w-1][1])&&n[w][0]===k&&_(n[w][1])&&(h=!0,n[w-1][1]=n[w-2][1].slice(-1)+n[w-1][1],n[w][1]=n[w-2][1].slice(-1)+n[w][1],n[w-2][1]=n[w-2][1].slice(0,-1));if(!h)return n;for(var S=[],w=0;w<n.length;w+=1)n[w][1].length>0&&S.push(n[w]);return S}function f(n,h,_){for(var O=h+_-1;O>=0&&O>=h-1;O--)if(O+1<n.length){var w=n[O],S=n[O+1];w[0]===S[1]&&n.splice(O,2,[w[0],w[1]+S[1]])}return n}},function(y,d){d=y.exports=typeof Object.keys=="function"?Object.keys:c,d.shim=c;function c(k){var m=[];for(var b in k)m.push(b);return m}},function(y,d){var c=function(){return Object.prototype.toString.call(arguments)}()=="[object Arguments]";d=y.exports=c?k:m,d.supported=k;function k(b){return Object.prototype.toString.call(b)=="[object Arguments]"}d.unsupported=m;function m(b){return b&&typeof b=="object"&&typeof b.length=="number"&&Object.prototype.hasOwnProperty.call(b,"callee")&&!Object.prototype.propertyIsEnumerable.call(b,"callee")||!1}},function(y,d){var c=Object.prototype.hasOwnProperty,k="~";function m(){}Object.create&&(m.prototype=Object.create(null),new m().__proto__||(k=!1));function b(p,s,o){this.fn=p,this.context=s,this.once=o||!1}function v(){this._events=new m,this._eventsCount=0}v.prototype.eventNames=function(){var s=[],o,e;if(this._eventsCount===0)return s;for(e in o=this._events)c.call(o,e)&&s.push(k?e.slice(1):e);return Object.getOwnPropertySymbols?s.concat(Object.getOwnPropertySymbols(o)):s},v.prototype.listeners=function(s,o){var e=k?k+s:s,t=this._events[e];if(o)return!!t;if(!t)return[];if(t.fn)return[t.fn];for(var u=0,l=t.length,a=new Array(l);u<l;u++)a[u]=t[u].fn;return a},v.prototype.emit=function(s,o,e,t,u,l){var a=k?k+s:s;if(!this._events[a])return!1;var r=this._events[a],i=arguments.length,f,n;if(r.fn){switch(r.once&&this.removeListener(s,r.fn,void 0,!0),i){case 1:return r.fn.call(r.context),!0;case 2:return r.fn.call(r.context,o),!0;case 3:return r.fn.call(r.context,o,e),!0;case 4:return r.fn.call(r.context,o,e,t),!0;case 5:return r.fn.call(r.context,o,e,t,u),!0;case 6:return r.fn.call(r.context,o,e,t,u,l),!0}for(n=1,f=new Array(i-1);n<i;n++)f[n-1]=arguments[n];r.fn.apply(r.context,f)}else{var h=r.length,_;for(n=0;n<h;n++)switch(r[n].once&&this.removeListener(s,r[n].fn,void 0,!0),i){case 1:r[n].fn.call(r[n].context);break;case 2:r[n].fn.call(r[n].context,o);break;case 3:r[n].fn.call(r[n].context,o,e);break;case 4:r[n].fn.call(r[n].context,o,e,t);break;default:if(!f)for(_=1,f=new Array(i-1);_<i;_++)f[_-1]=arguments[_];r[n].fn.apply(r[n].context,f)}}return!0},v.prototype.on=function(s,o,e){var t=new b(o,e||this),u=k?k+s:s;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],t]:this._events[u].push(t):(this._events[u]=t,this._eventsCount++),this},v.prototype.once=function(s,o,e){var t=new b(o,e||this,!0),u=k?k+s:s;return this._events[u]?this._events[u].fn?this._events[u]=[this._events[u],t]:this._events[u].push(t):(this._events[u]=t,this._eventsCount++),this},v.prototype.removeListener=function(s,o,e,t){var u=k?k+s:s;if(!this._events[u])return this;if(!o)return--this._eventsCount===0?this._events=new m:delete this._events[u],this;var l=this._events[u];if(l.fn)l.fn===o&&(!t||l.once)&&(!e||l.context===e)&&(--this._eventsCount===0?this._events=new m:delete this._events[u]);else{for(var a=0,r=[],i=l.length;a<i;a++)(l[a].fn!==o||t&&!l[a].once||e&&l[a].context!==e)&&r.push(l[a]);r.length?this._events[u]=r.length===1?r[0]:r:--this._eventsCount===0?this._events=new m:delete this._events[u]}return this},v.prototype.removeAllListeners=function(s){var o;return s?(o=k?k+s:s,this._events[o]&&(--this._eventsCount===0?this._events=new m:delete this._events[o])):(this._events=new m,this._eventsCount=0),this},v.prototype.off=v.prototype.removeListener,v.prototype.addListener=v.prototype.on,v.prototype.setMaxListeners=function(){return this},v.prefixed=k,v.EventEmitter=v,typeof y!="undefined"&&(y.exports=v)},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.matchText=d.matchSpacing=d.matchNewline=d.matchBlot=d.matchAttributor=d.default=void 0;var k=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(V){return typeof V}:function(V){return V&&typeof Symbol=="function"&&V.constructor===Symbol&&V!==Symbol.prototype?"symbol":typeof V},m=function(){function V($,Y){var Q=[],G=!0,te=!1,ne=void 0;try{for(var re=$[Symbol.iterator](),he;!(G=(he=re.next()).done)&&(Q.push(he.value),!(Y&&Q.length===Y));G=!0);}catch(pe){te=!0,ne=pe}finally{try{!G&&re.return&&re.return()}finally{if(te)throw ne}}return Q}return function($,Y){if(Array.isArray($))return $;if(Symbol.iterator in Object($))return V($,Y);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),b=function(){function V($,Y){for(var Q=0;Q<Y.length;Q++){var G=Y[Q];G.enumerable=G.enumerable||!1,G.configurable=!0,"value"in G&&(G.writable=!0),Object.defineProperty($,G.key,G)}}return function($,Y,Q){return Y&&V($.prototype,Y),Q&&V($,Q),$}}(),v=c(3),p=A(v),s=c(2),o=A(s),e=c(0),t=A(e),u=c(5),l=A(u),a=c(10),r=A(a),i=c(9),f=A(i),n=c(36),h=c(37),_=c(13),O=A(_),w=c(26),S=c(38),E=c(39),g=c(40);function A(V){return V&&V.__esModule?V:{default:V}}function T(V,$,Y){return $ in V?Object.defineProperty(V,$,{value:Y,enumerable:!0,configurable:!0,writable:!0}):V[$]=Y,V}function x(V,$){if(!(V instanceof $))throw new TypeError("Cannot call a class as a function")}function j(V,$){if(!V)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return $&&(typeof $=="object"||typeof $=="function")?$:V}function F(V,$){if(typeof $!="function"&&$!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof $);V.prototype=Object.create($&&$.prototype,{constructor:{value:V,enumerable:!1,writable:!0,configurable:!0}}),$&&(Object.setPrototypeOf?Object.setPrototypeOf(V,$):V.__proto__=$)}var U=(0,r.default)("quill:clipboard"),W="__ql-matcher",B=[[Node.TEXT_NODE,ue],[Node.TEXT_NODE,ie],["br",X],[Node.ELEMENT_NODE,ie],[Node.ELEMENT_NODE,Z],[Node.ELEMENT_NODE,oe],[Node.ELEMENT_NODE,K],[Node.ELEMENT_NODE,se],["li",ee],["b",z.bind(z,"bold")],["i",z.bind(z,"italic")],["style",J]],M=[n.AlignAttribute,S.DirectionAttribute].reduce(function(V,$){return V[$.keyName]=$,V},{}),N=[n.AlignStyle,h.BackgroundStyle,w.ColorStyle,S.DirectionStyle,E.FontStyle,g.SizeStyle].reduce(function(V,$){return V[$.keyName]=$,V},{}),q=function(V){F($,V);function $(Y,Q){x(this,$);var G=j(this,($.__proto__||Object.getPrototypeOf($)).call(this,Y,Q));return G.quill.root.addEventListener("paste",G.onPaste.bind(G)),G.container=G.quill.addContainer("ql-clipboard"),G.container.setAttribute("contenteditable",!0),G.container.setAttribute("tabindex",-1),G.matchers=[],B.concat(G.options.matchers).forEach(function(te){var ne=m(te,2),re=ne[0],he=ne[1];!Q.matchVisual&&he===oe||G.addMatcher(re,he)}),G}return b($,[{key:"addMatcher",value:function(Q,G){this.matchers.push([Q,G])}},{key:"convert",value:function(Q){if(typeof Q=="string")return this.container.innerHTML=Q.replace(/\>\r?\n +\</g,"><"),this.convert();var G=this.quill.getFormat(this.quill.selection.savedRange.index);if(G[O.default.blotName]){var te=this.container.innerText;return this.container.innerHTML="",new o.default().insert(te,T({},O.default.blotName,G[O.default.blotName]))}var ne=this.prepareMatching(),re=m(ne,2),he=re[0],pe=re[1],le=I(this.container,he,pe);return C(le,`
`)&&le.ops[le.ops.length-1].attributes==null&&(le=le.compose(new o.default().retain(le.length()-1).delete(1))),U.log("convert",this.container.innerHTML,le),this.container.innerHTML="",le}},{key:"dangerouslyPasteHTML",value:function(Q,G){var te=arguments.length>2&&arguments[2]!==void 0?arguments[2]:l.default.sources.API;if(typeof Q=="string")this.quill.setContents(this.convert(Q),G),this.quill.setSelection(0,l.default.sources.SILENT);else{var ne=this.convert(G);this.quill.updateContents(new o.default().retain(Q).concat(ne),te),this.quill.setSelection(Q+ne.length(),l.default.sources.SILENT)}}},{key:"onPaste",value:function(Q){var G=this;if(!(Q.defaultPrevented||!this.quill.isEnabled())){var te=this.quill.getSelection(),ne=new o.default().retain(te.index),re=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(l.default.sources.SILENT),setTimeout(function(){ne=ne.concat(G.convert()).delete(te.length),G.quill.updateContents(ne,l.default.sources.USER),G.quill.setSelection(ne.length()-te.length,l.default.sources.SILENT),G.quill.scrollingContainer.scrollTop=re,G.quill.focus()},1)}}},{key:"prepareMatching",value:function(){var Q=this,G=[],te=[];return this.matchers.forEach(function(ne){var re=m(ne,2),he=re[0],pe=re[1];switch(he){case Node.TEXT_NODE:te.push(pe);break;case Node.ELEMENT_NODE:G.push(pe);break;default:[].forEach.call(Q.container.querySelectorAll(he),function(le){le[W]=le[W]||[],le[W].push(pe)});break}}),[G,te]}}]),$}(f.default);q.DEFAULTS={matchers:[],matchVisual:!0};function R(V,$,Y){return(typeof $=="undefined"?"undefined":k($))==="object"?Object.keys($).reduce(function(Q,G){return R(Q,G,$[G])},V):V.reduce(function(Q,G){return G.attributes&&G.attributes[$]?Q.push(G):Q.insert(G.insert,(0,p.default)({},T({},$,Y),G.attributes))},new o.default)}function H(V){if(V.nodeType!==Node.ELEMENT_NODE)return{};var $="__ql-computed-style";return V[$]||(V[$]=window.getComputedStyle(V))}function C(V,$){for(var Y="",Q=V.ops.length-1;Q>=0&&Y.length<$.length;--Q){var G=V.ops[Q];if(typeof G.insert!="string")break;Y=G.insert+Y}return Y.slice(-1*$.length)===$}function L(V){if(V.childNodes.length===0)return!1;var $=H(V);return["block","list-item"].indexOf($.display)>-1}function I(V,$,Y){return V.nodeType===V.TEXT_NODE?Y.reduce(function(Q,G){return G(V,Q)},new o.default):V.nodeType===V.ELEMENT_NODE?[].reduce.call(V.childNodes||[],function(Q,G){var te=I(G,$,Y);return G.nodeType===V.ELEMENT_NODE&&(te=$.reduce(function(ne,re){return re(G,ne)},te),te=(G[W]||[]).reduce(function(ne,re){return re(G,ne)},te)),Q.concat(te)},new o.default):new o.default}function z(V,$,Y){return R(Y,V,!0)}function K(V,$){var Y=t.default.Attributor.Attribute.keys(V),Q=t.default.Attributor.Class.keys(V),G=t.default.Attributor.Style.keys(V),te={};return Y.concat(Q).concat(G).forEach(function(ne){var re=t.default.query(ne,t.default.Scope.ATTRIBUTE);re!=null&&(te[re.attrName]=re.value(V),te[re.attrName])||(re=M[ne],re!=null&&(re.attrName===ne||re.keyName===ne)&&(te[re.attrName]=re.value(V)||void 0),re=N[ne],re!=null&&(re.attrName===ne||re.keyName===ne)&&(re=N[ne],te[re.attrName]=re.value(V)||void 0))}),Object.keys(te).length>0&&($=R($,te)),$}function Z(V,$){var Y=t.default.query(V);if(Y==null)return $;if(Y.prototype instanceof t.default.Embed){var Q={},G=Y.value(V);G!=null&&(Q[Y.blotName]=G,$=new o.default().insert(Q,Y.formats(V)))}else typeof Y.formats=="function"&&($=R($,Y.blotName,Y.formats(V)));return $}function X(V,$){return C($,`
`)||$.insert(`
`),$}function J(){return new o.default}function ee(V,$){var Y=t.default.query(V);if(Y==null||Y.blotName!=="list-item"||!C($,`
`))return $;for(var Q=-1,G=V.parentNode;!G.classList.contains("ql-clipboard");)(t.default.query(G)||{}).blotName==="list"&&(Q+=1),G=G.parentNode;return Q<=0?$:$.compose(new o.default().retain($.length()-1).retain(1,{indent:Q}))}function ie(V,$){return C($,`
`)||(L(V)||$.length()>0&&V.nextSibling&&L(V.nextSibling))&&$.insert(`
`),$}function oe(V,$){if(L(V)&&V.nextElementSibling!=null&&!C($,`

`)){var Y=V.offsetHeight+parseFloat(H(V).marginTop)+parseFloat(H(V).marginBottom);V.nextElementSibling.offsetTop>V.offsetTop+Y*1.5&&$.insert(`
`)}return $}function se(V,$){var Y={},Q=V.style||{};return Q.fontStyle&&H(V).fontStyle==="italic"&&(Y.italic=!0),Q.fontWeight&&(H(V).fontWeight.startsWith("bold")||parseInt(H(V).fontWeight)>=700)&&(Y.bold=!0),Object.keys(Y).length>0&&($=R($,Y)),parseFloat(Q.textIndent||0)>0&&($=new o.default().insert("	").concat($)),$}function ue(V,$){var Y=V.data;if(V.parentNode.tagName==="O:P")return $.insert(Y.trim());if(Y.trim().length===0&&V.parentNode.classList.contains("ql-clipboard"))return $;if(!H(V.parentNode).whiteSpace.startsWith("pre")){var Q=function(te,ne){return ne=ne.replace(/[^\u00a0]/g,""),ne.length<1&&te?" ":ne};Y=Y.replace(/\r\n/g," ").replace(/\n/g," "),Y=Y.replace(/\s\s+/g,Q.bind(Q,!0)),(V.previousSibling==null&&L(V.parentNode)||V.previousSibling!=null&&L(V.previousSibling))&&(Y=Y.replace(/^\s+/,Q.bind(Q,!1))),(V.nextSibling==null&&L(V.parentNode)||V.nextSibling!=null&&L(V.nextSibling))&&(Y=Y.replace(/\s+$/,Q.bind(Q,!1)))}return $.insert(Y)}d.default=q,d.matchAttributor=K,d.matchBlot=Z,d.matchNewline=ie,d.matchSpacing=oe,d.matchText=ue},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(6),v=p(b);function p(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return k(l,[{key:"optimize",value:function(r){m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"optimize",this).call(this,r),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return m(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),l}(v.default);t.blotName="bold",t.tagName=["STRONG","B"],d.default=t},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.addControls=d.default=void 0;var k=function(){function g(A,T){var x=[],j=!0,F=!1,U=void 0;try{for(var W=A[Symbol.iterator](),B;!(j=(B=W.next()).done)&&(x.push(B.value),!(T&&x.length===T));j=!0);}catch(M){F=!0,U=M}finally{try{!j&&W.return&&W.return()}finally{if(F)throw U}}return x}return function(A,T){if(Array.isArray(A))return A;if(Symbol.iterator in Object(A))return g(A,T);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function(){function g(A,T){for(var x=0;x<T.length;x++){var j=T[x];j.enumerable=j.enumerable||!1,j.configurable=!0,"value"in j&&(j.writable=!0),Object.defineProperty(A,j.key,j)}}return function(A,T,x){return T&&g(A.prototype,T),x&&g(A,x),A}}(),b=c(2),v=r(b),p=c(0),s=r(p),o=c(5),e=r(o),t=c(10),u=r(t),l=c(9),a=r(l);function r(g){return g&&g.__esModule?g:{default:g}}function i(g,A,T){return A in g?Object.defineProperty(g,A,{value:T,enumerable:!0,configurable:!0,writable:!0}):g[A]=T,g}function f(g,A){if(!(g instanceof A))throw new TypeError("Cannot call a class as a function")}function n(g,A){if(!g)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return A&&(typeof A=="object"||typeof A=="function")?A:g}function h(g,A){if(typeof A!="function"&&A!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof A);g.prototype=Object.create(A&&A.prototype,{constructor:{value:g,enumerable:!1,writable:!0,configurable:!0}}),A&&(Object.setPrototypeOf?Object.setPrototypeOf(g,A):g.__proto__=A)}var _=(0,u.default)("quill:toolbar"),O=function(g){h(A,g);function A(T,x){f(this,A);var j=n(this,(A.__proto__||Object.getPrototypeOf(A)).call(this,T,x));if(Array.isArray(j.options.container)){var F=document.createElement("div");S(F,j.options.container),T.container.parentNode.insertBefore(F,T.container),j.container=F}else typeof j.options.container=="string"?j.container=document.querySelector(j.options.container):j.container=j.options.container;if(!(j.container instanceof HTMLElement)){var U;return U=_.error("Container required for toolbar",j.options),n(j,U)}return j.container.classList.add("ql-toolbar"),j.controls=[],j.handlers={},Object.keys(j.options.handlers).forEach(function(W){j.addHandler(W,j.options.handlers[W])}),[].forEach.call(j.container.querySelectorAll("button, select"),function(W){j.attach(W)}),j.quill.on(e.default.events.EDITOR_CHANGE,function(W,B){W===e.default.events.SELECTION_CHANGE&&j.update(B)}),j.quill.on(e.default.events.SCROLL_OPTIMIZE,function(){var W=j.quill.selection.getRange(),B=k(W,1),M=B[0];j.update(M)}),j}return m(A,[{key:"addHandler",value:function(x,j){this.handlers[x]=j}},{key:"attach",value:function(x){var j=this,F=[].find.call(x.classList,function(W){return W.indexOf("ql-")===0});if(!!F){if(F=F.slice(3),x.tagName==="BUTTON"&&x.setAttribute("type","button"),this.handlers[F]==null){if(this.quill.scroll.whitelist!=null&&this.quill.scroll.whitelist[F]==null){_.warn("ignoring attaching to disabled format",F,x);return}if(s.default.query(F)==null){_.warn("ignoring attaching to nonexistent format",F,x);return}}var U=x.tagName==="SELECT"?"change":"click";x.addEventListener(U,function(W){var B=void 0;if(x.tagName==="SELECT"){if(x.selectedIndex<0)return;var M=x.options[x.selectedIndex];M.hasAttribute("selected")?B=!1:B=M.value||!1}else x.classList.contains("ql-active")?B=!1:B=x.value||!x.hasAttribute("value"),W.preventDefault();j.quill.focus();var N=j.quill.selection.getRange(),q=k(N,1),R=q[0];if(j.handlers[F]!=null)j.handlers[F].call(j,B);else if(s.default.query(F).prototype instanceof s.default.Embed){if(B=prompt("Enter "+F),!B)return;j.quill.updateContents(new v.default().retain(R.index).delete(R.length).insert(i({},F,B)),e.default.sources.USER)}else j.quill.format(F,B,e.default.sources.USER);j.update(R)}),this.controls.push([F,x])}}},{key:"update",value:function(x){var j=x==null?{}:this.quill.getFormat(x);this.controls.forEach(function(F){var U=k(F,2),W=U[0],B=U[1];if(B.tagName==="SELECT"){var M=void 0;if(x==null)M=null;else if(j[W]==null)M=B.querySelector("option[selected]");else if(!Array.isArray(j[W])){var N=j[W];typeof N=="string"&&(N=N.replace(/\"/g,'\\"')),M=B.querySelector('option[value="'+N+'"]')}M==null?(B.value="",B.selectedIndex=-1):M.selected=!0}else if(x==null)B.classList.remove("ql-active");else if(B.hasAttribute("value")){var q=j[W]===B.getAttribute("value")||j[W]!=null&&j[W].toString()===B.getAttribute("value")||j[W]==null&&!B.getAttribute("value");B.classList.toggle("ql-active",q)}else B.classList.toggle("ql-active",j[W]!=null)})}}]),A}(a.default);O.DEFAULTS={};function w(g,A,T){var x=document.createElement("button");x.setAttribute("type","button"),x.classList.add("ql-"+A),T!=null&&(x.value=T),g.appendChild(x)}function S(g,A){Array.isArray(A[0])||(A=[A]),A.forEach(function(T){var x=document.createElement("span");x.classList.add("ql-formats"),T.forEach(function(j){if(typeof j=="string")w(x,j);else{var F=Object.keys(j)[0],U=j[F];Array.isArray(U)?E(x,F,U):w(x,F,U)}}),g.appendChild(x)})}function E(g,A,T){var x=document.createElement("select");x.classList.add("ql-"+A),T.forEach(function(j){var F=document.createElement("option");j!==!1?F.setAttribute("value",j):F.setAttribute("selected","selected"),x.appendChild(F)}),g.appendChild(x)}O.DEFAULTS={container:null,handlers:{clean:function(){var A=this,T=this.quill.getSelection();if(T!=null)if(T.length==0){var x=this.quill.getFormat();Object.keys(x).forEach(function(j){s.default.query(j,s.default.Scope.INLINE)!=null&&A.quill.format(j,!1)})}else this.quill.removeFormat(T,e.default.sources.USER)},direction:function(A){var T=this.quill.getFormat().align;A==="rtl"&&T==null?this.quill.format("align","right",e.default.sources.USER):!A&&T==="right"&&this.quill.format("align",!1,e.default.sources.USER),this.quill.format("direction",A,e.default.sources.USER)},indent:function(A){var T=this.quill.getSelection(),x=this.quill.getFormat(T),j=parseInt(x.indent||0);if(A==="+1"||A==="-1"){var F=A==="+1"?1:-1;x.direction==="rtl"&&(F*=-1),this.quill.format("indent",j+F,e.default.sources.USER)}},link:function(A){A===!0&&(A=prompt("Enter link URL:")),this.quill.format("link",A,e.default.sources.USER)},list:function(A){var T=this.quill.getSelection(),x=this.quill.getFormat(T);A==="check"?x.list==="checked"||x.list==="unchecked"?this.quill.format("list",!1,e.default.sources.USER):this.quill.format("list","unchecked",e.default.sources.USER):this.quill.format("list",A,e.default.sources.USER)}}},d.default=O,d.addControls=S},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(28),v=p(b);function p(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(a,r){s(this,l);var i=o(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return i.label.innerHTML=r,i.container.classList.add("ql-color-picker"),[].slice.call(i.container.querySelectorAll(".ql-picker-item"),0,7).forEach(function(f){f.classList.add("ql-primary")}),i}return k(l,[{key:"buildItem",value:function(r){var i=m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"buildItem",this).call(this,r);return i.style.backgroundColor=r.getAttribute("value")||"",i}},{key:"selectItem",value:function(r,i){m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,r,i);var f=this.label.querySelector(".ql-color-label"),n=r&&r.getAttribute("data-value")||"";f&&(f.tagName==="line"?f.style.stroke=n:f.style.fill=n)}}]),l}(v.default);d.default=t},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(28),v=p(b);function p(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(a,r){s(this,l);var i=o(this,(l.__proto__||Object.getPrototypeOf(l)).call(this,a));return i.container.classList.add("ql-icon-picker"),[].forEach.call(i.container.querySelectorAll(".ql-picker-item"),function(f){f.innerHTML=r[f.getAttribute("data-value")||""]}),i.defaultItem=i.container.querySelector(".ql-selected"),i.selectItem(i.defaultItem),i}return k(l,[{key:"selectItem",value:function(r,i){m(l.prototype.__proto__||Object.getPrototypeOf(l.prototype),"selectItem",this).call(this,r,i),r=r||this.defaultItem,this.label.innerHTML=r.innerHTML}}]),l}(v.default);d.default=t},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function v(p,s){for(var o=0;o<s.length;o++){var e=s[o];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(p,e.key,e)}}return function(p,s,o){return s&&v(p.prototype,s),o&&v(p,o),p}}();function m(v,p){if(!(v instanceof p))throw new TypeError("Cannot call a class as a function")}var b=function(){function v(p,s){var o=this;m(this,v),this.quill=p,this.boundsContainer=s||document.body,this.root=p.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",function(){o.root.style.marginTop=-1*o.quill.root.scrollTop+"px"}),this.hide()}return k(v,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(s){var o=s.left+s.width/2-this.root.offsetWidth/2,e=s.bottom+this.quill.root.scrollTop;this.root.style.left=o+"px",this.root.style.top=e+"px",this.root.classList.remove("ql-flip");var t=this.boundsContainer.getBoundingClientRect(),u=this.root.getBoundingClientRect(),l=0;if(u.right>t.right&&(l=t.right-u.right,this.root.style.left=o+l+"px"),u.left<t.left&&(l=t.left-u.left,this.root.style.left=o+l+"px"),u.bottom>t.bottom){var a=u.bottom-u.top,r=s.bottom-s.top+a;this.root.style.top=e-r+"px",this.root.classList.add("ql-flip")}return l}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),v}();d.default=b},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function E(g,A){var T=[],x=!0,j=!1,F=void 0;try{for(var U=g[Symbol.iterator](),W;!(x=(W=U.next()).done)&&(T.push(W.value),!(A&&T.length===A));x=!0);}catch(B){j=!0,F=B}finally{try{!x&&U.return&&U.return()}finally{if(j)throw F}}return T}return function(g,A){if(Array.isArray(g))return g;if(Symbol.iterator in Object(g))return E(g,A);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),m=function E(g,A,T){g===null&&(g=Function.prototype);var x=Object.getOwnPropertyDescriptor(g,A);if(x===void 0){var j=Object.getPrototypeOf(g);return j===null?void 0:E(j,A,T)}else{if("value"in x)return x.value;var F=x.get;return F===void 0?void 0:F.call(T)}},b=function(){function E(g,A){for(var T=0;T<A.length;T++){var x=A[T];x.enumerable=x.enumerable||!1,x.configurable=!0,"value"in x&&(x.writable=!0),Object.defineProperty(g,x.key,x)}}return function(g,A,T){return A&&E(g.prototype,A),T&&E(g,T),g}}(),v=c(3),p=f(v),s=c(8),o=f(s),e=c(43),t=f(e),u=c(27),l=f(u),a=c(15),r=c(41),i=f(r);function f(E){return E&&E.__esModule?E:{default:E}}function n(E,g){if(!(E instanceof g))throw new TypeError("Cannot call a class as a function")}function h(E,g){if(!E)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return g&&(typeof g=="object"||typeof g=="function")?g:E}function _(E,g){if(typeof g!="function"&&g!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof g);E.prototype=Object.create(g&&g.prototype,{constructor:{value:E,enumerable:!1,writable:!0,configurable:!0}}),g&&(Object.setPrototypeOf?Object.setPrototypeOf(E,g):E.__proto__=g)}var O=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],w=function(E){_(g,E);function g(A,T){n(this,g),T.modules.toolbar!=null&&T.modules.toolbar.container==null&&(T.modules.toolbar.container=O);var x=h(this,(g.__proto__||Object.getPrototypeOf(g)).call(this,A,T));return x.quill.container.classList.add("ql-snow"),x}return b(g,[{key:"extendToolbar",value:function(T){T.container.classList.add("ql-snow"),this.buildButtons([].slice.call(T.container.querySelectorAll("button")),i.default),this.buildPickers([].slice.call(T.container.querySelectorAll("select")),i.default),this.tooltip=new S(this.quill,this.options.bounds),T.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},function(x,j){T.handlers.link.call(T,!j.format.link)})}}]),g}(t.default);w.DEFAULTS=(0,p.default)(!0,{},t.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(g){if(g){var A=this.quill.getSelection();if(A==null||A.length==0)return;var T=this.quill.getText(A);/^\S+@\S+\.\S+$/.test(T)&&T.indexOf("mailto:")!==0&&(T="mailto:"+T);var x=this.quill.theme.tooltip;x.edit("link",T)}else this.quill.format("link",!1)}}}}});var S=function(E){_(g,E);function g(A,T){n(this,g);var x=h(this,(g.__proto__||Object.getPrototypeOf(g)).call(this,A,T));return x.preview=x.root.querySelector("a.ql-preview"),x}return b(g,[{key:"listen",value:function(){var T=this;m(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",function(x){T.root.classList.contains("ql-editing")?T.save():T.edit("link",T.preview.textContent),x.preventDefault()}),this.root.querySelector("a.ql-remove").addEventListener("click",function(x){if(T.linkRange!=null){var j=T.linkRange;T.restoreFocus(),T.quill.formatText(j,"link",!1,o.default.sources.USER),delete T.linkRange}x.preventDefault(),T.hide()}),this.quill.on(o.default.events.SELECTION_CHANGE,function(x,j,F){if(x!=null){if(x.length===0&&F===o.default.sources.USER){var U=T.quill.scroll.descendant(l.default,x.index),W=k(U,2),B=W[0],M=W[1];if(B!=null){T.linkRange=new a.Range(x.index-M,B.length());var N=l.default.formats(B.domNode);T.preview.textContent=N,T.preview.setAttribute("href",N),T.show(),T.position(T.quill.getBounds(T.linkRange));return}}else delete T.linkRange;T.hide()}})}},{key:"show",value:function(){m(g.prototype.__proto__||Object.getPrototypeOf(g.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),g}(e.BaseTooltip);S.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),d.default=w},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(29),m=G(k),b=c(36),v=c(38),p=c(64),s=c(65),o=G(s),e=c(66),t=G(e),u=c(67),l=G(u),a=c(37),r=c(26),i=c(39),f=c(40),n=c(56),h=G(n),_=c(68),O=G(_),w=c(27),S=G(w),E=c(69),g=G(E),A=c(70),T=G(A),x=c(71),j=G(x),F=c(72),U=G(F),W=c(73),B=G(W),M=c(13),N=G(M),q=c(74),R=G(q),H=c(75),C=G(H),L=c(57),I=G(L),z=c(41),K=G(z),Z=c(28),X=G(Z),J=c(59),ee=G(J),ie=c(60),oe=G(ie),se=c(61),ue=G(se),V=c(108),$=G(V),Y=c(62),Q=G(Y);function G(te){return te&&te.__esModule?te:{default:te}}m.default.register({"attributors/attribute/direction":v.DirectionAttribute,"attributors/class/align":b.AlignClass,"attributors/class/background":a.BackgroundClass,"attributors/class/color":r.ColorClass,"attributors/class/direction":v.DirectionClass,"attributors/class/font":i.FontClass,"attributors/class/size":f.SizeClass,"attributors/style/align":b.AlignStyle,"attributors/style/background":a.BackgroundStyle,"attributors/style/color":r.ColorStyle,"attributors/style/direction":v.DirectionStyle,"attributors/style/font":i.FontStyle,"attributors/style/size":f.SizeStyle},!0),m.default.register({"formats/align":b.AlignClass,"formats/direction":v.DirectionClass,"formats/indent":p.IndentClass,"formats/background":a.BackgroundStyle,"formats/color":r.ColorStyle,"formats/font":i.FontClass,"formats/size":f.SizeClass,"formats/blockquote":o.default,"formats/code-block":N.default,"formats/header":t.default,"formats/list":l.default,"formats/bold":h.default,"formats/code":M.Code,"formats/italic":O.default,"formats/link":S.default,"formats/script":g.default,"formats/strike":T.default,"formats/underline":j.default,"formats/image":U.default,"formats/video":B.default,"formats/list/item":u.ListItem,"modules/formula":R.default,"modules/syntax":C.default,"modules/toolbar":I.default,"themes/bubble":$.default,"themes/snow":Q.default,"ui/icons":K.default,"ui/picker":X.default,"ui/icon-picker":oe.default,"ui/color-picker":ee.default,"ui/tooltip":ue.default},!0),d.default=m.default},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.IndentClass=void 0;var k=function(){function l(a,r){for(var i=0;i<r.length;i++){var f=r[i];f.enumerable=f.enumerable||!1,f.configurable=!0,"value"in f&&(f.writable=!0),Object.defineProperty(a,f.key,f)}}return function(a,r,i){return r&&l(a.prototype,r),i&&l(a,i),a}}(),m=function l(a,r,i){a===null&&(a=Function.prototype);var f=Object.getOwnPropertyDescriptor(a,r);if(f===void 0){var n=Object.getPrototypeOf(a);return n===null?void 0:l(n,r,i)}else{if("value"in f)return f.value;var h=f.get;return h===void 0?void 0:h.call(i)}},b=c(0),v=p(b);function p(l){return l&&l.__esModule?l:{default:l}}function s(l,a){if(!(l instanceof a))throw new TypeError("Cannot call a class as a function")}function o(l,a){if(!l)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return a&&(typeof a=="object"||typeof a=="function")?a:l}function e(l,a){if(typeof a!="function"&&a!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof a);l.prototype=Object.create(a&&a.prototype,{constructor:{value:l,enumerable:!1,writable:!0,configurable:!0}}),a&&(Object.setPrototypeOf?Object.setPrototypeOf(l,a):l.__proto__=a)}var t=function(l){e(a,l);function a(){return s(this,a),o(this,(a.__proto__||Object.getPrototypeOf(a)).apply(this,arguments))}return k(a,[{key:"add",value:function(i,f){if(f==="+1"||f==="-1"){var n=this.value(i)||0;f=f==="+1"?n+1:n-1}return f===0?(this.remove(i),!0):m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"add",this).call(this,i,f)}},{key:"canAdd",value:function(i,f){return m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,i,f)||m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"canAdd",this).call(this,i,parseInt(f))}},{key:"value",value:function(i){return parseInt(m(a.prototype.__proto__||Object.getPrototypeOf(a.prototype),"value",this).call(this,i))||void 0}}]),a}(v.default.Attributor.Class),u=new t("indent","ql-indent",{scope:v.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});d.IndentClass=u},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(4),m=b(k);function b(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return v(this,t),p(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="blockquote",o.tagName="blockquote",d.default=o},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function t(u,l){for(var a=0;a<l.length;a++){var r=l[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(u,r.key,r)}}return function(u,l,a){return l&&t(u.prototype,l),a&&t(u,a),u}}(),m=c(4),b=v(m);function v(t){return t&&t.__esModule?t:{default:t}}function p(t,u){if(!(t instanceof u))throw new TypeError("Cannot call a class as a function")}function s(t,u){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return u&&(typeof u=="object"||typeof u=="function")?u:t}function o(t,u){if(typeof u!="function"&&u!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof u);t.prototype=Object.create(u&&u.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),u&&(Object.setPrototypeOf?Object.setPrototypeOf(t,u):t.__proto__=u)}var e=function(t){o(u,t);function u(){return p(this,u),s(this,(u.__proto__||Object.getPrototypeOf(u)).apply(this,arguments))}return k(u,null,[{key:"formats",value:function(a){return this.tagName.indexOf(a.tagName)+1}}]),u}(b.default);e.blotName="header",e.tagName=["H1","H2","H3","H4","H5","H6"],d.default=e},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.ListItem=void 0;var k=function(){function n(h,_){for(var O=0;O<_.length;O++){var w=_[O];w.enumerable=w.enumerable||!1,w.configurable=!0,"value"in w&&(w.writable=!0),Object.defineProperty(h,w.key,w)}}return function(h,_,O){return _&&n(h.prototype,_),O&&n(h,O),h}}(),m=function n(h,_,O){h===null&&(h=Function.prototype);var w=Object.getOwnPropertyDescriptor(h,_);if(w===void 0){var S=Object.getPrototypeOf(h);return S===null?void 0:n(S,_,O)}else{if("value"in w)return w.value;var E=w.get;return E===void 0?void 0:E.call(O)}},b=c(0),v=t(b),p=c(4),s=t(p),o=c(25),e=t(o);function t(n){return n&&n.__esModule?n:{default:n}}function u(n,h,_){return h in n?Object.defineProperty(n,h,{value:_,enumerable:!0,configurable:!0,writable:!0}):n[h]=_,n}function l(n,h){if(!(n instanceof h))throw new TypeError("Cannot call a class as a function")}function a(n,h){if(!n)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return h&&(typeof h=="object"||typeof h=="function")?h:n}function r(n,h){if(typeof h!="function"&&h!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof h);n.prototype=Object.create(h&&h.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),h&&(Object.setPrototypeOf?Object.setPrototypeOf(n,h):n.__proto__=h)}var i=function(n){r(h,n);function h(){return l(this,h),a(this,(h.__proto__||Object.getPrototypeOf(h)).apply(this,arguments))}return k(h,[{key:"format",value:function(O,w){O===f.blotName&&!w?this.replaceWith(v.default.create(this.statics.scope)):m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"format",this).call(this,O,w)}},{key:"remove",value:function(){this.prev==null&&this.next==null?this.parent.remove():m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(O,w){return this.parent.isolate(this.offset(this.parent),this.length()),O===this.parent.statics.blotName?(this.parent.replaceWith(O,w),this):(this.parent.unwrap(),m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"replaceWith",this).call(this,O,w))}}],[{key:"formats",value:function(O){return O.tagName===this.tagName?void 0:m(h.__proto__||Object.getPrototypeOf(h),"formats",this).call(this,O)}}]),h}(s.default);i.blotName="list-item",i.tagName="LI";var f=function(n){r(h,n),k(h,null,[{key:"create",value:function(O){var w=O==="ordered"?"OL":"UL",S=m(h.__proto__||Object.getPrototypeOf(h),"create",this).call(this,w);return(O==="checked"||O==="unchecked")&&S.setAttribute("data-checked",O==="checked"),S}},{key:"formats",value:function(O){if(O.tagName==="OL")return"ordered";if(O.tagName==="UL")return O.hasAttribute("data-checked")?O.getAttribute("data-checked")==="true"?"checked":"unchecked":"bullet"}}]);function h(_){l(this,h);var O=a(this,(h.__proto__||Object.getPrototypeOf(h)).call(this,_)),w=function(E){if(E.target.parentNode===_){var g=O.statics.formats(_),A=v.default.find(E.target);g==="checked"?A.format("list","unchecked"):g==="unchecked"&&A.format("list","checked")}};return _.addEventListener("touchstart",w),_.addEventListener("mousedown",w),O}return k(h,[{key:"format",value:function(O,w){this.children.length>0&&this.children.tail.format(O,w)}},{key:"formats",value:function(){return u({},this.statics.blotName,this.statics.formats(this.domNode))}},{key:"insertBefore",value:function(O,w){if(O instanceof i)m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"insertBefore",this).call(this,O,w);else{var S=w==null?this.length():w.offset(this),E=this.split(S);E.parent.insertBefore(O,E)}}},{key:"optimize",value:function(O){m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"optimize",this).call(this,O);var w=this.next;w!=null&&w.prev===this&&w.statics.blotName===this.statics.blotName&&w.domNode.tagName===this.domNode.tagName&&w.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(w.moveChildren(this),w.remove())}},{key:"replace",value:function(O){if(O.statics.blotName!==this.statics.blotName){var w=v.default.create(this.statics.defaultChild);O.moveChildren(w),this.appendChild(w)}m(h.prototype.__proto__||Object.getPrototypeOf(h.prototype),"replace",this).call(this,O)}}]),h}(e.default);f.blotName="list",f.scope=v.default.Scope.BLOCK_BLOT,f.tagName=["OL","UL"],f.defaultChild="list-item",f.allowedChildren=[i],d.ListItem=i,d.default=f},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(56),m=b(k);function b(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return v(this,t),p(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="italic",o.tagName=["EM","I"],d.default=o},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function u(l,a){for(var r=0;r<a.length;r++){var i=a[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(l,i.key,i)}}return function(l,a,r){return a&&u(l.prototype,a),r&&u(l,r),l}}(),m=function u(l,a,r){l===null&&(l=Function.prototype);var i=Object.getOwnPropertyDescriptor(l,a);if(i===void 0){var f=Object.getPrototypeOf(l);return f===null?void 0:u(f,a,r)}else{if("value"in i)return i.value;var n=i.get;return n===void 0?void 0:n.call(r)}},b=c(6),v=p(b);function p(u){return u&&u.__esModule?u:{default:u}}function s(u,l){if(!(u instanceof l))throw new TypeError("Cannot call a class as a function")}function o(u,l){if(!u)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return l&&(typeof l=="object"||typeof l=="function")?l:u}function e(u,l){if(typeof l!="function"&&l!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof l);u.prototype=Object.create(l&&l.prototype,{constructor:{value:u,enumerable:!1,writable:!0,configurable:!0}}),l&&(Object.setPrototypeOf?Object.setPrototypeOf(u,l):u.__proto__=l)}var t=function(u){e(l,u);function l(){return s(this,l),o(this,(l.__proto__||Object.getPrototypeOf(l)).apply(this,arguments))}return k(l,null,[{key:"create",value:function(r){return r==="super"?document.createElement("sup"):r==="sub"?document.createElement("sub"):m(l.__proto__||Object.getPrototypeOf(l),"create",this).call(this,r)}},{key:"formats",value:function(r){if(r.tagName==="SUB")return"sub";if(r.tagName==="SUP")return"super"}}]),l}(v.default);t.blotName="script",t.tagName=["SUB","SUP"],d.default=t},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(6),m=b(k);function b(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return v(this,t),p(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="strike",o.tagName="S",d.default=o},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=c(6),m=b(k);function b(e){return e&&e.__esModule?e:{default:e}}function v(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t&&(typeof t=="object"||typeof t=="function")?t:e}function s(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var o=function(e){s(t,e);function t(){return v(this,t),p(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return t}(m.default);o.blotName="underline",o.tagName="U",d.default=o},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var _=n.get;return _===void 0?void 0:_.call(f)}},b=c(0),v=s(b),p=c(27);function s(a){return a&&a.__esModule?a:{default:a}}function o(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function e(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function t(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var u=["alt","height","width"],l=function(a){t(r,a);function r(){return o(this,r),e(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return k(r,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=m(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return typeof f=="string"&&n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,h){return f.hasAttribute(h)&&(n[h]=f.getAttribute(h)),n},{})}},{key:"match",value:function(f){return/\.(jpe?g|gif|png)$/.test(f)||/^data:image\/.+;base64/.test(f)}},{key:"sanitize",value:function(f){return(0,p.sanitize)(f,["http","https","data"])?f:"//:0"}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(v.default.Embed);l.blotName="image",l.tagName="IMG",d.default=l},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0});var k=function(){function a(r,i){for(var f=0;f<i.length;f++){var n=i[f];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,n.key,n)}}return function(r,i,f){return i&&a(r.prototype,i),f&&a(r,f),r}}(),m=function a(r,i,f){r===null&&(r=Function.prototype);var n=Object.getOwnPropertyDescriptor(r,i);if(n===void 0){var h=Object.getPrototypeOf(r);return h===null?void 0:a(h,i,f)}else{if("value"in n)return n.value;var _=n.get;return _===void 0?void 0:_.call(f)}},b=c(4),v=c(27),p=s(v);function s(a){return a&&a.__esModule?a:{default:a}}function o(a,r){if(!(a instanceof r))throw new TypeError("Cannot call a class as a function")}function e(a,r){if(!a)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r&&(typeof r=="object"||typeof r=="function")?r:a}function t(a,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof r);a.prototype=Object.create(r&&r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),r&&(Object.setPrototypeOf?Object.setPrototypeOf(a,r):a.__proto__=r)}var u=["height","width"],l=function(a){t(r,a);function r(){return o(this,r),e(this,(r.__proto__||Object.getPrototypeOf(r)).apply(this,arguments))}return k(r,[{key:"format",value:function(f,n){u.indexOf(f)>-1?n?this.domNode.setAttribute(f,n):this.domNode.removeAttribute(f):m(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"format",this).call(this,f,n)}}],[{key:"create",value:function(f){var n=m(r.__proto__||Object.getPrototypeOf(r),"create",this).call(this,f);return n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!0),n.setAttribute("src",this.sanitize(f)),n}},{key:"formats",value:function(f){return u.reduce(function(n,h){return f.hasAttribute(h)&&(n[h]=f.getAttribute(h)),n},{})}},{key:"sanitize",value:function(f){return p.default.sanitize(f)}},{key:"value",value:function(f){return f.getAttribute("src")}}]),r}(b.BlockEmbed);l.blotName="video",l.className="ql-video",l.tagName="IFRAME",d.default=l},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.FormulaBlot=void 0;var k=function(){function f(n,h){for(var _=0;_<h.length;_++){var O=h[_];O.enumerable=O.enumerable||!1,O.configurable=!0,"value"in O&&(O.writable=!0),Object.defineProperty(n,O.key,O)}}return function(n,h,_){return h&&f(n.prototype,h),_&&f(n,_),n}}(),m=function f(n,h,_){n===null&&(n=Function.prototype);var O=Object.getOwnPropertyDescriptor(n,h);if(O===void 0){var w=Object.getPrototypeOf(n);return w===null?void 0:f(w,h,_)}else{if("value"in O)return O.value;var S=O.get;return S===void 0?void 0:S.call(_)}},b=c(35),v=t(b),p=c(5),s=t(p),o=c(9),e=t(o);function t(f){return f&&f.__esModule?f:{default:f}}function u(f,n){if(!(f instanceof n))throw new TypeError("Cannot call a class as a function")}function l(f,n){if(!f)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return n&&(typeof n=="object"||typeof n=="function")?n:f}function a(f,n){if(typeof n!="function"&&n!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof n);f.prototype=Object.create(n&&n.prototype,{constructor:{value:f,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(f,n):f.__proto__=n)}var r=function(f){a(n,f);function n(){return u(this,n),l(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return k(n,null,[{key:"create",value:function(_){var O=m(n.__proto__||Object.getPrototypeOf(n),"create",this).call(this,_);return typeof _=="string"&&(window.katex.render(_,O,{throwOnError:!1,errorColor:"#f00"}),O.setAttribute("data-value",_)),O}},{key:"value",value:function(_){return _.getAttribute("data-value")}}]),n}(v.default);r.blotName="formula",r.className="ql-formula",r.tagName="SPAN";var i=function(f){a(n,f),k(n,null,[{key:"register",value:function(){s.default.register(r,!0)}}]);function n(){u(this,n);var h=l(this,(n.__proto__||Object.getPrototypeOf(n)).call(this));if(window.katex==null)throw new Error("Formula module requires KaTeX.");return h}return n}(e.default);d.FormulaBlot=r,d.default=i},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.CodeToken=d.CodeBlock=void 0;var k=function(){function _(O,w){for(var S=0;S<w.length;S++){var E=w[S];E.enumerable=E.enumerable||!1,E.configurable=!0,"value"in E&&(E.writable=!0),Object.defineProperty(O,E.key,E)}}return function(O,w,S){return w&&_(O.prototype,w),S&&_(O,S),O}}(),m=function _(O,w,S){O===null&&(O=Function.prototype);var E=Object.getOwnPropertyDescriptor(O,w);if(E===void 0){var g=Object.getPrototypeOf(O);return g===null?void 0:_(g,w,S)}else{if("value"in E)return E.value;var A=E.get;return A===void 0?void 0:A.call(S)}},b=c(0),v=l(b),p=c(5),s=l(p),o=c(9),e=l(o),t=c(13),u=l(t);function l(_){return _&&_.__esModule?_:{default:_}}function a(_,O){if(!(_ instanceof O))throw new TypeError("Cannot call a class as a function")}function r(_,O){if(!_)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return O&&(typeof O=="object"||typeof O=="function")?O:_}function i(_,O){if(typeof O!="function"&&O!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof O);_.prototype=Object.create(O&&O.prototype,{constructor:{value:_,enumerable:!1,writable:!0,configurable:!0}}),O&&(Object.setPrototypeOf?Object.setPrototypeOf(_,O):_.__proto__=O)}var f=function(_){i(O,_);function O(){return a(this,O),r(this,(O.__proto__||Object.getPrototypeOf(O)).apply(this,arguments))}return k(O,[{key:"replaceWith",value:function(S){this.domNode.textContent=this.domNode.textContent,this.attach(),m(O.prototype.__proto__||Object.getPrototypeOf(O.prototype),"replaceWith",this).call(this,S)}},{key:"highlight",value:function(S){var E=this.domNode.textContent;this.cachedText!==E&&((E.trim().length>0||this.cachedText==null)&&(this.domNode.innerHTML=S(E),this.domNode.normalize(),this.attach()),this.cachedText=E)}}]),O}(u.default);f.className="ql-syntax";var n=new v.default.Attributor.Class("token","hljs",{scope:v.default.Scope.INLINE}),h=function(_){i(O,_),k(O,null,[{key:"register",value:function(){s.default.register(n,!0),s.default.register(f,!0)}}]);function O(w,S){a(this,O);var E=r(this,(O.__proto__||Object.getPrototypeOf(O)).call(this,w,S));if(typeof E.options.highlight!="function")throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var g=null;return E.quill.on(s.default.events.SCROLL_OPTIMIZE,function(){clearTimeout(g),g=setTimeout(function(){E.highlight(),g=null},E.options.interval)}),E.highlight(),E}return k(O,[{key:"highlight",value:function(){var S=this;if(!this.quill.selection.composing){this.quill.update(s.default.sources.USER);var E=this.quill.getSelection();this.quill.scroll.descendants(f).forEach(function(g){g.highlight(S.options.highlight)}),this.quill.update(s.default.sources.SILENT),E!=null&&this.quill.setSelection(E,s.default.sources.SILENT)}}}]),O}(e.default);h.DEFAULTS={highlight:function(){return window.hljs==null?null:function(_){var O=window.hljs.highlightAuto(_);return O.value}}(),interval:1e3},d.CodeBlock=f,d.CodeToken=n,d.default=h},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(y,d){y.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(y,d){y.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(y,d){y.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(y,d){y.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(y,d){y.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(y,d,c){Object.defineProperty(d,"__esModule",{value:!0}),d.default=d.BubbleTooltip=void 0;var k=function O(w,S,E){w===null&&(w=Function.prototype);var g=Object.getOwnPropertyDescriptor(w,S);if(g===void 0){var A=Object.getPrototypeOf(w);return A===null?void 0:O(A,S,E)}else{if("value"in g)return g.value;var T=g.get;return T===void 0?void 0:T.call(E)}},m=function(){function O(w,S){for(var E=0;E<S.length;E++){var g=S[E];g.enumerable=g.enumerable||!1,g.configurable=!0,"value"in g&&(g.writable=!0),Object.defineProperty(w,g.key,g)}}return function(w,S,E){return S&&O(w.prototype,S),E&&O(w,E),w}}(),b=c(3),v=a(b),p=c(8),s=a(p),o=c(43),e=a(o),t=c(15),u=c(41),l=a(u);function a(O){return O&&O.__esModule?O:{default:O}}function r(O,w){if(!(O instanceof w))throw new TypeError("Cannot call a class as a function")}function i(O,w){if(!O)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return w&&(typeof w=="object"||typeof w=="function")?w:O}function f(O,w){if(typeof w!="function"&&w!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof w);O.prototype=Object.create(w&&w.prototype,{constructor:{value:O,enumerable:!1,writable:!0,configurable:!0}}),w&&(Object.setPrototypeOf?Object.setPrototypeOf(O,w):O.__proto__=w)}var n=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],h=function(O){f(w,O);function w(S,E){r(this,w),E.modules.toolbar!=null&&E.modules.toolbar.container==null&&(E.modules.toolbar.container=n);var g=i(this,(w.__proto__||Object.getPrototypeOf(w)).call(this,S,E));return g.quill.container.classList.add("ql-bubble"),g}return m(w,[{key:"extendToolbar",value:function(E){this.tooltip=new _(this.quill,this.options.bounds),this.tooltip.root.appendChild(E.container),this.buildButtons([].slice.call(E.container.querySelectorAll("button")),l.default),this.buildPickers([].slice.call(E.container.querySelectorAll("select")),l.default)}}]),w}(e.default);h.DEFAULTS=(0,v.default)(!0,{},e.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(w){w?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var _=function(O){f(w,O);function w(S,E){r(this,w);var g=i(this,(w.__proto__||Object.getPrototypeOf(w)).call(this,S,E));return g.quill.on(s.default.events.EDITOR_CHANGE,function(A,T,x,j){if(A===s.default.events.SELECTION_CHANGE)if(T!=null&&T.length>0&&j===s.default.sources.USER){g.show(),g.root.style.left="0px",g.root.style.width="",g.root.style.width=g.root.offsetWidth+"px";var F=g.quill.getLines(T.index,T.length);if(F.length===1)g.position(g.quill.getBounds(T));else{var U=F[F.length-1],W=g.quill.getIndex(U),B=Math.min(U.length()-1,T.index+T.length-W),M=g.quill.getBounds(new t.Range(W,B));g.position(M)}}else document.activeElement!==g.textbox&&g.quill.hasFocus()&&g.hide()}),g}return m(w,[{key:"listen",value:function(){var E=this;k(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",function(){E.root.classList.remove("ql-editing")}),this.quill.on(s.default.events.SCROLL_OPTIMIZE,function(){setTimeout(function(){if(!E.root.classList.contains("ql-hidden")){var g=E.quill.getSelection();g!=null&&E.position(E.quill.getBounds(g))}},1)})}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(E){var g=k(w.prototype.__proto__||Object.getPrototypeOf(w.prototype),"position",this).call(this,E),A=this.root.querySelector(".ql-tooltip-arrow");if(A.style.marginLeft="",g===0)return g;A.style.marginLeft=-1*g-A.offsetWidth/2+"px"}}]),w}(o.BaseTooltip);_.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),d.BubbleTooltip=_,d.default=h},function(y,d,c){y.exports=c(63)}]).default})})(Fe);var ke=We(Fe.exports),ve=-1,de=1,ce=0;function be(P,D,y,d){if(P===D)return P?[[ce,P]]:[];if(y!=null){var c=it(P,D,y);if(c)return c}var k=xe(P,D),m=P.substring(0,k);P=P.substring(k),D=D.substring(k),k=Pe(P,D);var b=P.substring(P.length-k);P=P.substring(0,P.length-k),D=D.substring(0,D.length-k);var v=et(P,D);return m&&v.unshift([ce,m]),b&&v.push([ce,b]),Ue(v,d),v}function et(P,D){var y;if(!P)return[[de,D]];if(!D)return[[ve,P]];var d=P.length>D.length?P:D,c=P.length>D.length?D:P,k=d.indexOf(c);if(k!==-1)return y=[[de,d.substring(0,k)],[ce,c],[de,d.substring(k+c.length)]],P.length>D.length&&(y[0][0]=y[2][0]=ve),y;if(c.length===1)return[[ve,P],[de,D]];var m=nt(P,D);if(m){var b=m[0],v=m[1],p=m[2],s=m[3],o=m[4],e=be(b,p),t=be(v,s);return e.concat([[ce,o]],t)}return tt(P,D)}function tt(P,D){for(var y=P.length,d=D.length,c=Math.ceil((y+d)/2),k=c,m=2*c,b=new Array(m),v=new Array(m),p=0;p<m;p++)b[p]=-1,v[p]=-1;b[k+1]=0,v[k+1]=0;for(var s=y-d,o=s%2!==0,e=0,t=0,u=0,l=0,a=0;a<c;a++){for(var r=-a+e;r<=a-t;r+=2){var i=k+r,f;r===-a||r!==a&&b[i-1]<b[i+1]?f=b[i+1]:f=b[i-1]+1;for(var n=f-r;f<y&&n<d&&P.charAt(f)===D.charAt(n);)f++,n++;if(b[i]=f,f>y)t+=2;else if(n>d)e+=2;else if(o){var h=k+s-r;if(h>=0&&h<m&&v[h]!==-1){var _=y-v[h];if(f>=_)return Me(P,D,f,n)}}}for(var O=-a+u;O<=a-l;O+=2){var h=k+O,_;O===-a||O!==a&&v[h-1]<v[h+1]?_=v[h+1]:_=v[h-1]+1;for(var w=_-O;_<y&&w<d&&P.charAt(y-_-1)===D.charAt(d-w-1);)_++,w++;if(v[h]=_,_>y)l+=2;else if(w>d)u+=2;else if(!o){var i=k+s-O;if(i>=0&&i<m&&b[i]!==-1){var f=b[i],n=k+f-i;if(_=y-_,f>=_)return Me(P,D,f,n)}}}}return[[ve,P],[de,D]]}function Me(P,D,y,d){var c=P.substring(0,y),k=D.substring(0,d),m=P.substring(y),b=D.substring(d),v=be(c,k),p=be(m,b);return v.concat(p)}function xe(P,D){if(!P||!D||P.charAt(0)!==D.charAt(0))return 0;for(var y=0,d=Math.min(P.length,D.length),c=d,k=0;y<c;)P.substring(k,c)==D.substring(k,c)?(y=c,k=y):d=c,c=Math.floor((d-y)/2+y);return He(P.charCodeAt(c-1))&&c--,c}function Pe(P,D){if(!P||!D||P.slice(-1)!==D.slice(-1))return 0;for(var y=0,d=Math.min(P.length,D.length),c=d,k=0;y<c;)P.substring(P.length-c,P.length-k)==D.substring(D.length-c,D.length-k)?(y=c,k=y):d=c,c=Math.floor((d-y)/2+y);return ze(P.charCodeAt(P.length-c))&&c--,c}function nt(P,D){var y=P.length>D.length?P:D,d=P.length>D.length?D:P;if(y.length<4||d.length*2<y.length)return null;function c(t,u,l){for(var a=t.substring(l,l+Math.floor(t.length/4)),r=-1,i="",f,n,h,_;(r=u.indexOf(a,r+1))!==-1;){var O=xe(t.substring(l),u.substring(r)),w=Pe(t.substring(0,l),u.substring(0,r));i.length<w+O&&(i=u.substring(r-w,r)+u.substring(r,r+O),f=t.substring(0,l-w),n=t.substring(l+O),h=u.substring(0,r-w),_=u.substring(r+O))}return i.length*2>=t.length?[f,n,h,_,i]:null}var k=c(y,d,Math.ceil(y.length/4)),m=c(y,d,Math.ceil(y.length/2)),b;if(!k&&!m)return null;m?k?b=k[4].length>m[4].length?k:m:b=m:b=k;var v,p,s,o;P.length>D.length?(v=b[0],p=b[1],s=b[2],o=b[3]):(s=b[0],o=b[1],v=b[2],p=b[3]);var e=b[4];return[v,p,s,o,e]}function Ue(P,D){P.push([ce,""]);for(var y=0,d=0,c=0,k="",m="",b;y<P.length;){if(y<P.length-1&&!P[y][1]){P.splice(y,1);continue}switch(P[y][0]){case de:c++,m+=P[y][1],y++;break;case ve:d++,k+=P[y][1],y++;break;case ce:var v=y-c-d-1;if(D){if(v>=0&&Ve(P[v][1])){var p=P[v][1].slice(-1);if(P[v][1]=P[v][1].slice(0,-1),k=p+k,m=p+m,!P[v][1]){P.splice(v,1),y--;var s=v-1;P[s]&&P[s][0]===de&&(c++,m=P[s][1]+m,s--),P[s]&&P[s][0]===ve&&(d++,k=P[s][1]+k,s--),v=s}}if(Ke(P[y][1])){var p=P[y][1].charAt(0);P[y][1]=P[y][1].slice(1),k+=p,m+=p}}if(y<P.length-1&&!P[y][1]){P.splice(y,1);break}if(k.length>0||m.length>0){k.length>0&&m.length>0&&(b=xe(m,k),b!==0&&(v>=0?P[v][1]+=m.substring(0,b):(P.splice(0,0,[ce,m.substring(0,b)]),y++),m=m.substring(b),k=k.substring(b)),b=Pe(m,k),b!==0&&(P[y][1]=m.substring(m.length-b)+P[y][1],m=m.substring(0,m.length-b),k=k.substring(0,k.length-b)));var o=c+d;k.length===0&&m.length===0?(P.splice(y-o,o),y=y-o):k.length===0?(P.splice(y-o,o,[de,m]),y=y-o+1):m.length===0?(P.splice(y-o,o,[ve,k]),y=y-o+1):(P.splice(y-o,o,[ve,k],[de,m]),y=y-o+2)}y!==0&&P[y-1][0]===ce?(P[y-1][1]+=P[y][1],P.splice(y,1)):y++,c=0,d=0,k="",m="";break}}P[P.length-1][1]===""&&P.pop();var e=!1;for(y=1;y<P.length-1;)P[y-1][0]===ce&&P[y+1][0]===ce&&(P[y][1].substring(P[y][1].length-P[y-1][1].length)===P[y-1][1]?(P[y][1]=P[y-1][1]+P[y][1].substring(0,P[y][1].length-P[y-1][1].length),P[y+1][1]=P[y-1][1]+P[y+1][1],P.splice(y-1,1),e=!0):P[y][1].substring(0,P[y+1][1].length)==P[y+1][1]&&(P[y-1][1]+=P[y+1][1],P[y][1]=P[y][1].substring(P[y+1][1].length)+P[y+1][1],P.splice(y+1,1),e=!0)),y++;e&&Ue(P,D)}function He(P){return P>=55296&&P<=56319}function ze(P){return P>=56320&&P<=57343}function Ke(P){return ze(P.charCodeAt(0))}function Ve(P){return He(P.charCodeAt(P.length-1))}function rt(P){for(var D=[],y=0;y<P.length;y++)P[y][1].length>0&&D.push(P[y]);return D}function we(P,D,y,d){return Ve(P)||Ke(d)?null:rt([[ce,P],[ve,D],[de,y],[ce,d]])}function it(P,D,y){var d=typeof y=="number"?{index:y,length:0}:y.oldRange,c=typeof y=="number"?null:y.newRange,k=P.length,m=D.length;if(d.length===0&&(c===null||c.length===0)){var b=d.index,v=P.slice(0,b),p=P.slice(b),s=c?c.index:null;e:{var o=b+m-k;if(s!==null&&s!==o||o<0||o>m)break e;var e=D.slice(0,o),t=D.slice(o);if(t!==p)break e;var u=Math.min(b,o),l=v.slice(0,u),a=e.slice(0,u);if(l!==a)break e;var r=v.slice(u),i=e.slice(u);return we(l,r,i,p)}e:{if(s!==null&&s!==b)break e;var f=b,e=D.slice(0,f),t=D.slice(f);if(e!==v)break e;var n=Math.min(k-f,m-f),h=p.slice(p.length-n),_=t.slice(t.length-n);if(h!==_)break e;var r=p.slice(0,p.length-n),i=t.slice(0,t.length-n);return we(v,r,i,h)}}if(d.length>0&&c&&c.length===0){e:{var l=P.slice(0,d.index),h=P.slice(d.index+d.length),u=l.length,n=h.length;if(m<u+n)break e;var a=D.slice(0,u),_=D.slice(m-n);if(l!==a||h!==_)break e;var r=P.slice(u,k-n),i=D.slice(u,m-n);return we(l,r,i,h)}}return null}function Ee(P,D,y){return be(P,D,y,!0)}Ee.INSERT=de;Ee.DELETE=ve;Ee.EQUAL=ce;var ot=Ee,Le={},$e=ye&&ye.__importDefault||function(P){return P&&P.__esModule?P:{default:P}};Object.defineProperty(Le,"__esModule",{value:!0});var lt=$e(Be.exports),at=$e(Ce.exports),Te;(function(P){function D(k,m,b){k===void 0&&(k={}),m===void 0&&(m={}),typeof k!="object"&&(k={}),typeof m!="object"&&(m={});var v=lt.default(m);b||(v=Object.keys(v).reduce(function(s,o){return v[o]!=null&&(s[o]=v[o]),s},{}));for(var p in k)k[p]!==void 0&&m[p]===void 0&&(v[p]=k[p]);return Object.keys(v).length>0?v:void 0}P.compose=D;function y(k,m){k===void 0&&(k={}),m===void 0&&(m={}),typeof k!="object"&&(k={}),typeof m!="object"&&(m={});var b=Object.keys(k).concat(Object.keys(m)).reduce(function(v,p){return at.default(k[p],m[p])||(v[p]=m[p]===void 0?null:m[p]),v},{});return Object.keys(b).length>0?b:void 0}P.diff=y;function d(k,m){k===void 0&&(k={}),m===void 0&&(m={}),k=k||{};var b=Object.keys(m).reduce(function(v,p){return m[p]!==k[p]&&k[p]!==void 0&&(v[p]=m[p]),v},{});return Object.keys(k).reduce(function(v,p){return k[p]!==m[p]&&m[p]===void 0&&(v[p]=null),v},b)}P.invert=d;function c(k,m,b){if(b===void 0&&(b=!1),typeof k!="object")return m;if(typeof m=="object"){if(!b)return m;var v=Object.keys(m).reduce(function(p,s){return k[s]===void 0&&(p[s]=m[s]),p},{});return Object.keys(v).length>0?v:void 0}}P.transform=c})(Te||(Te={}));Le.default=Te;var Ae={},qe={},ut=ye&&ye.__importDefault||function(P){return P&&P.__esModule?P:{default:P}};Object.defineProperty(qe,"__esModule",{value:!0});var Ie=ut(Ae),ft=function(){function P(D){this.ops=D,this.index=0,this.offset=0}return P.prototype.hasNext=function(){return this.peekLength()<1/0},P.prototype.next=function(D){D||(D=1/0);var y=this.ops[this.index];if(y){var d=this.offset,c=Ie.default.length(y);if(D>=c-d?(D=c-d,this.index+=1,this.offset=0):this.offset+=D,typeof y.delete=="number")return{delete:D};var k={};return y.attributes&&(k.attributes=y.attributes),typeof y.retain=="number"?k.retain=D:typeof y.insert=="string"?k.insert=y.insert.substr(d,D):k.insert=y.insert,k}else return{retain:1/0}},P.prototype.peek=function(){return this.ops[this.index]},P.prototype.peekLength=function(){return this.ops[this.index]?Ie.default.length(this.ops[this.index])-this.offset:1/0},P.prototype.peekType=function(){return this.ops[this.index]?typeof this.ops[this.index].delete=="number"?"delete":typeof this.ops[this.index].retain=="number"?"retain":"insert":"retain"},P.prototype.rest=function(){if(this.hasNext()){if(this.offset===0)return this.ops.slice(this.index);var D=this.offset,y=this.index,d=this.next(),c=this.ops.slice(this.index);return this.offset=D,this.index=y,[d].concat(c)}else return[]},P}();qe.default=ft;var st=ye&&ye.__importDefault||function(P){return P&&P.__esModule?P:{default:P}};Object.defineProperty(Ae,"__esModule",{value:!0});var ct=st(qe),Se;(function(P){function D(d){return new ct.default(d)}P.iterator=D;function y(d){return typeof d.delete=="number"?d.delete:typeof d.retain=="number"?d.retain:typeof d.insert=="string"?d.insert.length:1}P.length=y})(Se||(Se={}));Ae.default=Se;var _e=ye&&ye.__importDefault||function(P){return P&&P.__esModule?P:{default:P}},Oe=_e(ot),ht=_e(Be.exports),Ne=_e(Ce.exports),me=_e(Le),fe=_e(Ae),dt=String.fromCharCode(0);(function(){function P(D){Array.isArray(D)?this.ops=D:D!=null&&Array.isArray(D.ops)?this.ops=D.ops:this.ops=[]}return P.prototype.insert=function(D,y){var d={};return typeof D=="string"&&D.length===0?this:(d.insert=D,y!=null&&typeof y=="object"&&Object.keys(y).length>0&&(d.attributes=y),this.push(d))},P.prototype.delete=function(D){return D<=0?this:this.push({delete:D})},P.prototype.retain=function(D,y){if(D<=0)return this;var d={retain:D};return y!=null&&typeof y=="object"&&Object.keys(y).length>0&&(d.attributes=y),this.push(d)},P.prototype.push=function(D){var y=this.ops.length,d=this.ops[y-1];if(D=ht.default(D),typeof d=="object"){if(typeof D.delete=="number"&&typeof d.delete=="number")return this.ops[y-1]={delete:d.delete+D.delete},this;if(typeof d.delete=="number"&&D.insert!=null&&(y-=1,d=this.ops[y-1],typeof d!="object"))return this.ops.unshift(D),this;if(Ne.default(D.attributes,d.attributes)){if(typeof D.insert=="string"&&typeof d.insert=="string")return this.ops[y-1]={insert:d.insert+D.insert},typeof D.attributes=="object"&&(this.ops[y-1].attributes=D.attributes),this;if(typeof D.retain=="number"&&typeof d.retain=="number")return this.ops[y-1]={retain:d.retain+D.retain},typeof D.attributes=="object"&&(this.ops[y-1].attributes=D.attributes),this}}return y===this.ops.length?this.ops.push(D):this.ops.splice(y,0,D),this},P.prototype.chop=function(){var D=this.ops[this.ops.length-1];return D&&D.retain&&!D.attributes&&this.ops.pop(),this},P.prototype.filter=function(D){return this.ops.filter(D)},P.prototype.forEach=function(D){this.ops.forEach(D)},P.prototype.map=function(D){return this.ops.map(D)},P.prototype.partition=function(D){var y=[],d=[];return this.forEach(function(c){var k=D(c)?y:d;k.push(c)}),[y,d]},P.prototype.reduce=function(D,y){return this.ops.reduce(D,y)},P.prototype.changeLength=function(){return this.reduce(function(D,y){return y.insert?D+fe.default.length(y):y.delete?D-y.delete:D},0)},P.prototype.length=function(){return this.reduce(function(D,y){return D+fe.default.length(y)},0)},P.prototype.slice=function(D,y){D===void 0&&(D=0),y===void 0&&(y=1/0);for(var d=[],c=fe.default.iterator(this.ops),k=0;k<y&&c.hasNext();){var m=void 0;k<D?m=c.next(D-k):(m=c.next(y-k),d.push(m)),k+=fe.default.length(m)}return new P(d)},P.prototype.compose=function(D){var y=fe.default.iterator(this.ops),d=fe.default.iterator(D.ops),c=[],k=d.peek();if(k!=null&&typeof k.retain=="number"&&k.attributes==null){for(var m=k.retain;y.peekType()==="insert"&&y.peekLength()<=m;)m-=y.peekLength(),c.push(y.next());k.retain-m>0&&d.next(k.retain-m)}for(var b=new P(c);y.hasNext()||d.hasNext();)if(d.peekType()==="insert")b.push(d.next());else if(y.peekType()==="delete")b.push(y.next());else{var v=Math.min(y.peekLength(),d.peekLength()),p=y.next(v),s=d.next(v);if(typeof s.retain=="number"){var o={};typeof p.retain=="number"?o.retain=v:o.insert=p.insert;var e=me.default.compose(p.attributes,s.attributes,typeof p.retain=="number");if(e&&(o.attributes=e),b.push(o),!d.hasNext()&&Ne.default(b.ops[b.ops.length-1],o)){var t=new P(y.rest());return b.concat(t).chop()}}else typeof s.delete=="number"&&typeof p.retain=="number"&&b.push(s)}return b.chop()},P.prototype.concat=function(D){var y=new P(this.ops.slice());return D.ops.length>0&&(y.push(D.ops[0]),y.ops=y.ops.concat(D.ops.slice(1))),y},P.prototype.diff=function(D,y){if(this.ops===D.ops)return new P;var d=[this,D].map(function(v){return v.map(function(p){if(p.insert!=null)return typeof p.insert=="string"?p.insert:dt;var s=v===D?"on":"with";throw new Error("diff() called "+s+" non-document")}).join("")}),c=new P,k=Oe.default(d[0],d[1],y),m=fe.default.iterator(this.ops),b=fe.default.iterator(D.ops);return k.forEach(function(v){for(var p=v[1].length;p>0;){var s=0;switch(v[0]){case Oe.default.INSERT:s=Math.min(b.peekLength(),p),c.push(b.next(s));break;case Oe.default.DELETE:s=Math.min(p,m.peekLength()),m.next(s),c.delete(s);break;case Oe.default.EQUAL:s=Math.min(m.peekLength(),b.peekLength(),p);var o=m.next(s),e=b.next(s);Ne.default(o.insert,e.insert)?c.retain(s,me.default.diff(o.attributes,e.attributes)):c.push(e).delete(s);break}p-=s}}),c.chop()},P.prototype.eachLine=function(D,y){y===void 0&&(y=`
`);for(var d=fe.default.iterator(this.ops),c=new P,k=0;d.hasNext();){if(d.peekType()!=="insert")return;var m=d.peek(),b=fe.default.length(m)-d.peekLength(),v=typeof m.insert=="string"?m.insert.indexOf(y,b)-b:-1;if(v<0)c.push(d.next());else if(v>0)c.push(d.next(v));else{if(D(c,d.next(1).attributes||{},k)===!1)return;k+=1,c=new P}}c.length()>0&&D(c,{},k)},P.prototype.invert=function(D){var y=new P;return this.reduce(function(d,c){if(c.insert)y.delete(fe.default.length(c));else{if(c.retain&&c.attributes==null)return y.retain(c.retain),d+c.retain;if(c.delete||c.retain&&c.attributes){var k=c.delete||c.retain,m=D.slice(d,d+k);return m.forEach(function(b){c.delete?y.push(b):c.retain&&c.attributes&&y.retain(fe.default.length(b),me.default.invert(c.attributes,b.attributes))}),d+k}}return d},0),y.chop()},P.prototype.transform=function(D,y){if(y===void 0&&(y=!1),y=!!y,typeof D=="number")return this.transformPosition(D,y);for(var d=D,c=fe.default.iterator(this.ops),k=fe.default.iterator(d.ops),m=new P;c.hasNext()||k.hasNext();)if(c.peekType()==="insert"&&(y||k.peekType()!=="insert"))m.retain(fe.default.length(c.next()));else if(k.peekType()==="insert")m.push(k.next());else{var b=Math.min(c.peekLength(),k.peekLength()),v=c.next(b),p=k.next(b);if(v.delete)continue;p.delete?m.push(p):m.retain(b,me.default.transform(v.attributes,p.attributes,y))}return m.chop()},P.prototype.transformPosition=function(D,y){y===void 0&&(y=!1),y=!!y;for(var d=fe.default.iterator(this.ops),c=0;d.hasNext()&&c<=D;){var k=d.peekLength(),m=d.peekType();if(d.next(),m==="delete"){D-=Math.min(k,D-c);continue}else m==="insert"&&(c<D||!y)&&(D+=k);c+=k}return D},P.Op=fe.default,P.AttributeMap=me.default,P})();/*!
 * VueQuill @vueup/vue-quill v1.0.0-beta.9
 * https://vueup.github.io/vue-quill/
 * 
 * Includes quill v1.3.7
 * https://quilljs.com/
 * 
 * Copyright (c) 2022 Ahmad Luthfi Masruri
 * Released under the MIT license
 * Date: 2022-07-15T12:19:26.189Z
 */const De={essential:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}],["blockquote","code-block","link"],[{color:[]},"clean"]],minimal:[[{header:1},{header:2}],["bold","italic","underline"],[{list:"ordered"},{list:"bullet"},{align:[]}]],full:[["bold","italic","underline","strike"],["blockquote","code-block"],[{header:1},{header:2}],[{list:"ordered"},{list:"bullet"}],[{script:"sub"},{script:"super"}],[{indent:"-1"},{indent:"+1"}],[{direction:"rtl"}],[{size:["small",!1,"large","huge"]}],[{header:[1,2,3,4,5,6,!1]}],[{color:[]},{background:[]}],[{font:[]}],[{align:[]}],["link","video","image"],["clean"]]},pt=Ge({name:"QuillEditor",inheritAttrs:!1,props:{content:{type:[String,Object],default:()=>{}},contentType:{type:String,default:"delta",validator:P=>["delta","html","text"].includes(P)},enable:{type:Boolean,default:!0},readOnly:{type:Boolean,default:!1},placeholder:{type:String,required:!1},theme:{type:String,default:"snow",validator:P=>["snow","bubble",""].includes(P)},toolbar:{type:[String,Array,Object],required:!1,validator:P=>typeof P=="string"&&P!==""?P.charAt(0)==="#"?!0:Object.keys(De).indexOf(P)!==-1:!0},modules:{type:Object,required:!1},options:{type:Object,required:!1},globalOptions:{type:Object,required:!1}},emits:["textChange","selectionChange","editorChange","update:content","focus","blur","ready"],setup:(P,D)=>{Ye(()=>{k()}),Qe(()=>{y=null});let y,d;const c=Re(),k=()=>{var _;if(!!c.value){if(d=m(),P.modules)if(Array.isArray(P.modules))for(const O of P.modules)ke.register(`modules/${O.name}`,O.module);else ke.register(`modules/${P.modules.name}`,P.modules.module);y=new ke(c.value,d),l(P.content),y.on("text-change",b),y.on("selection-change",p),y.on("editor-change",s),P.theme!=="bubble"&&c.value.classList.remove("ql-bubble"),P.theme!=="snow"&&c.value.classList.remove("ql-snow"),(_=y.getModule("toolbar"))===null||_===void 0||_.container.addEventListener("mousedown",O=>{O.preventDefault()}),D.emit("ready",y)}},m=()=>{const _={};if(P.theme!==""&&(_.theme=P.theme),P.readOnly&&(_.readOnly=P.readOnly),P.placeholder&&(_.placeholder=P.placeholder),P.toolbar&&P.toolbar!==""&&(_.modules={toolbar:(()=>{if(typeof P.toolbar=="object")return P.toolbar;if(typeof P.toolbar=="string")return P.toolbar.charAt(0)==="#"?P.toolbar:De[P.toolbar]})()}),P.modules){const O=(()=>{var w,S;const E={};if(Array.isArray(P.modules))for(const g of P.modules)E[g.name]=(w=g.options)!==null&&w!==void 0?w:{};else E[P.modules.name]=(S=P.modules.options)!==null&&S!==void 0?S:{};return E})();_.modules=Object.assign({},_.modules,O)}return Object.assign({},P.globalOptions,P.options,_)},b=(_,O,w)=>{D.emit("update:content",u()),D.emit("textChange",{delta:_,oldContents:O,source:w})},v=Re(),p=(_,O,w)=>{v.value=!!(y!=null&&y.hasFocus()),D.emit("selectionChange",{range:_,oldRange:O,source:w})};je(v,_=>{_?D.emit("focus",c):D.emit("blur",c)});const s=(..._)=>{_[0]==="text-change"&&D.emit("editorChange",{name:_[0],delta:_[1],oldContents:_[2],source:_[3]}),_[0]==="selection-change"&&D.emit("editorChange",{name:_[0],range:_[1],oldRange:_[2],source:_[3]})},o=()=>c.value,e=()=>{var _;return(_=y==null?void 0:y.getModule("toolbar"))===null||_===void 0?void 0:_.container},t=()=>{if(y)return y;throw`The quill editor hasn't been instantiated yet, 
                  make sure to call this method when the editor ready
                  or use v-on:ready="onReady(quill)" event instead.`},u=(_,O)=>P.contentType==="html"?i():P.contentType==="text"?a(_,O):y==null?void 0:y.getContents(_,O),l=(_,O="api")=>{P.contentType==="html"?f(_):P.contentType==="text"?r(_,O):y==null||y.setContents(_,O)},a=(_,O)=>{var w;return(w=y==null?void 0:y.getText(_,O))!==null&&w!==void 0?w:""},r=(_,O="api")=>{y==null||y.setText(_,O)},i=()=>{var _;return(_=y==null?void 0:y.root.innerHTML)!==null&&_!==void 0?_:""},f=_=>{y&&(y.root.innerHTML=_)},n=(_,O="api")=>{const w=y==null?void 0:y.clipboard.convert(_);w&&(y==null||y.setContents(w,O))},h=()=>{Je(()=>{var _;!D.slots.toolbar&&y&&((_=y.getModule("toolbar"))===null||_===void 0||_.container.remove()),k()})};return je(()=>P.enable,_=>{y&&y.enable(_)}),{editor:c,getEditor:o,getToolbar:e,getQuill:t,getContents:u,setContents:l,getHTML:i,setHTML:f,pasteHTML:n,getText:a,setText:r,reinit:h}},render(){var P,D;return[(D=(P=this.$slots).toolbar)===null||D===void 0?void 0:D.call(P),Xe("div",{ref:"editor",...this.$attrs})]}});export{pt as Q};
