<template>
<Head title="Edit Category" />
<div class="p-2 sm:p-4">
    <div class="mb-2 flex justify-start max-w-2xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('categories')">{{ trans('CategoriesList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
        {{ form.name }}
      </h2>
    </div>
    <div class="bg-white rounded-md shadow overflow-hidden max-w-2xl">
      <trashed-message v-if="category.deleted_at" class="mb-6" @restore="restore">
        {{ trans('This') + ' ' +  trans('Category') + ' ' + trans('HasBeenDeleted')}}
      </trashed-message>
      <form @submit.prevent="update">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input  v-model="form.name" :error="form.errors.name" type="text" class="pr-6 pb-8 w-full" direction="ltr" label="Name"  :autofocus="true"/>
          <select-input v-model="form.default_device" :error="form.errors.default_device "  class="pr-6 pb-4 w-full" direction="ltr" label="DefaultDevice">
            <option value="null" hidden disabled>{{trans("SelectDefaultDevice")}}</option>
            <option v-for="device in ourDevices" :key="device.id" :value="device.name">{{ device.name }}</option>
          </select-input>
        </div>
        <div class="px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
          <loading-button :loading="form.processing" class="btn-green" type="submit">{{trans('Update') + ' ' + trans('Category')}}</loading-button>
          <button v-if="!category.deleted_at" class="text-red-600 hover:underline" tabindex="-1" type="button" @click="destroy">{{trans('Delete') + ' ' + trans('Category')}}</button>
        </div>
      </form>
    </div>

    <div class="mt-8 shadow-sm bg-yellow-500 w-40 px-4 py-1 text-gray-50 max-w-sm rounded-sm">
        <span>{{trans('RelatedTests') }}</span>
    </div>

    <div class="shadow overflow-x-auto rounded-lg bg-white max-w-2xl mt-2">
      <table class="w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-green-500">
            
            <th class="px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('Sequence')}}
            </th>

            <th class="px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('ShortName')}}
            </th>

            <th class="px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('FullName')}}
            </th>
            
            <th class="px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('Edit')}}	
            </th>
          </tr>
        </thead>

        <tr v-for="test in category.tests" :key="test.id" class="hover:bg-gray-100 focus-within:bg-gray-100">
          
          <td class="border-t p-0 max-w-fit">
            <div class="px-6 py-2 max-w-fit">
                <input class="form-input" @change="submitSequenceForm(test.id, test.sequence)" type="number" v-model.number="test.sequence"> 
            </div>
          </td>

          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">
                {{ test.short_name }}
            </div>
            </Link>
          </td>


          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">
                {{ test.full_name }}
            </div>
            </Link>
          </td>
          
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">
              <icon name="cheveron-right" class="block w-6 h-6 fill-gray-400" />
            </div>
            </Link>
          </td>
        </tr>
        <tr v-if="category.tests.length === 0">
          <td class="border-t p-0 px-6 py-2" colspan="4">No tests found.</td>
        </tr>
      </table>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'
import { reactive } from '@vue/reactivity'

const props = defineProps({
  locale: String,
    category: Object,
    devices: Array,
});

let ourDevices = reactive(props.devices);

let form = useForm({
    _method: 'put',
    name: props.category.name,
    default_device: props.category.default_device,
});

let sequenceForm = useForm({
    _method: 'put',
    sequence: null,
});

let submitSequenceForm = (test_id, sequence) => {
  sequenceForm.sequence = sequence;

  sequenceForm.put(route('tests.updateTestSequence', test_id), {
    preserveScroll: true,
    onSuccess: () => sequenceForm.reset(),
  })
};

let update = () => {
    form.post(route('categories.update', props.category.id), {
    onSuccess: () => form.reset('name'),
    })
};

let destroy = () => {
    if (confirm('Are you sure you want to delete this category?')) {
    Inertia.delete(route('categories.destroy', props.category.id))
    }
};

let restore = () => {
    if (confirm('Are you sure you want to restore this category?')) {
    Inertia.put(route('categories.restore', props.category.id))
    }
};

</script>