import n from"./ApiTokenManager.724b7df6.js";import{A as r}from"./AppLayout.14f8c8f6.js";import{_ as p}from"./plugin-vue_export-helper.21dcd24c.js";import{n as m,r as s,o as l,y as d,B as t,a as e,b as c}from"./app.5bf25e6f.js";import"./ActionMessage.eaf60617.js";import"./DialogModal.3b39e601.js";import"./InputError.2a9befad.js";import"./Button.de389ba7.js";import"./DangerButton.177534fb.js";import"./FormSection.3de6ac09.js";import"./Input.f95445aa.js";import"./Checkbox.c93fed4a.js";import"./Label.a34a8f2d.js";import"./SecondaryButton.fcd49457.js";import"./SectionBorder.aab76ee9.js";const _=m({props:["tokens","availablePermissions","defaultPermissions"],components:{ApiTokenManager:n,AppLayout:r}}),f=e("h2",{class:"font-semibold text-xl text-gray-800 leading-tight"}," API Tokens ",-1),u={class:"max-w-7xl mx-auto py-10 sm:px-6 lg:px-8"};function k(o,x,v,g,h,b){const a=s("api-token-manager"),i=s("app-layout");return l(),d(i,{title:"API Tokens"},{header:t(()=>[f]),default:t(()=>[e("div",null,[e("div",u,[c(a,{tokens:o.tokens,"available-permissions":o.availablePermissions,"default-permissions":o.defaultPermissions},null,8,["tokens","available-permissions","default-permissions"])])])]),_:1})}var z=p(_,[["render",k]]);export{z as default};
