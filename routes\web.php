<?php

// use Illuminate\Foundation\Application;
// use Illuminate\Support\Facades\Route;
// use Inertia\Inertia;

// Route::get('/', function () {
//     return Inertia::render('Welcome', [
//         'canLogin' => Route::has('login'),
//         'canRegister' => Route::has('register'),
//         'laravelVersion' => Application::VERSION,
//         'phpVersion' => PHP_VERSION,
//     ]);
// });

// Route::middleware([
//     'auth:sanctum',
//     config('jetstream.auth_session'),
//     'verified',
// ])->group(function () {
//     Route::get('/dashboard', function () {
//         return Inertia::render('Dashboard');
//     })->name('dashboard');
// });

use Illuminate\Foundation\Application;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

use App\Http\Controllers\DeviceController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\OfferController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\DoctorController;
use App\Http\Controllers\LabController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\VisitController;
use App\Http\Controllers\PatientVisit;
use App\Http\Controllers\ResultController;
use App\Http\Controllers\FlagController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\StatisticController;
use App\Http\Controllers\UsersController;
use App\Http\Controllers\BuckupController;
use App\Http\Controllers\TransferController;
use App\Http\Controllers\ResearchController;
use App\Http\Controllers\TrackingTagController;

//Route::get('mov', [TransferController::class, 'transferOfferVisit']);

// Locale
Route::get('locale/{locale}', function ($locale) {
    Session()->put('locale', $locale);
    return redirect()->back();
})->name('set_locale_language');

Route::middleware(['auth:sanctum', 'verified'])->get('/', function () {
    return Inertia::render('Dashboard');
});

Route::middleware(['auth:sanctum', 'verified'])->get('/dashboard', function () {
    return Inertia::render('Dashboard');
})->name('dashboard');


// Users
Route::get('users', [UsersController::class, 'index'])->name('users')->middleware('auth')->can('manage-users');
Route::get('users/create', [UsersController::class, 'create'])->name('users.create')->middleware('auth')->can('manage-users');
Route::post('users', [UsersController::class, 'store'])->name('users.store')->middleware('auth')->can('manage-users');
Route::get('users/{user}/edit', [UsersController::class, 'edit'])->name('users.edit')->middleware('auth')->can('manage-users');
Route::put('users/{user}', [UsersController::class, 'update'])->name('users.update')->middleware('auth')->can('manage-users');
Route::delete('users/{user}', [UsersController::class, 'destroy'])->name('users.destroy')->middleware('auth')->can('manage-users');
Route::put('users/{user}/restore', [UsersController::class, 'restore'])->name('users.restore')->middleware('auth')->can('manage-users');

// Categories
Route::get('categories', [CategoryController::class, 'index'])->name('categories')->middleware('auth')->can('manage-reception');
Route::get('categories/create', [CategoryController::class, 'create'])->name('categories.create')->middleware('auth')->can('manage-reception');
Route::post('categories', [CategoryController::class, 'store'])->name('categories.store')->middleware('auth')->can('manage-reception');
Route::get('categories/{category}/edit', [CategoryController::class, 'edit'])->name('categories.edit')->middleware('auth')->can('manage-reception');
Route::put('categories/{category}', [CategoryController::class, 'update'])->name('categories.update')->middleware('auth')->can('manage-reception');
Route::delete('categories/{category}', [CategoryController::class, 'destroy'])->name('categories.destroy')->middleware('auth')->can('manage-reception');
Route::put('categories/{category}/restore', [CategoryController::class, 'restore'])->name('categories.restore')->middleware('auth')->can('manage-reception');


Route::put('tests/{test}/updateTestSequence', [CategoryController::class, 'updateTestSequence'])->name('tests.updateTestSequence')->middleware('auth')->can('manage-reception');



// Devices
Route::get('devices', [DeviceController::class, 'index'])->name('devices')->middleware('auth')->can('manage-reception');
Route::get('devices/create', [DeviceController::class, 'create'])->name('devices.create')->middleware('auth')->can('manage-reception');
Route::post('devices', [DeviceController::class, 'store'])->name('devices.store')->middleware('auth');
Route::get('devices/{device}/edit', [DeviceController::class, 'edit'])->name('devices.edit')->middleware('auth')->can('manage-reception');
Route::put('devices/{device}', [DeviceController::class, 'update'])->name('devices.update')->middleware('auth')->can('manage-reception');
Route::delete('devices/{device}', [DeviceController::class, 'destroy'])->name('devices.destroy')->middleware('auth')->can('manage-reception');
Route::put('devices/{device}/restore', [DeviceController::class, 'restore'])->name('devices.restore')->middleware('auth')->can('manage-reception');

// Results
Route::get('results', [ResultController::class, 'index'])->name('results')->middleware('auth')->can('manage-reception');
Route::get('results/create', [ResultController::class, 'create'])->name('results.create')->middleware('auth')->can('manage-reception');
Route::post('results', [ResultController::class, 'store'])->name('results.store')->middleware('auth')->can('manage-reception');
Route::get('results/{result}/edit', [ResultController::class, 'edit'])->name('results.edit')->middleware('auth')->can('manage-reception');
Route::put('results/{result}', [ResultController::class, 'update'])->name('results.update')->middleware('auth')->can('manage-reception');
Route::delete('results/{result}', [ResultController::class, 'destroy'])->name('results.destroy')->middleware('auth')->can('manage-reception');
Route::put('results/{result}/restore', [ResultController::class, 'restore'])->name('results.restore')->middleware('auth')->can('manage-reception');

// Flags
Route::get('flags', [FlagController::class, 'index'])->name('flags')->middleware('auth')->can('manage-reception');
Route::get('flags/create', [FlagController::class, 'create'])->name('flags.create')->middleware('auth')->can('manage-reception');
Route::post('flags', [FlagController::class, 'store'])->name('flags.store')->middleware('auth');
Route::get('flags/{flag}/edit', [FlagController::class, 'edit'])->name('flags.edit')->middleware('auth')->can('manage-reception');
Route::put('flags/{flag}', [FlagController::class, 'update'])->name('flags.update')->middleware('auth')->can('manage-reception');
Route::delete('flags/{flag}', [FlagController::class, 'destroy'])->name('flags.destroy')->middleware('auth')->can('manage-reception');
Route::put('flags/{flag}/restore', [FlagController::class, 'restore'])->name('flags.restore')->middleware('auth')->can('manage-reception');

// Doctors
Route::get('doctors', [DoctorController::class, 'index'])->name('doctors')->middleware('auth')->can('manage-reception');
Route::get('doctors/create', [DoctorController::class, 'create'])->name('doctors.create')->middleware('auth')->can('manage-reception');
Route::post('doctors', [DoctorController::class, 'store'])->name('doctors.store')->middleware('auth')->can('manage-reception');
Route::get('doctors/{doctor}/edit', [DoctorController::class, 'edit'])->name('doctors.edit')->middleware('auth')->can('manage-reception');
Route::put('doctors/{doctor}', [DoctorController::class, 'update'])->name('doctors.update')->middleware('auth')->can('manage-reception');
Route::delete('doctors/{doctor}', [DoctorController::class, 'destroy'])->name('doctors.destroy')->middleware('auth')->can('manage-reception');
Route::put('doctors/{doctor}/restore', [DoctorController::class, 'restore'])->name('doctors.restore')->middleware('auth')->can('manage-reception');

// Labs
Route::get('labs', [LabController::class, 'index'])->name('labs')->middleware('auth')->can('manage-reception');
Route::get('labs/create', [LabController::class, 'create'])->name('labs.create')->middleware('auth')->can('manage-reception');
Route::post('labs', [LabController::class, 'store'])->name('labs.store')->middleware('auth')->can('manage-reception');
Route::get('labs/{lab}/edit', [LabController::class, 'edit'])->name('labs.edit')->middleware('auth')->can('manage-reception');
Route::put('labs/{lab}', [LabController::class, 'update'])->name('labs.update')->middleware('auth')->can('manage-reception');
Route::delete('labs/{lab}', [LabController::class, 'destroy'])->name('labs.destroy')->middleware('auth')->can('manage-reception');
Route::put('labs/{lab}/restore', [LabController::class, 'restore'])->name('labs.restore')->middleware('auth')->can('manage-reception');

// Groups
Route::get('groups', [GroupController::class, 'index'])->name('groups')->middleware('auth')->can('manage-reception');
Route::get('groups/create', [GroupController::class, 'create'])->name('groups.create')->middleware('auth')->can('manage-reception');
Route::post('groups', [GroupController::class, 'store'])->name('groups.store')->middleware('auth')->can('manage-reception');
Route::get('groups/{group}/edit', [GroupController::class, 'edit'])->name('groups.edit')->middleware('auth')->can('manage-reception');
Route::put('groups/{group}', [GroupController::class, 'update'])->name('groups.update')->middleware('auth')->can('manage-reception');
Route::delete('groups/{group}', [GroupController::class, 'destroy'])->name('groups.destroy')->middleware('auth')->can('manage-reception');
Route::put('groups/{group}/restore', [GroupController::class, 'restore'])->name('groups.restore')->middleware('auth')->can('manage-reception');

// Offers
Route::get('offers', [OfferController::class, 'index'])->name('offers')->middleware('auth')->can('manage-users');
Route::get('offers/create', [OfferController::class, 'create'])->name('offers.create')->middleware('auth')->can('manage-users');
Route::post('offers', [OfferController::class, 'store'])->name('offers.store')->middleware('auth')->can('manage-users');
Route::get('offers/{offer}/edit', [OfferController::class, 'edit'])->name('offers.edit')->middleware('auth')->can('manage-users');
Route::put('offers/{offer}', [OfferController::class, 'update'])->name('offers.update')->middleware('auth')->can('manage-users');
Route::delete('offers/{offer}', [OfferController::class, 'destroy'])->name('offers.destroy')->middleware('auth')->can('manage-users');
Route::put('offers/{offer}/restore', [OfferController::class, 'restore'])->name('offers.restore')->middleware('auth')->can('manage-users');

// Tests
Route::get('tests', [TestController::class, 'index'])->name('tests')->middleware('auth')->can('manage-users');
Route::get('tests/create', [TestController::class, 'create'])->name('tests.create')->middleware('auth')->can('manage-users');
Route::post('tests', [TestController::class, 'store'])->name('tests.store')->middleware('auth')->can('manage-users');
Route::get('tests/{test}/edit', [TestController::class, 'edit'])->name('tests.edit')->middleware('auth')->can('manage-users');
Route::put('tests/{test}', [TestController::class, 'update'])->name('tests.update')->middleware('auth')->can('manage-users');
Route::delete('tests/{test}', [TestController::class, 'destroy'])->name('tests.destroy')->middleware('auth')->can('manage-users');
Route::put('tests/{test}/restore', [TestController::class, 'restore'])->name('tests.restore')->middleware('auth')->can('manage-users');
Route::delete('tests/{test}/forceDelete', [TestController::class, 'forceDelete'])->name('tests.forceDelete')->middleware('auth')->can('force-delete');

// Patients
Route::get('patients', [PatientController::class, 'index'])->name('patients')->middleware('auth')->can('manage-reception');
Route::get('patients/{patient}/edit', [PatientController::class, 'edit'])->name('patients.edit')->middleware('auth')->can('manage-reception');
Route::put('patients/{patient}', [PatientController::class, 'update'])->name('patients.update')->middleware('auth')->can('manage-reception');
Route::delete('patients/{patient}', [PatientController::class, 'destroy'])->name('patients.destroy')->middleware('auth')->can('manage-reception');
Route::put('patients/{patient}/restore', [PatientController::class, 'restore'])->name('patients.restore')->middleware('auth')->can('manage-reception');
Route::delete('patients/{patient}/forceDelete', [PatientController::class, 'forceDelete'])->name('patients.forceDelete')->middleware('auth')->can('manage-users');

// PatientsVisits
Route::get('patientsVisits/create_full', [PatientVisit::class, 'create_full'])->name('patientsVisits.create_full')->middleware('auth')->can('manage-reception');
Route::post('patientsVisits', [PatientVisit::class, 'storeFull'])->name('patientsVisits.storeFull')->middleware('auth')->can('manage-reception');

Route::get('patientsVisits/{visit}/editFull', [PatientVisit::class, 'editFull'])->name('patientsVisits.editFull')->middleware('auth')->can('manage-reception');
Route::put('patientsVisits/{visit}/updateFull', [PatientVisit::class, 'updateFull'])->name('patientsVisits.updateFull')->middleware('auth')->can('manage-reception');

Route::get('patientsVisits/{patient}/createNew', [PatientVisit::class, 'createNew'])->name('patientsVisits.createNew')->middleware('auth')->can('manage-reception');
Route::post('patientsVisits/{patient}', [PatientVisit::class, 'storeNew'])->name('patientsVisits.storeNew')->middleware('auth')->can('manage-reception');

Route::get('patientsVisits/{visit}/print', [PatientVisit::class, 'print'])->name('patientsVisits.print')->middleware('auth')->can('manage-reception');

// Settings
Route::get('settings/edit', [SettingController::class, 'edit'])->name('settings.edit')->middleware('auth')->can('manage-users');
Route::put('settings/{setting}', [SettingController::class, 'update'])->name('settings.update')->middleware('auth')->can('manage-users');


// Statistics
// Route::get('statistics', [StatisticController::class, 'index'])->name('statistics.index')->middleware('auth');

Route::get('statistics/income_report', [StatisticController::class, 'IncomeReportIndex'])->name('income_report/index')->middleware('auth')->can('manage-reception');
Route::post('statistics/income_report', [StatisticController::class, 'IncomeReportFetch'])->name('income_report/fetch')->middleware('auth')->can('manage-reception');

Route::get('statistics/doctors_report', [StatisticController::class, 'DoctorsReportIndex'])->name('doctors_report/index')->middleware('auth')->can('manage-reception');
Route::post('statistics/doctors_report', [StatisticController::class, 'DoctorsReportFetch'])->name('doctors_report/fetch')->middleware('auth')->can('manage-reception');

Route::get('statistics/side_labs_report', [StatisticController::class, 'SideLabsReportIndex'])->name('side_labs_report/index')->middleware('auth')->can('manage-reception');
Route::post('statistics/side_labs_report', [StatisticController::class, 'SideLabsReportFetch'])->name('side_labs_report/fetch')->middleware('auth')->can('manage-reception');

Route::get('statistics/tests_report', [StatisticController::class, 'TestsReportIndex'])->name('tests_report/index')->middleware('auth')->can('manage-reception');
Route::post('statistics/tests_report', [StatisticController::class, 'TestsReportFetch'])->name('tests_report/fetch')->middleware('auth')->can('manage-reception');

Route::get('statistics/offers_report', [StatisticController::class, 'OffersReportIndex'])->name('offers_report/index')->middleware('auth')->can('manage-reception');
Route::post('statistics/offers_report', [StatisticController::class, 'OffersReportFetch'])->name('offers_report/fetch')->middleware('auth')->can('manage-reception');

Route::get('statistics/visits_report', [StatisticController::class, 'VisitsReportIndex'])->name('visits_report/index')->middleware('auth')->can('manage-reception');
Route::post('statistics/visits_report', [StatisticController::class, 'VisitsReportFetch'])->name('visits_report/fetch')->middleware('auth')->can('manage-reception');

// Visits
Route::get('statistics/visitsDetails', [StatisticController::class, 'visitsDetails'])->name('visitsDetails')->middleware('auth')->can('manage-reception');
// Route::delete('visits/{visit}', [VisitController::class, 'destroy'])->name('visits.destroy')->middleware('auth');
// Route::delete('visits/{visit}/forceDelete', [VisitController::class, 'forceDelete'])->name('visits.forceDelete')->middleware('auth');
// Route::put('visits/{visit}/restore', [VisitController::class, 'restore'])->name('visits.restore')->middleware('auth');

Route::get('/buckup', [BuckupController::class, 'download'])->name('buckup')->can('manage-users');

// Researchs
Route::get('researchs/VisitsIndex', [ResearchController::class, 'VisitsIndex'])->name('researchs.VisitsIndex')->middleware('auth')->can('manage-users');
// TrackingTags
Route::get('tracking_tags', [TrackingTagController::class, 'index'])->name('tracking_tags')->middleware('auth')->can('manage-reception');
Route::get('tracking_tags/create', [TrackingTagController::class, 'create'])->name('tracking_tags.create')->middleware('auth')->can('manage-reception');
Route::post('tracking_tags', [TrackingTagController::class, 'store'])->name('tracking_tags.store')->middleware('auth')->can('manage-reception');
Route::get('tracking_tags/{tracking_tag}/edit', [TrackingTagController::class, 'edit'])->name('tracking_tags.edit')->middleware('auth')->can('manage-reception');
Route::put('tracking_tags/{tracking_tag}', [TrackingTagController::class, 'update'])->name('tracking_tags.update')->middleware('auth')->can('manage-reception');
Route::delete('tracking_tags/{tracking_tag}', [TrackingTagController::class, 'destroy'])->name('tracking_tags.destroy')->middleware('auth')->can('manage-reception');
Route::put('tracking_tags/{tracking_tag}/restore', [TrackingTagController::class, 'restore'])->name('tracking_tags.restore')->middleware('auth')->can('manage-reception');

Route::middleware(['auth'])->post('sync_tracking_tag', [TrackingTagController::class, 'sync_tracking_tag'])->name('sync_tracking_tag')->can('manage-reception');
Route::middleware(['auth'])->post('detach_tracking_tag', [TrackingTagController::class, 'detach_tracking_tag'])->name('detach_tracking_tag')->can('manage-reception');
Route::middleware(['auth'])->post('get_tracking_tag_info', [TrackingTagController::class, 'get_tracking_tag_info'])->name('get_tracking_tag_info')->can('manage-reception');