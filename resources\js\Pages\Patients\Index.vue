<template>
<Head title="Patients" />
  <div class="p-4">
    <div class="flex flex-col sm:flex-row mb-3 justify-between items-center">
      <search-filter v-model="filters.search" direction="rtl" placeholder="بحث عن المراجعين" class="mb-2 sm:mb-0 w-full" @reset="reset">
        <label class="block text-gray-700">Trashed:</label>
        <select v-model="filters.trashed" class="mt-1 w-full form-select">
          <option :value="null" />
          <option value="with">{{ trans('WithTrashed') }}</option>
          <option value="only">{{ trans('OnlyTrashed') }}</option>
        </select>
      </search-filter>
      <Link class="btn-green" :href="route('patientsVisits.create_full')">
        <span class="inline sm:hidden">{{ trans('Create')}}</span>
        <span class="hidden sm:inline-flex">{{ trans('RegisterANewPatient') }}</span>
      </Link>
    </div>

    <div class="shadow overflow-x-auto rounded-lg bg-white">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-green-500">
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('ID') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Name') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Gender') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Age') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Mobile') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Created_at') }}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Updated_at') }}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Delete') }} </th>
          </tr>
        </thead>

        <tr v-for="patient in patients.data" :key="patient.id" class=" hover:bg-gray-100 focus-within:bg-gray-100" >
          
          <td class="border-t">
            <Link class="px-4 py-2 flex items-center" :href="route('patients.edit', patient.id)" tabindex="-1">
              {{ patient.id }}
            </Link>
          </td>
          <td class="border-t">
            <Link class="px-4 py-2 flex items-center focus:text-green-500" :href="route('patients.edit', patient.id)">
              {{ patient.name }}
              <icon v-if="patient.deleted_at" name="trash" class="flex-shrink-0 w-3 h-3 fill-gray-400 ml-2" />
            </Link>
          </td>

          <td class="border-t">
            <Link class="px-4 py-2 flex items-center" :href="route('patients.edit', patient.id)" tabindex="-1">
               {{ patient.gender===1? trans('Male') : patient.gender===2? trans('Female'): trans('Missing') }}
            </Link>
          </td>
          <td class="border-t">
            <Link class="px-4 py-2 flex items-center" :href="route('patients.edit', patient.id)" tabindex="-1">
              {{ patient.age }}
            </Link>
          </td>
          <td class="border-t">
            <Link class="px-4 py-2 flex items-center" :href="route('patients.edit', patient.id)" tabindex="-1">
              {{ patient.mobile }}
            </Link>
          </td>
          <td class="border-t">
            <Link class="px-4 py-2 flex items-center" :href="route('patients.edit', patient.id)" tabindex="-1">
              {{ patient.created_at}}
            </Link>
          </td>
          <td class="border-t">
            <Link class="px-4 py-2 flex items-center" :href="route('patients.edit', patient.id)" tabindex="-1">
              {{patient.updated_at}}
            </Link>
          </td>

          <td class="border-t">
            <button v-if="!patient.deleted_at" class="px-4 py-2 flex items-center text-red-600 hover:underline"  type="button" @click="destroy(patient.id, patient.name)">{{ trans('Delete') }}  </button>
            <button v-else class="px-4 py-2 flex items-center text-green-600 hover:underline"  type="button" @click="restore(patient.id, patient.name)">{{ trans('Restor') }}  </button>
          </td>
        </tr>       
        <tr v-if="patients.data.length === 0">
          <td class="border-t px-4 py-2" colspan="4">No patients found.</td>
        </tr>
      </table>
      <hr class="bg-gray-300 pt-px">
      <pagination class="px-6 py-2 bg-white border-none border-t p-0" :links="patients.links" :from="patients.from" :to="patients.to" :total="patients.total" />
    </div>
  </div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
  import Icon from '@/MyComponents/Icon.vue'
  import pickBy from "lodash/pickBy"
  import AppLayout from '@/Layouts/AppLayout.vue'
  import throttle from 'lodash/throttle'
  import Pagination from '@/MyComponents/Pagination.vue'
  import SearchFilter from '@/MyComponents/SearchFilter.vue'

  import moment from "moment";
  import { Head, Link } from '@inertiajs/inertia-vue3';

  import { Inertia } from '@inertiajs/inertia';

  import { reactive} from '@vue/reactivity'
  import { watch } from '@vue/runtime-core'

  const props = defineProps({
    filters: Object,
    patients: Object,
  })

  const filters = reactive({
    search: props.filters.search,
    trashed: props.filters.trashed,
  })

  watch(filters, throttle(function () {
    Inertia.get(route('patients'),  pickBy(filters), { preserveState: true, replace: true });
    }, 300),
    { deep: true }
  );

  function reset() {
    Object.assign(filters,{search: null, trashed: ''});
  }

  let destroy = (patient_id, patient_name) => {
    if (confirm('Are you sure you want to delete ' + patient_name + ' ?')) {
      Inertia.delete(route('patients.destroy', patient_id))
    }
  };

  let restore = (patient_id, patient_name) => {
    if (confirm('Are you sure you want to restore ' + patient_name + ' ?')) {
      Inertia.put(route('patients.restore', patient_id))
    }
  };
</script>
