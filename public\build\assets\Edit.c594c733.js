import{Q as F,o as n,c,b as i,u as e,a as o,B as m,d as p,t as u,C as f,y,E as V,z as x,F as U,H as v,L as k,U as h}from"./app.5bf25e6f.js";import{A as _}from"./AppLayout.14f8c8f6.js";import{_ as w}from"./TextInput.48e8e32c.js";import{_ as B}from"./SelectInput.16ffd220.js";import{L as A}from"./LoadingButton.c8fb65b2.js";import{T as L}from"./TrashedMessage.5487e7e2.js";import"./plugin-vue_export-helper.21dcd24c.js";const C={class:"p-2 sm:p-4"},R={class:"mb-2 flex justify-start max-w-3xl"},S={class:"font-bold text-2xl"},j=o("span",{class:"text-indigo-400 font-medium"}," / ",-1),N=["src"],T={class:"bg-white rounded-md shadow overflow-hidden max-w-3xl"},D=o("option",{value:"Administrator"},"Administrator",-1),H=o("option",{value:"Reception_staff"},"Reception_Staff",-1),$={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},z={layout:_},J=Object.assign(z,{__name:"Edit",props:{locale:String,userForEdit:Object},setup(a){const d=a;let t=F({_method:"put",name:d.userForEdit.name,email:d.userForEdit.email,password:null,role:d.userForEdit.role}),b=()=>{t.post(route("users.update",d.userForEdit.id),{onSuccess:()=>t.reset("password","photo")})},g=()=>{confirm("Are you sure you want to delete this user?")&&h.Inertia.delete(route("users.destroy",d.userForEdit.id))},E=()=>{confirm("Are you sure you want to restore this user?")&&h.Inertia.put(route("users.restore",d.userForEdit.id))};return(l,r)=>(n(),c(U,null,[i(e(v),{title:"Edit User"}),o("div",C,[o("div",R,[o("h2",S,[i(e(k),{class:"text-indigo-400 hover:text-indigo-600",href:l.route("users")},{default:m(()=>[p(u(l.trans("UsersList")),1)]),_:1},8,["href"]),j,p(" "+u(e(t).name),1)]),a.userForEdit.photo?(n(),c("img",{key:0,class:"block w-10 h-10 rounded-full ml-4",src:`/storage/${a.userForEdit.photo}`},null,8,N)):f("",!0)]),o("div",T,[a.userForEdit.deleted_at?(n(),y(L,{key:0,class:"mb-6",onRestore:e(E)},{default:m(()=>[p(u(l.trans("This")+" "+l.trans("User")+" "+l.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):f("",!0),o("form",{onSubmit:r[5]||(r[5]=V((...s)=>e(b)&&e(b)(...s),["prevent"]))},[o("div",{class:x(["p-8 -mr-6 -mb-8 flex flex-wrap",a.locale=="ar"?"rtl text-right":"ltr text-left"])},[i(w,{modelValue:e(t).name,"onUpdate:modelValue":r[0]||(r[0]=s=>e(t).name=s),error:e(t).errors.name,type:"text",direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",label:"Username"},null,8,["modelValue","error"]),i(w,{modelValue:e(t).email,"onUpdate:modelValue":r[1]||(r[1]=s=>e(t).email=s),error:e(t).errors.email,type:"email",direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",label:"Email"},null,8,["modelValue","error"]),i(w,{modelValue:e(t).password,"onUpdate:modelValue":r[2]||(r[2]=s=>e(t).password=s),error:e(t).errors.password,type:"password",direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",autocomplete:"new-password",label:"Password"},null,8,["modelValue","error"]),i(B,{modelValue:e(t).role,"onUpdate:modelValue":r[3]||(r[3]=s=>e(t).role=s),error:e(t).errors.role,direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",label:"Role"},{default:m(()=>[D,H]),_:1},8,["modelValue","error"])],2),o("div",$,[a.userForEdit.can.edit?(n(),y(A,{key:0,loading:e(t).processing,class:"btn-green",type:"submit"},{default:m(()=>[p(u(l.trans("Update")+" "+l.trans("User")),1)]),_:1},8,["loading"])):f("",!0),!a.userForEdit.deleted_at&&a.userForEdit.can.delete?(n(),c("button",{key:1,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:r[4]||(r[4]=(...s)=>e(g)&&e(g)(...s))},u(l.trans("Delete")+" "+l.trans("User")),1)):f("",!0)])],32)])])],64))}});export{J as default};
