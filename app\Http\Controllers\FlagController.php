<?php

namespace App\Http\Controllers;


use App\Models\Flag;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Gate;

class FlagController extends Controller
{

    // public function insert_flags()
    // {
    //     $now = now()->toDateTimeString();

    //     $flags = [
    //         ['user_id' => 1, 'id' => 1, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Negative'],
    //         ['user_id' => 1, 'id' => 2, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Positive'],
    //         ['user_id' => 1, 'id' => 3, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Equivocal'],
    //         ['user_id' => 1, 'id' => 4, 'created_at' => $now, 'updated_at' => $now, 'name' => 'High'],
    //         ['user_id' => 1, 'id' => 5, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Low'],
    //         ['user_id' => 1, 'id' => 6, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Normal'],
    //         ['user_id' => 1, 'id' => 7, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Abnormal'],
    //         ['user_id' => 1, 'id' => 8, 'created_at' => $now, 'updated_at' => $now, 'name' => 'E.Coli'],
    //         ['user_id' => 1, 'id' => 9, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Staphylococcus aureus'],
    //         ['user_id' => 1, 'id' => 10, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Staphylococcus epidermidis'],
    //         ['user_id' => 1, 'id' => 11, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Streptococcus pyogenes'],
    //         ['user_id' => 1, 'id' => 12, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Proteus'],
    //         ['user_id' => 1, 'id' => 13, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Klebsiella'],
    //         ['user_id' => 1, 'id' => 14, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Pseudomonas aeruginosa'],
    //         ['user_id' => 1, 'id' => 15, 'created_at' => $now, 'updated_at' => $now, 'name' => 'No growth of bacteria'],
    //         ['user_id' => 1, 'id' => 16, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Growth of moneilia'],
    //         ['user_id' => 1, 'id' => 17, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Staphylococcus'],
    //         ['user_id' => 1, 'id' => 18, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Streptococcus'],
    //         ['user_id' => 1, 'id' => 19, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Pseudomonas ssp'],
    //         ['user_id' => 1, 'id' => 20, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Enterococcus'],
    //         ['user_id' => 1, 'id' => 21, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Mycobacterium'],
    //         ['user_id' => 1, 'id' => 22, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Mycoplasma'],
    //         ['user_id' => 1, 'id' => 23, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Chlamydia'],
    //         ['user_id' => 1, 'id' => 24, 'created_at' => $now, 'updated_at' => $now, 'name' => 'Streptococcus agalactiae']
    //     ];

    //     Flag::insert($flags);
    // }
    public function index()
    {
        return Inertia::render('Flags/Index', [
            'filters' => Request::all('search', 'trashed'),
            'flags' => Flag::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($flag) => [
                    'id' => $flag->id,
                    'name' => $flag->name,
                    'created_at' => $flag->created_at->diffForHumans(),
                    'updated_at' => $flag->updated_at->diffForHumans(),
                    'deleted_at' => $flag->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Flags/Create');
    }

    public function store()
    {
        Auth::user()->flags()->create(
            Request::validate([
                'name' => ['required', 'max:100', 'unique:flags']
            ])
        );
        if (Request::input('createAnother')) {
            return Redirect::back()->with('success', 'Flag created.');
        } else {
            return Redirect::route('flags')->with('success', 'Flag created.');
        }
    }

    public function edit(Flag $flag)
    {
        return Inertia::render('Flags/Edit', [
            'flag' => [
                'id' => $flag->id,
                'name' => $flag->name,
                'created_at' => $flag->created_at,
                'deleted_at' => $flag->deleted_at,
            ],
        ]);
    }

    public function update(Flag $flag)
    {
        $flag->update(
            Request::validate([
                'name' => ['required', 'max:100', Rule::unique('flags')->ignore($flag->id)],
            ])
        );
        return Redirect::route('flags')->with('success', 'Flag updated.');
    }

    public function destroy(Flag $flag)
    {
        $flag->delete();
        return Redirect::route('flags')->with('success', 'Flag deleted.');
    }

    public function restore(Flag $flag)
    {
        $flag->restore();
        return Redirect::back()->with('success', 'Flag restored.');
    }
}
