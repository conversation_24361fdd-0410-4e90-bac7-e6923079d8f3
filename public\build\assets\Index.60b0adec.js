import{v as x,q as b,o as c,c as p,b as a,u as r,a as t,B as d,t as o,F as _,D as g,C as u,H as y,K as v,R as k,L as i,d as V,y as N,U as j}from"./app.5bf25e6f.js";import{A as B,I as m}from"./AppLayout.14f8c8f6.js";import{t as C,S,p as D}from"./SearchFilter.f110f3d1.js";import{P as O}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const F={class:"p-4 max-w-7xl"},I={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},L=t("label",{class:"block text-gray-700"},"Trashed:",-1),M=t("option",{value:null},null,-1),T={value:"with"},U={value:"only"},A={class:"inline sm:hidden"},E={class:"hidden sm:flex w-48 text-center"},H={class:"shadow overflow-x-auto rounded-lg bg-white"},P={class:"min-w-full divide-y divide-gray-200"},R={class:"bg-gray-50"},q={class:"bg-green-500"},G={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},K={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},X={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Y={class:"border-t p-0"},Z={class:"px-6 py-2 flex items-center"},$={class:"ml-4"},tt={class:"flex items-center text-sm font-medium text-gray-900"},et={class:"border-t p-0"},st={class:"px-6 py-2"},ot={class:"border-t p-0"},at={class:"px-6 py-2"},rt={class:"border-t p-0"},dt={class:"px-6 py-2"},it={class:"border-t p-0"},lt={class:"px-6 py-2"},nt={class:"border-t p-0"},ct={class:"px-6 py-2"},ht={class:"border-t p-0"},pt={class:"px-6 py-2"},ft={key:0},_t=t("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No doctors found.",-1),ut=[_t],mt=t("hr",{class:"bg-gray-300 pt-px"},null,-1),wt={layout:B},Vt=Object.assign(wt,{__name:"Index",props:{filters:Object,doctors:Object},setup(l){const f=l,n=x({search:f.filters.search,trashed:f.filters.trashed});b(n,C(function(){j.Inertia.get(route("doctors"),D(n),{preserveState:!0,replace:!0})},300),{deep:!0});function w(){Object.assign(n,{search:null,trashed:""})}return(e,h)=>(c(),p(_,null,[a(r(y),{title:"Doctors"}),t("div",F,[t("div",I,[a(S,{modelValue:n.search,"onUpdate:modelValue":h[1]||(h[1]=s=>n.search=s),direction:"rtl",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0627\u0637\u0628\u0627\u0621",class:"mb-2 sm:mb-0 w-full",onReset:w},{default:d(()=>[L,v(t("select",{"onUpdate:modelValue":h[0]||(h[0]=s=>n.trashed=s),class:"mt-1 w-full form-select"},[M,t("option",T,o(e.trans("WithTrashed")),1),t("option",U,o(e.trans("OnlyTrashed")),1)],512),[[k,n.trashed]])]),_:1},8,["modelValue"]),a(r(i),{class:"w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium",href:e.route("doctors.create")},{default:d(()=>[t("span",A,o(e.trans("Create")),1),t("span",E,o(e.trans("CreateANewDoctor")),1)]),_:1},8,["href"])]),t("div",H,[t("table",P,[t("thead",R,[t("tr",q,[t("th",G,o(e.trans("Name")),1),t("th",K,o(e.trans("Gender")),1),t("th",W,o(e.trans("Mobile")),1),t("th",z,o(e.trans("Specialty")),1),t("th",J,o(e.trans("Created_at")),1),t("th",Q,o(e.trans("Updated_at")),1),t("th",X,o(e.trans("Edit")),1)])]),(c(!0),p(_,null,g(l.doctors.data,s=>(c(),p("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[t("td",Y,[a(r(i),{href:e.route("doctors.edit",s.id),tabindex:"-1"},{default:d(()=>[t("div",Z,[t("div",$,[t("div",tt,[V(o(s.name)+" ",1),s.deleted_at?(c(),N(m,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):u("",!0)])])])]),_:2},1032,["href"])]),t("td",et,[a(r(i),{href:e.route("doctors.edit",s.id),tabindex:"-1"},{default:d(()=>[t("div",st,o(s.gender===1?e.trans("Male"):s.gender===2?e.trans("Female"):e.trans("Missing")),1)]),_:2},1032,["href"])]),t("td",ot,[a(r(i),{href:e.route("doctors.edit",s.id),tabindex:"-1"},{default:d(()=>[t("div",at,o(s.mobile),1)]),_:2},1032,["href"])]),t("td",rt,[a(r(i),{href:e.route("doctors.edit",s.id),tabindex:"-1"},{default:d(()=>[t("div",dt,o(s.specialty),1)]),_:2},1032,["href"])]),t("td",it,[a(r(i),{href:e.route("doctors.edit",s.id),tabindex:"-1"},{default:d(()=>[t("div",lt,o(s.created_at),1)]),_:2},1032,["href"])]),t("td",nt,[a(r(i),{href:e.route("doctors.edit",s.id),tabindex:"-1"},{default:d(()=>[t("div",ct,o(s.updated_at),1)]),_:2},1032,["href"])]),t("td",ht,[a(r(i),{class:"flex items-center text-right text-sm font-medium",href:e.route("doctors.edit",s.id),tabindex:"-1"},{default:d(()=>[t("div",pt,[a(m,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),l.doctors.data.length===0?(c(),p("tr",ft,ut)):u("",!0)]),mt,a(O,{class:"px-6 py-2 bg-white border-none border-t p-0",links:l.doctors.links,from:l.doctors.from,to:l.doctors.to,total:l.doctors.total},null,8,["links","from","to","total"])])])],64))}});export{Vt as default};
