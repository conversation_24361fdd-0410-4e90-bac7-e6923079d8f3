<template>
    <Head title="Dashboard" />
    <div class="max-w-7xl mx-auto sm:px-6 mt-8 2xl:mt-12">
        <div class="p-6 mt-4 bg-white dark:bg-gray-800 overflow-hidden shadow sm:rounded-lg leading-10">
            <div class="flex justify-center px-6 py-4 sm:justify-start">
                <ApplicationLogo class="" />
            </div>
            <div class="mb-4 p-6 sm:px-20 bg-white border-gray-200 rtl">
                <div class="mt-4">
                    <span class="text-blue-700 font-medium md:font-bold text-2xl md:text-4xl"> أهلاً بكم في </span>
                    <span class="text-red-700 font-medium md:font-bold text-2xl md:text-4xl">نظام إدارة المختبرات الطبية </span>
                </div>
                <div class="mt-6 text-gray-800 text-lg md:text-3xl leading-10">
                    <p>
                        تم تصميم وبرمجة هذا النظام بواسطة المبرمج
                        <a href="https://www.facebook.com/alebrahimyahmed1" class="w-6 mx-1 text-blue-600">أحمد ألإبراهيمي.</a>
                    </p>
                    <p>
                        لطلب نسخة من هذا النظام أو لطلبات تصميم البرامج
                        والمواقع الإلكترونية الرجاء التواصل عبر رقم الهاتف
                        (٠٧٨٠٦٣٤٩٠٢٨) أو عبر مواقع التواصل الإجتماعي أدناه.
                    </p>
                    <p class="text-red-500 text-lg md:text-2xl mt-8">
                        جميع الحقوق محفوظة وغير مسموح نسخ أو إعادة توزيع هذا
                        النظام أو التلاعب به بأي شكل من الاشكال.
                    </p>
                </div>
            </div>
        </div>
        <Footer />
    </div>
</template>
    
<script>
    export default {layout: AppLayout}
</script>
<script setup>
import AppLayout from "@/Layouts/AppLayout.vue";
import Welcome from "@/Jetstream/Welcome.vue";
import ApplicationLogo from "@/Jetstream/ApplicationLogo.vue";
import Footer from "@/MyComponents/Footer.vue";
import { Head } from '@inertiajs/inertia-vue3';

const props = defineProps({
    locale: String,
});
</script>