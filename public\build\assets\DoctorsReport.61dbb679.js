import{Q as b,o as c,c as m,b as p,u as n,a as t,E as g,t as e,K as _,S as h,B as y,z as w,d,F as v,D,H as S}from"./app.5bf25e6f.js";import{_ as k}from"./StatisticsNav.0926be50.js";import{A as T}from"./AppLayout.14f8c8f6.js";import{L as A}from"./LoadingButton.c8fb65b2.js";import{u as R,w as B,a as E}from"./xlsx.0799a57e.js";import{h as F}from"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const M={class:"p-2 xl:p-3"},C={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4"},V={class:"flex flex-wrap"},L={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},N={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},Y={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},j={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},I={id:"reportToPrint",class:"flex overflow-hidden max-w-3xl"},P={class:"flex w-full"},z={id:"table",class:"w-full shadow-lg"},H={class:"ttr"},O={class:"tth text-center",colspan:"3"},Q={class:"ttr"},U={class:"ttr"},$={colspan:"1",class:"tth text-center"},G={colspan:"1",class:"tth text-center"},K={colspan:"1",class:"tth text-center"},q={key:0},J={class:"text-yellow-500"},W={class:"text-yellow-500"},X=d(" )"),Z={key:1},tt=d("( "),et={class:"text-yellow-500"},st=d(" )"),ot={class:"ttd text-yellow-500 text-left"},rt={class:"ttd text-yellow-500 text-left"},nt={class:"ttd text-yellow-500 text-center"},at={layout:T},ut=Object.assign(at,{__name:"DoctorsReport",props:{errors:Object,locale:String,ReportDateAndTime:String,start_date:String,end_date:String,doctors_income:Array},setup(a){let l=b({start_date:null,end_date:null}),x=()=>{l.post(route("doctors_report/fetch"),{preserveState:!0,onSuccess:()=>{}})},u=()=>{setTimeout(function(){window.print()},50)},f=(s,r)=>{var o=document.getElementById("table"),i=R.table_to_book(o,{sheet:"sheet1"});return r?B(i,{bookType:type,bookSST:!0,type:"base64"}):E(i,s||"MySheetName."+(type||"xlsx"))};return(s,r)=>(c(),m("div",M,[p(n(S),{title:"DoctorsReport"}),t("div",C,[t("form",{onSubmit:r[4]||(r[4]=g((...o)=>n(x)&&n(x)(...o),["prevent"]))},[t("div",V,[t("div",L,[t("span",N,e(s.trans("From"))+": ",1),_(t("input",{"onUpdate:modelValue":r[0]||(r[0]=o=>n(l).start_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,n(l).start_date]])]),t("div",Y,[t("span",j,e(s.trans("To"))+": ",1),_(t("input",{"onUpdate:modelValue":r[1]||(r[1]=o=>n(l).end_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,n(l).end_date]])]),p(A,{loading:n(l).processing,type:"submit",class:"btn-indigo flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3"},{default:y(()=>[d(e(s.trans("Go")),1)]),_:1},8,["loading"]),t("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:r[2]||(r[2]=o=>n(f)("DoctorsReport.xlsx"))},e(s.trans("ExportToExcel")),1),t("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:r[3]||(r[3]=(...o)=>n(u)&&n(u)(...o))},e(s.trans("Print")),1)])],32),p(k)]),t("div",I,[t("div",P,[t("table",z,[t("tr",H,[t("th",O,e(s.trans("DoctorsReport")),1)]),t("tr",Q,[t("th",{class:w(["tth",a.locale=="ar"?"rtl text-right":"ltr text-left"]),locale:"",colspan:"3"},e(s.trans("ReportDateAndTime"))+": "+e(n(F)(a.ReportDateAndTime).format("YYYY-MM-DD h:m A")),3)]),t("tr",U,[t("th",$,e(s.trans("Doctor")),1),t("th",G,e(s.trans("PatientsCount")),1),t("th",K,[d(e(s.trans("Income"))+" - ",1),a.start_date&&a.end_date?(c(),m("span",q,[d("( "+e(s.trans("From"))+" ",1),t("span",J,e(a.start_date),1),d(" "+e(s.trans("To"))+" ",1),t("span",W,e(a.end_date),1),X])):(c(),m("span",Z,[tt,t("span",et,e(s.trans("ThisMonth")),1),st]))])]),(c(!0),m(v,null,D(a.doctors_income,(o,i)=>(c(),m("tr",{class:"ttr",key:i},[t("td",ot,e(i+1+" - "+o.doctor_name),1),t("td",rt,e(o.visits_count),1),t("td",nt,e(o.doctor_income)+" IQD",1)]))),128))])])])]))}});export{ut as default};
