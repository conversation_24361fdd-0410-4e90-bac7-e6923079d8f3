import{o as s,c as t,b as o,B as c,K as n,M as i,a as r,A as l,J as d}from"./app.5bf25e6f.js";const _={class:"text-sm text-gray-600"},m={__name:"ActionMessage",props:{on:Boolean},setup(e){return(a,p)=>(s(),t("div",null,[o(d,{"leave-active-class":"transition ease-in duration-1000","leave-from-class":"opacity-100","leave-to-class":"opacity-0"},{default:c(()=>[n(r("div",_,[l(a.$slots,"default")],512),[[i,e.on]])]),_:3})]))}};export{m as _};
