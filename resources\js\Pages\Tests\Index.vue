<template>
  <div class="p-4">
    <Head title="Tests" />
    <div class="flex flex-col sm:flex-row mb-3 justify-between items-center">
      <search-filter v-model="filters.search" direction="ltr" placeholder="Search for tests....." class="mb-2 sm:mb-0 w-full" @reset="reset">
        <label class="block text-gray-700">Category:</label>
        <select v-model="filters.category_id" class="mt-1 w-full form-select">
          <option :value="null"/>
          <option v-for="(category, i) in categories" :key="i"  :value="category.id" >{{category.name}}</option>
        </select>
        <label class="block text-gray-700">Trashed:</label>
        <select v-model="filters.trashed" class="mt-1 w-full form-select">
          <option :value="null" />
          <option value="with">{{ trans('WithTrashed') }}</option>
          <option value="only">{{ trans('OnlyTrashed') }}</option>
        </select>
      </search-filter>
      <Link class="btn-green" :href="route('tests.create')">{{ trans('CreateANewTest') }}</Link>
    </div>

    <div class="shadow overflow-x-auto rounded-lg bg-white">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-green-500">
            <th scope="col"  @click="sort('short_name')" class="py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              <div class="flex justify-start items-center">
                <span class="mr-1">{{trans('ShortName')}}</span>
                <icon v-if="filters.field === 'short_name' && filters.direction === 'asc'" name="asc" class="mt-1" />
                <icon v-if="filters.field === 'short_name' && filters.direction === 'desc'" name="desc" class="mt-1" />
              </div>
            </th>
            <th scope="col" class="px-2 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('FullName')}}
            </th>
            <th scope="col"  @click="sort('category_id')" class="py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              <div class="flex justify-start items-center">
                <span class="mr-1">{{trans('Category')}}</span>
                <icon v-if="filters.field === 'category_id' && filters.direction === 'asc'" name="asc" class="mt-1" />
                <icon v-if="filters.field === 'category_id' && filters.direction === 'desc'" name="desc" class="mt-1" />
              </div>
            </th>
            <th scope="col"  @click="sort('sequence')" class="py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              <div class="flex justify-start items-center">
                <span class="mr-1">{{trans('Sequence')}}</span>
                <icon v-if="filters.field === 'sequence' && filters.direction === 'asc'" name="asc" class="mt-1" />
                <icon v-if="filters.field === 'sequence' && filters.direction === 'desc'" name="desc" class="mt-1" />
              </div>
            </th>
            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('LabToPatientPrice')}}
            </th>
            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('LabToLabPrice')}}
            </th>
            <th scope="col"  @click="sort('created_at')" class="py-4 px-2 cursor-pointer text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              <div class="flex justify-start items-center">
                <span class="mr-1">{{trans('Created_at')}}</span>
                <icon v-if="filters.field === 'created_at' && filters.direction === 'asc'" name="asc" class="mt-1" />
                <icon v-if="filters.field === 'created_at' && filters.direction === 'desc'" name="desc" class="mt-1" />
              </div>
            </th>
            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{trans('Updated_at')}}	
            </th>
            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-50 uppercase tracking-wider">
              {{ trans('Delete') + ' / ' + trans('Restore')}} 
            </th>

          </tr>
        </thead>

        <tr v-for="test in tests.data" :key="test.id" class="hover:bg-gray-100 focus-within:bg-gray-100">
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2 flex items-center">
              <div class="ml-4">
                <div class="flex items-center text-sm font-medium text-gray-900">
                  {{ test.short_name }}
                  <icon v-if="test.deleted_at" name="trash" class="flex-shrink-0 w-3 h-3 fill-gray-200 ml-2" />
                </div>
              </div>
            </div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">{{ test.full_name }}</div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">{{test.category.name}}</div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">{{test.sequence}}</div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">{{test.lab_to_patient_price}}</div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">{{test.lab_to_lab_price}}</div>
            </Link>
          </td>

          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">{{test.created_at}}</div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('tests.edit', test.id)" tabindex="-1">
            <div class="px-6 py-2">{{test.updated_at}}</div>
            </Link>
          </td>
          <td v-if="!test.deleted_at" class="border-t p-0">
            <button class="text-red-600 hover:underline"  type="button" @click="destroy(test.id)">{{ trans('Delete') }} </button>
          </td>
          <td v-else class="border-t p-0">
            <button class=" text-green-600 hover:underline"  type="button" @click="restore(test.id)">{{ trans('Restore') }} </button>
          </td>
        </tr>
        <tr v-if="tests.data.length === 0">
          <td class="border-t p-0 px-6 py-2" colspan="4">No tests found.</td>
        </tr>
      </table>
      <hr class="bg-gray-300 pt-px">
      <pagination class="px-6 py-2 bg-white border-none border-t p-0" :links="tests.links" :from="tests.from" :to="tests.to" :total="tests.total" />
    </div>
  </div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
  import Icon from '@/MyComponents/Icon.vue'
  import pickBy from "lodash/pickBy"
  import AppLayout from '@/Layouts/AppLayout.vue'
  import throttle from 'lodash/throttle'
  import Pagination from '@/MyComponents/Pagination.vue'
  import SearchFilter from '@/MyComponents/SearchFilter.vue'

  import moment from "moment";
  import { Head, Link } from '@inertiajs/inertia-vue3';

  import { Inertia } from '@inertiajs/inertia';

  import { reactive} from '@vue/reactivity'
  import { watch } from '@vue/runtime-core'

  const props = defineProps({
    filters: Object,
    tests: Object,
    categories: Array,
  })

  const filters = reactive({
    search: props.filters.search,
    trashed: props.filters.trashed,
    category_id: props.filters.category_id,
    field: props.filters.field,
    direction: props.filters.direction,
  })

  watch(filters, throttle(function () {
    Inertia.get(route('tests'),  pickBy(filters), { preserveState: true, replace: true });
    }, 300),
    { deep: true }
  );

  function  sort(field) {
    filters.field = field;
    filters.direction = filters.direction === 'asc' ? 'desc' : 'asc';
  }

  function reset() {
      Object.assign(filters,{search: null, trashed: null, category_id: null, field: null, direction: null});
  }

  let destroy = (test_id) => {
    if (confirm('Are you sure you want to destroy this visit ?')) {
      Inertia.delete(route('tests.destroy', test_id))
    }
  };

  let restore = (test_id) => {
    if (confirm('Are you sure you want to restore this visit ? ')) {
      Inertia.put(route('tests.restore', test_id))
    }
  };
</script>
