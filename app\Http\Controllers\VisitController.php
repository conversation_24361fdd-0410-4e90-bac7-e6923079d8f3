<?php

namespace App\Http\Controllers;

use App\Models\Visit;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Gate;

class VisitController extends Controller
{
    // public function index()
    // {
    //     $per_page = 10;
    //     if (Request::filled('per_page')) {
    //         $per_page = Request::input('per_page');
    //     }

    //     Gate::authorize('manage-visits');
    //     return Inertia::render('Statistics/VisitsIndex', [
    //         'filters' => Request::all(
    //             'search',
    //             'trashed',
    //             'sentToday',
    //             'HasDiscount',
    //             'start_date',
    //             'end_date',
    //             'age_min',
    //             'age_max',
    //             'tracking_tag_id',
    //             'test_id',
    //             'test_min_value',
    //             'test_max_value',
    //             'gender',
    //             'per_page',
    //         ),
    //         'tests'           => Test::orderBy('sequence', 'asc')->get()->map->only('id', 'short_name'),
    //         'tracking_tags'       => TrackingTag::orderBy('id', 'desc')->get()->map->only('id', 'name'),
    //         'visits' => Visit::with('patient:id,name')
    //             ->orderBy('id')
    //             ->filter(Request::only(
    //                 'search',
    //                 'trashed',
    //                 'sentToday',
    //                 'HasDiscount',
    //                 'start_date',
    //                 'end_date',
    //                 'age_min',
    //                 'age_max',
    //                 'tracking_tag_id',
    //                 'test_id',
    //                 'test_min_value',
    //                 'test_max_value',
    //                 'gender',
    //                 'per_page',
    //             ))
    //             ->paginate($per_page)
    //             ->withQueryString()
    //             ->through(fn ($visit) => [
    //                 'id'               => $visit->id,
    //                 'name'             => $visit->patient->name ? $visit->patient->name : 'deleted',
    //                 'patient_id'       => $visit->patient ? $visit->patient->id : 'deleted',
    //                 'referred_by'      => $visit->referred_by,
    //                 'doctor_name'      => $visit->doctor ? $visit->doctor->name : null,
    //                 'user'             => $visit->user ? $visit->user->name : null,
    //                 'lab_name'         => $visit->lab ? $visit->lab->name : null,
    //                 'tests_count'      => $visit->tests_count,
    //                 'offers_count'     => $visit->offers_count,

    //                 'tests_cost'       => $visit->tests_cost,
    //                 'discount'         => $visit->discount,
    //                 'final_cost'       => $visit->final_cost,
    //                 'paid_amount'      => $visit->paid_amount,
    //                 'remaining_amount' => $visit->remaining_amount,
    //                 'created_at'       => $visit->created_at,
    //                 'deleted_at'       => $visit->deleted_at,
    //                 'visitTests'       => $visit->tests()->orderBy('sequence')->get()->map->only('id', 'short_name', 'lab_to_patient_price', 'lab_to_lab_price', 'category_id'),
    //             ]),
    //     ]);
    // }

    public function destroy(Visit $visit)
    {
        Gate::authorize('manage-reception');
        $visit->delete();

        return Redirect::back()->with('success', 'Visit deleted.');
    }

    public function restore(Visit $visit)
    {
        Gate::authorize('manage-reception');
        $visit->restore();

        return Redirect::back()->with('success', 'Visit restored.');
    }

    public function forceDelete(Visit $visit)
    {
        Gate::authorize('manage-visits');
        $visit->forceDelete();

        return Redirect::back()->with('success', 'The visit has been permanently deleted.');
    }
}
