import{v as T,Q as D,o as i,c,b as p,u as a,a as t,B as u,d as h,t as s,y as b,C as m,E as L,z as M,F as x,D as C,H as E,L as w,U as S}from"./app.5bf25e6f.js";import{A as I,I as P}from"./AppLayout.14f8c8f6.js";import{_ as y}from"./TextInput.48e8e32c.js";import{_ as k}from"./SelectInput.16ffd220.js";import{L as j}from"./LoadingButton.c8fb65b2.js";import{T as R}from"./TrashedMessage.5487e7e2.js";import{D as O}from"./DropdownSearch.7a091d54.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const $={class:"p-4"},z={class:"mb-2 flex justify-start max-w-xl"},H={class:"font-bold text-2xl"},G=t("span",{class:"text-indigo-400 font-medium"}," /",-1),Q={class:"bg-white rounded-md shadow-sm max-w-xl"},W=t("option",{value:null},null,-1),q={value:"1"},J={value:"2"},K=t("option",{value:null},null,-1),X={value:"1"},Y={value:"2"},Z={value:"3"},tt={value:"4"},et=t("option",{value:null},null,-1),st={value:"1"},ot={value:"2"},at={value:"3"},nt={class:"mt-2 z-30 pr-6 pb-2 w-full"},rt={class:"pr-6 pb-4 w-full"},lt={class:"flex mt-1"},it=["onClick"],dt=["onClick"],pt={class:"px-4 py-2 bg-gray-100 border-t border-gray-100 flex items-center justify-between"},ct={class:"shadow overflow-x-auto rounded-lg bg-white mt-4 max-w-7xl"},ut={class:"flex justify-between py-2 px-4 items-center"},mt={class:"text-left text-gray-900 font-semibold tracking-wider"},ht={class:"min-w-full divide-y divide-gray-200"},gt={class:"bg-gray-50"},ft={class:"bg-green-500"},_t=t("th",{scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},"# ",-1),bt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},wt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},xt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},yt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},kt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},vt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Vt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Tt={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Ct={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},St={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Ft={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},At={key:0,class:"border-t px-2 py-2 flex items-center text-red-500"},Nt={key:1,class:"border-t px-2 py-2"},Ut={class:"border-t px-2 py-2"},Bt={class:"border-t px-2 py-2"},Dt={class:"border-t px-2 py-2"},Lt={class:"border-t px-2 py-2"},Mt={class:"border-t px-2 py-2"},Et={class:"border-t px-2 py-2"},It={class:"border-t px-2 py-2"},Pt={class:"border-t px-2 py-2"},jt={class:"border-t px-2 py-2"},Rt={class:"border-t px-2 py-2"},Ot={class:"border-t px-2 py-2"},$t={class:""},zt={key:0},Ht=t("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No visits found.",-1),Gt=[Ht],Qt={layout:I},oe=Object.assign(Qt,{__name:"Edit",props:{errors:Object,locale:String,patient:Object,tracking_tags:Array,patientTrackingTags:Array},setup(d){const l=d;let g=T(l.tracking_tags.slice()),f=T(l.patientTrackingTags.slice()),F=e=>{axios.post("/sync_tracking_tag",{tracking_tag_id:e.id,patient_id:l.patient.id}).then(n=>{f.push(e),g.splice(g.findIndex(o=>o.id===e.id),1)}).catch(n=>console.log(n))},A=e=>{confirm("Are you sure you want to detach this tracking_tag.?")&&axios.post("/detach_tracking_tag",{tracking_tag_id:e.id,patient_id:l.patient.id}).then(n=>{console.log(n.data),g.push(e),f.splice(f.findIndex(o=>o.id===e.id),1)}).catch(n=>{alert(n.response.data.message)})},N=e=>{axios.post("/get_tracking_tag_info",{tracking_tag_id:e.id,patient_id:l.patient.id}).then(n=>{alert(n.data)}).catch(n=>{alert(n.response.data.message)})},r=D({_method:"put",name:l.patient.name,gender:l.patient.gender,mobile:l.patient.mobile,age:l.patient.age,marital:l.patient.marital,smoking:l.patient.smoking}),v=()=>{r.post(route("patients.update",l.patient.id))},V=()=>{confirm("Are you sure you want to delete this patient ?")&&S.Inertia.delete(route("patients.destroy",l.patient.id))},U=()=>{confirm("Are you sure you want to restore this patient ?")&&S.Inertia.put(route("patients.restore",l.patient.id))};return(e,n)=>(i(),c(x,null,[p(a(E),{title:"Edit Patient"}),t("div",$,[t("div",z,[t("h2",H,[p(a(w),{class:"text-indigo-400 hover:text-indigo-600",href:e.route("patients")},{default:u(()=>[h(s(e.trans("PatientsList")),1)]),_:1},8,["href"]),G,h(" "+s(a(r).name),1)])]),t("div",Q,[d.patient.deleted_at?(i(),b(R,{key:0,class:"mb-6",onRestore:a(U)},{default:u(()=>[h(s(e.trans("This")+" "+e.trans("Patient")+" "+e.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):m("",!0),t("form",{onSubmit:n[7]||(n[7]=L((...o)=>a(v)&&a(v)(...o),["prevent"]))},[t("div",{class:M(["p-8 -mr-6 -mb-8 flex flex-wrap",d.locale=="ar"?"rtl text-right":"ltr text-left"])},[p(y,{modelValue:a(r).name,"onUpdate:modelValue":n[0]||(n[0]=o=>a(r).name=o),error:a(r).errors.name,direction:"rtl",class:"pr-6 pb-4 w-full lg:w-1/2",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),p(k,{modelValue:a(r).gender,"onUpdate:modelValue":n[1]||(n[1]=o=>a(r).gender=o),modelModifiers:{number:!0},error:a(r).errors.gender,class:"pr-6 pb-4 w-full lg:w-1/2",label:"Gender"},{default:u(()=>[W,t("option",q,s(e.trans("Male")),1),t("option",J,s(e.trans("Female")),1)]),_:1},8,["modelValue","error"]),p(y,{modelValue:a(r).age,"onUpdate:modelValue":n[2]||(n[2]=o=>a(r).age=o),error:a(r).errors.age,direction:"ltr",class:"pr-6 pb-4 w-full lg:w-1/2",type:"text",label:"Age"},null,8,["modelValue","error"]),p(y,{modelValue:a(r).mobile,"onUpdate:modelValue":n[3]||(n[3]=o=>a(r).mobile=o),modelModifiers:{number:!0},error:a(r).errors.mobile,direction:"ltr",class:"pr-6 pb-4 w-full lg:w-1/2",type:"text",label:"Mobile"},null,8,["modelValue","error"]),p(k,{modelValue:a(r).marital,"onUpdate:modelValue":n[4]||(n[4]=o=>a(r).marital=o),error:d.errors.marital,class:"pr-6 pb-4 w-full lg:w-1/2",label:"MaritalStatus"},{default:u(()=>[K,t("option",X,s(e.trans("Single")),1),t("option",Y,s(e.trans("Married")),1),t("option",Z,s(e.trans("Divorced")),1),t("option",tt,s(e.trans("Widowed")),1)]),_:1},8,["modelValue","error"]),p(k,{modelValue:a(r).smoking,"onUpdate:modelValue":n[5]||(n[5]=o=>a(r).smoking=o),error:d.errors.smoking,class:"pr-6 pb-4 w-full lg:w-1/2",label:"Smoking"},{default:u(()=>[et,t("option",st,s(e.trans("Smoker")),1),t("option",ot,s(e.trans("NonSmoker")),1),t("option",at,s(e.trans("ExSmoker")),1)]),_:1},8,["modelValue","error"]),t("div",nt,[p(O,{class:"w-full ltr",options:a(g),onSelect:a(F),CloseOnSelect:!0,fixedLabel:!0,label:"Select tracking tag",searchBy:"name",direction:"ltr",placeholder:"Search for Tracking Tags"},null,8,["options","onSelect"])]),t("div",rt,[(i(!0),c(x,null,C(a(f),(o,_)=>(i(),c("div",{key:_,class:"inline-block pr-2 pb-1"},[t("div",lt,[t("span",{class:"text-gray-800 p-0.5 hover:bg-red-500 hover:text-red-50 text-md font-semibold shadow-md cursor-pointer",title:"Click to Remove tracking tag",style:{"background-color":"#dee2ed",color:"#FF391F"},onClick:B=>a(A)(o)},s(o.name),9,it),t("span",{title:"Click to see details",class:"px-0.5 py-0.5 bg-blue-400 hover:text-blue-50 text-md font-medium shadow-md cursor-pointer",style:{"background-color":"#dee2ed",color:"#FF391F"},onClick:B=>a(N)(o)},[p(P,{name:"info2",class:"block h-5 w-5 fill-gray-100"})],8,dt)])]))),128))])],2),t("div",pt,[p(j,{loading:a(r).processing,class:"btn-green",type:"submit"},{default:u(()=>[h(s(e.trans("Update")),1)]),_:1},8,["loading"]),d.patient.deleted_at?m("",!0):(i(),c("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:n[6]||(n[6]=(...o)=>a(V)&&a(V)(...o))},s(e.trans("Delete")),1))])],32)]),t("div",ct,[t("div",ut,[t("h1",mt,s(e.trans("RelatedVisits")),1),d.patient.deleted_at?m("",!0):(i(),b(a(w),{key:0,class:"btn-green",href:e.route("patientsVisits.createNew",d.patient.id)},{default:u(()=>[t("span",null,s(e.trans("RegisterANewVisit")),1)]),_:1},8,["href"]))]),t("table",ht,[t("thead",gt,[t("tr",ft,[_t,t("th",bt,s(e.trans("TestsCount")),1),t("th",wt,s(e.trans("OffersCount")),1),t("th",xt,s(e.trans("TestsCost")),1),t("th",yt,s(e.trans("Discount")),1),t("th",kt,s(e.trans("FinalCost")),1),t("th",vt,s(e.trans("PaidAmount")),1),t("th",Vt,s(e.trans("RemainingAmount")),1),t("th",Tt,s(e.trans("Created_at")),1),t("th",Ct,s(e.trans("Updated_at")),1),t("th",St,s(e.trans("Print")),1),t("th",Ft,s(e.trans("Edit")),1)])]),(i(!0),c(x,null,C(d.patient.visits,(o,_)=>(i(),c("tr",{key:o.id,class:""},[o.deleted_at?(i(),c("td",At,s(_+1)+" this Visit has been deleted..!",1)):(i(),c("td",Nt,s(_+1),1)),t("td",Ut,s(o.tests_count),1),t("td",Bt,s(o.offers_count),1),t("td",Dt,s(o.tests_cost),1),t("td",Lt,s(o.discount),1),t("td",Mt,s(o.final_cost),1),t("td",Et,s(o.paid_amount),1),t("td",It,s(o.remaining_amount),1),t("td",Pt,s(o.created_at),1),t("td",jt,s(o.updated_at),1),t("td",Rt,[!o.deleted_at&&!d.patient.deleted_at?(i(),b(a(w),{key:0,class:"btn-indigo3 bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-300",href:e.route("patientsVisits.print",o.id),tabindex:"-1"},{default:u(()=>[h(s(e.trans("Print")),1)]),_:2},1032,["href"])):m("",!0)]),t("td",Ot,[!o.deleted_at&&!d.patient.deleted_at?(i(),b(a(w),{key:0,class:"btn-indigo3 bg-green-600 hover:bg-green-700 focus:ring-green-400",href:e.route("patientsVisits.editFull",o.id)},{default:u(()=>[t("span",$t,s(e.trans("Edit")),1)]),_:2},1032,["href"])):m("",!0)])]))),128)),d.patient.visits.length===0?(i(),c("tr",zt,Gt)):m("",!0)])])])],64))}});export{oe as default};
