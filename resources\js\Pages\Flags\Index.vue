<template>
<Head title="Flags" />
  <div class="p-4 max-w-7xl ">
    <div class="flex flex-col sm:flex-row mb-6 justify-between items-center">
      <search-filter v-model="filters.search" direction="ltr" placeholder="Search flags..." class="mb-2 sm:mb-0 w-full" @reset="reset">
        <label class="block text-gray-700">Trashed:</label>
        <select v-model="filters.trashed" class="mt-1 w-full form-select">
          <option :value="null" />
          <option value="with">{{ trans('WithTrashed') }}</option>
          <option value="only">{{ trans('OnlyTrashed') }}</option>
        </select>
      </search-filter>
      <Link class="w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium" :href="route('flags.create')">
        <span class="inline sm:hidden">{{ trans('Create')}}</span>
        <span class="hidden sm:flex w-48 text-center">{{ trans('CreateANewFlag') }}</span>
      </Link>
    </div>

    <div class="shadow overflow-x-auto rounded-lg bg-white">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-green-500">
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Name')}}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Created_at')}}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Updated_at')}}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{trans('Edit')}}</th>
          </tr>
        </thead>

        <tr v-for="flag in flags.data" :key="flag.id" class="hover:bg-gray-100 focus-within:bg-gray-100">
          <td class="border-t p-0">
            <Link :href="route('flags.edit', flag.id)" tabindex="-1">
            <div class="px-6 py-2 flex items-center">
              <div class="ml-4">
                <div class="flex items-center text-sm font-medium text-gray-900">
                  {{ flag.name }}
                  <icon v-if="flag.deleted_at" name="trash" class="flex-shrink-0 w-3 h-3 fill-gray-200 ml-2" />
                </div>
              </div>
            </div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('flags.edit', flag.id)" tabindex="-1">
            <div class="px-6 py-2">{{flag.created_at}}</div>
            </Link>
          </td>
          <td class="border-t p-0">
            <Link :href="route('flags.edit', flag.id)" tabindex="-1">
            <div class="px-6 py-2">{{flag.updated_at}}</div>
            </Link>
          </td>
          
          <td class="border-t p-0">
            <Link class="flex items-center  text-right text-sm font-medium" :href="route('flags.edit', flag.id)" tabindex="-1">
            <div class="px-6 py-2">
              <icon name="cheveron-right" class="block w-6 h-6 fill-gray-400" />
            </div>
            </Link>
          </td>
        </tr>
        <tr v-if="flags.data.length === 0">
          <td class="border-t p-0 px-6 py-2" colspan="4">No flags found.</td>
        </tr>
      </table>
      <hr class="bg-gray-300 pt-px">
      <pagination class="px-6 py-2 bg-white border-none border-t p-0" :links="flags.links" :from="flags.from" :to="flags.to" :total="flags.total" />
    </div>
  </div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
  import Icon from '@/MyComponents/Icon.vue'
  import pickBy from "lodash/pickBy"
  import AppLayout from '@/Layouts/AppLayout.vue'
  import throttle from 'lodash/throttle'
  import Pagination from '@/MyComponents/Pagination.vue'
  import SearchFilter from '@/MyComponents/SearchFilter.vue'

  import moment from "moment";
  import { Head, Link } from '@inertiajs/inertia-vue3';

  import { Inertia } from '@inertiajs/inertia';

  import { reactive} from '@vue/reactivity'
  import { watch } from '@vue/runtime-core'

  const props = defineProps({
    filters: Object,
    flags: Object,
  })

  const filters = reactive({
    search: props.filters.search,
    trashed: props.filters.trashed,
  })

  watch(filters, throttle(function () {
    Inertia.get(route('flags'),  pickBy(filters), { preserveState: true, replace: true });
    }, 300),
    { deep: true }
  );

  function reset() {
      Object.assign(filters,{search: null, trashed: ''});
  }
</script>
