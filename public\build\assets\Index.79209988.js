import{v as k,q as v,o as h,c as p,b as a,u as r,a as e,B as n,t as o,F as _,D as C,C as b,H as V,K as I,R as N,L as i,d as f,y as A,U as u}from"./app.5bf25e6f.js";import{A as B,I as D}from"./AppLayout.14f8c8f6.js";import{t as j,S as O,p as S}from"./SearchFilter.f110f3d1.js";import{P as F}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const L={class:"p-4"},M={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},P=e("label",{class:"block text-gray-700"},"Trashed:",-1),R=e("option",{value:null},null,-1),T={value:"with"},U={value:"only"},H={class:"inline sm:hidden"},$={class:"hidden sm:inline-flex"},q={class:"shadow overflow-x-auto rounded-lg bg-white"},E={class:"min-w-full divide-y divide-gray-200"},G={class:"bg-gray-50"},K={class:"bg-green-500"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},X={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Y={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},ee={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},te={class:"border-t"},se={class:"border-t"},oe={class:"border-t"},ae={class:"border-t"},re={class:"border-t"},ne={class:"border-t"},le={class:"border-t"},ie={class:"border-t"},de=["onClick"],ce=["onClick"],he={key:0},fe=e("td",{class:"border-t px-4 py-2",colspan:"4"},"No patients found.",-1),pe=[fe],ue=e("hr",{class:"bg-gray-300 pt-px"},null,-1),me={layout:B},ke=Object.assign(me,{__name:"Index",props:{filters:Object,patients:Object},setup(d){const m=d,c=k({search:m.filters.search,trashed:m.filters.trashed});v(c,j(function(){u.Inertia.get(route("patients"),S(c),{preserveState:!0,replace:!0})},300),{deep:!0});function w(){Object.assign(c,{search:null,trashed:""})}let x=(t,l)=>{confirm("Are you sure you want to delete "+l+" ?")&&u.Inertia.delete(route("patients.destroy",t))},y=(t,l)=>{confirm("Are you sure you want to restore "+l+" ?")&&u.Inertia.put(route("patients.restore",t))};return(t,l)=>(h(),p(_,null,[a(r(V),{title:"Patients"}),e("div",L,[e("div",M,[a(O,{modelValue:c.search,"onUpdate:modelValue":l[1]||(l[1]=s=>c.search=s),direction:"rtl",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0645\u0631\u0627\u062C\u0639\u064A\u0646",class:"mb-2 sm:mb-0 w-full",onReset:w},{default:n(()=>[P,I(e("select",{"onUpdate:modelValue":l[0]||(l[0]=s=>c.trashed=s),class:"mt-1 w-full form-select"},[R,e("option",T,o(t.trans("WithTrashed")),1),e("option",U,o(t.trans("OnlyTrashed")),1)],512),[[N,c.trashed]])]),_:1},8,["modelValue"]),a(r(i),{class:"btn-green",href:t.route("patientsVisits.create_full")},{default:n(()=>[e("span",H,o(t.trans("Create")),1),e("span",$,o(t.trans("RegisterANewPatient")),1)]),_:1},8,["href"])]),e("div",q,[e("table",E,[e("thead",G,[e("tr",K,[e("th",W,o(t.trans("ID")),1),e("th",z,o(t.trans("Name")),1),e("th",J,o(t.trans("Gender")),1),e("th",Q,o(t.trans("Age")),1),e("th",X,o(t.trans("Mobile")),1),e("th",Y,o(t.trans("Created_at")),1),e("th",Z,o(t.trans("Updated_at")),1),e("th",ee,o(t.trans("Delete")),1)])]),(h(!0),p(_,null,C(d.patients.data,s=>(h(),p("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",te,[a(r(i),{class:"px-4 py-2 flex items-center",href:t.route("patients.edit",s.id),tabindex:"-1"},{default:n(()=>[f(o(s.id),1)]),_:2},1032,["href"])]),e("td",se,[a(r(i),{class:"px-4 py-2 flex items-center focus:text-green-500",href:t.route("patients.edit",s.id)},{default:n(()=>[f(o(s.name)+" ",1),s.deleted_at?(h(),A(D,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-400 ml-2"})):b("",!0)]),_:2},1032,["href"])]),e("td",oe,[a(r(i),{class:"px-4 py-2 flex items-center",href:t.route("patients.edit",s.id),tabindex:"-1"},{default:n(()=>[f(o(s.gender===1?t.trans("Male"):s.gender===2?t.trans("Female"):t.trans("Missing")),1)]),_:2},1032,["href"])]),e("td",ae,[a(r(i),{class:"px-4 py-2 flex items-center",href:t.route("patients.edit",s.id),tabindex:"-1"},{default:n(()=>[f(o(s.age),1)]),_:2},1032,["href"])]),e("td",re,[a(r(i),{class:"px-4 py-2 flex items-center",href:t.route("patients.edit",s.id),tabindex:"-1"},{default:n(()=>[f(o(s.mobile),1)]),_:2},1032,["href"])]),e("td",ne,[a(r(i),{class:"px-4 py-2 flex items-center",href:t.route("patients.edit",s.id),tabindex:"-1"},{default:n(()=>[f(o(s.created_at),1)]),_:2},1032,["href"])]),e("td",le,[a(r(i),{class:"px-4 py-2 flex items-center",href:t.route("patients.edit",s.id),tabindex:"-1"},{default:n(()=>[f(o(s.updated_at),1)]),_:2},1032,["href"])]),e("td",ie,[s.deleted_at?(h(),p("button",{key:1,class:"px-4 py-2 flex items-center text-green-600 hover:underline",type:"button",onClick:g=>r(y)(s.id,s.name)},o(t.trans("Restor")),9,ce)):(h(),p("button",{key:0,class:"px-4 py-2 flex items-center text-red-600 hover:underline",type:"button",onClick:g=>r(x)(s.id,s.name)},o(t.trans("Delete")),9,de))])]))),128)),d.patients.data.length===0?(h(),p("tr",he,pe)):b("",!0)]),ue,a(F,{class:"px-6 py-2 bg-white border-none border-t p-0",links:d.patients.links,from:d.patients.from,to:d.patients.to,total:d.patients.total},null,8,["links","from","to","total"])])])],64))}});export{ke as default};
