import{Q as g,o as i,c as m,b as p,u as n,a as t,E as b,t as e,K as f,S as _,B as y,d,z as w,F as v,D as S,H as k}from"./app.5bf25e6f.js";import{_ as T}from"./StatisticsNav.0926be50.js";import{A as D}from"./AppLayout.14f8c8f6.js";import{L as A}from"./LoadingButton.c8fb65b2.js";import{u as O,w as R,a as B}from"./xlsx.0799a57e.js";import{h as E}from"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const F={class:"p-2 xl:p-3"},M={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4"},C={class:"flex flex-wrap"},V={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},L={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},N={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},Y={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},j={id:"reportToPrint",class:"flex overflow-hidden max-w-3xl"},z={class:"flex w-full"},H={id:"table",class:"w-full shadow-lg"},P={class:"ttr"},U={colspan:"2",class:"tth text-center"},$={key:0},G={class:"text-yellow-500"},I={class:"text-yellow-500"},K=d(" )"),Q={key:1},q=d("( "),J={class:"text-yellow-500"},W=d(" )"),X={class:"ttr"},Z={class:"ttr"},tt={colspan:"1",class:"tth text-center"},et={colspan:"1",class:"tth text-center"},st={class:"tth text-left"},rt={class:"ttd bg-gray-300"},ot={layout:D},pt=Object.assign(ot,{__name:"OffersReport",props:{errors:Object,locale:String,ReportDateAndTime:String,start_date:String,end_date:String,offers_array:Array},setup(a){let l=g({start_date:null,end_date:null}),u=()=>{l.post(route("offers_report/fetch"),{preserveState:!0,onSuccess:()=>{}})},x=()=>{setTimeout(function(){window.print()},50)},h=(s,o)=>{var r=document.getElementById("table"),c=O.table_to_book(r,{sheet:"sheet1"});return o?R(c,{bookType:type,bookSST:!0,type:"base64"}):B(c,s||"MySheetName."+(type||"xlsx"))};return(s,o)=>(i(),m("div",F,[p(n(k),{title:"Offers Report"}),t("div",M,[t("form",{onSubmit:o[4]||(o[4]=b((...r)=>n(u)&&n(u)(...r),["prevent"]))},[t("div",C,[t("div",V,[t("span",L,e(s.trans("From"))+": ",1),f(t("input",{"onUpdate:modelValue":o[0]||(o[0]=r=>n(l).start_date=r),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[_,n(l).start_date]])]),t("div",N,[t("span",Y,e(s.trans("To"))+": ",1),f(t("input",{"onUpdate:modelValue":o[1]||(o[1]=r=>n(l).end_date=r),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[_,n(l).end_date]])]),p(A,{loading:n(l).processing,type:"submit",class:"btn-indigo flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3"},{default:y(()=>[d(e(s.trans("Go")),1)]),_:1},8,["loading"]),t("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:o[2]||(o[2]=r=>n(h)("OffersReport.xlsx"))},e(s.trans("ExportToExcel")),1),t("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:o[3]||(o[3]=(...r)=>n(x)&&n(x)(...r))},e(s.trans("Print")),1)])],32),p(T)]),t("div",j,[t("div",z,[t("table",H,[t("tr",P,[t("th",U,[d(e(s.trans("OffersReport"))+" - ",1),a.start_date&&a.end_date?(i(),m("span",$,[d("( "+e(s.trans("From"))+" ",1),t("span",G,e(a.start_date),1),d(" "+e(s.trans("To"))+" ",1),t("span",I,e(a.end_date),1),K])):(i(),m("span",Q,[q,t("span",J,e(s.trans("ThisMonth")),1),W]))])]),t("tr",X,[t("th",{class:w(["tth",a.locale=="ar"?"rtl text-right":"ltr text-left"]),locale:"",colspan:"3"},e(s.trans("ReportDateAndTime"))+": "+e(n(E)(a.ReportDateAndTime).format("YYYY-MM-DD h:m A")),3)]),t("tr",Z,[t("th",tt,e(s.trans("Offer")),1),t("th",et,e(s.trans("Count")),1)]),(i(!0),m(v,null,S(a.offers_array,(r,c)=>(i(),m("tr",{class:"ttr",key:r.name},[t("th",st,e(c+1+" - "+r.name),1),t("td",rt,e(r.count),1)]))),128))])])])]))}});export{pt as default};
