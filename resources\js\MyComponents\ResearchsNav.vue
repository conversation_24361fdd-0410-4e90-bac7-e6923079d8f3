<template>
<nav class="flex justify-start items-center">
    <Link @click="sidebarOpened = false" v-for="(item, index) in Navigation" :key="index" :href="route(item.route)" class="flex items-center group py-3 mr-1 xl:mr-2"
    :class="$page.url == item.url? 'text-yellow-400' : 'text-indigo-200 group-hover:text-orange-500 hover:text-orange-500'">
    <icon :name="item.icon" class="w-5 h-5 mx-1" :class="$page.url == item.url? 'fill-white' :  'fill-indigo-400 group-hover:fill-white'" />{{ trans(item.label) }}
    </Link>
</nav>
</template>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import JetNavLink from '@/Jetstream/NavLink.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, <PERSON>, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'

let Navigation = [
    {route: 'researchs.VisitsIndex', url:'/researchs/VisitsIndex', label: 'Visits', icon: 'visit'},
];

  let tabletoExcel = () => {
        var uri = 'data:application/vnd.ms-excel;base64,'
              , template = '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--><meta http-equiv="content-type" content="text/plain; charset=UTF-8"/></head><body><table>{table}</table></body></html>'
              , base64 = function (s) { return window.btoa(unescape(encodeURIComponent(s))); }
              , format = function (s, c) { return s.replace(/{(\w+)}/g, function (m, p) { return c[p]; }); };
            if (!table.nodeType) table = document.getElementById('table');
            var ctx = { worksheet: 'Patients_Statistics' || 'Worksheet', table: table.innerHTML };
            window.location.href = uri + base64(format(template, ctx));

    };
</script>

