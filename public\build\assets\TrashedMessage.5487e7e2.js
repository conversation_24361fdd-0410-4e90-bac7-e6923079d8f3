import{I as n}from"./AppLayout.14f8c8f6.js";import{_ as r}from"./plugin-vue_export-helper.21dcd24c.js";import{r as a,o as l,c as i,a as t,b as c,A as m,t as d}from"./app.5bf25e6f.js";const p={components:{Icon:n}},f={class:"p-4 bg-yellow-300 rounded flex items-center justify-between max-w-3xl"},_={class:"flex items-center"},u={class:"text-sm font-medium text-yellow-800"};function x(e,s,h,v,w,y){const o=a("icon");return l(),i("div",f,[t("div",_,[c(o,{name:"trash",class:"flex-shrink-0 w-4 h-4 fill-yellow-800 mr-2"}),t("div",u,[m(e.$slots,"default")])]),t("button",{class:"text-sm text-yellow-800 hover:underline",tabindex:"-1",type:"button",onClick:s[0]||(s[0]=$=>e.$emit("restore"))},d(e.trans("Restore")),1)])}var B=r(p,[["render",x]]);export{B as T};
