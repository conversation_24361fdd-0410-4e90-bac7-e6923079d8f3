import{v as g,q as w,o as c,c as p,b as a,u as d,a as t,B as i,t as o,F as f,D as b,C as _,H as y,K as v,R as k,L as n,d as V,y as C,U as N}from"./app.5bf25e6f.js";import{A as j,I as m}from"./AppLayout.14f8c8f6.js";import{t as B,S,p as O}from"./SearchFilter.f110f3d1.js";import{P as T}from"./Pagination.b9f6e44a.js";import"./plugin-vue_export-helper.21dcd24c.js";const I={class:"p-4 max-w-7xl"},L={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},U=t("label",{class:"block text-gray-700"},"Trashed:",-1),A=t("option",{value:null},null,-1),D={value:"with"},F={value:"only"},E={class:"inline sm:hidden"},G={class:"hidden sm:flex w-48 text-center"},H={class:"shadow overflow-x-auto rounded-lg bg-white"},P={class:"min-w-full divide-y divide-gray-200"},R={class:"bg-gray-50"},q={class:"bg-green-500"},K={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},M={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={class:"border-t p-0"},X={class:"px-6 py-2 flex items-center"},Y={class:"ml-4"},Z={class:"flex items-center text-sm font-medium text-gray-900"},$={class:"border-t p-0"},tt={class:"px-6 py-2"},et={class:"border-t p-0"},st={class:"px-6 py-2"},ot={class:"border-t p-0"},at={class:"px-6 py-2"},rt={class:"border-t p-0"},lt={class:"px-6 py-2"},dt={key:0},it=t("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No groups found.",-1),nt=[it],ct=t("hr",{class:"bg-gray-300 pt-px"},null,-1),ht={layout:j},xt=Object.assign(ht,{__name:"Index",props:{filters:Object,groups:Object},setup(r){const u=r,l=g({search:u.filters.search,trashed:u.filters.trashed});w(l,B(function(){N.Inertia.get(route("groups"),O(l),{preserveState:!0,replace:!0})},300),{deep:!0});function x(){Object.assign(l,{search:null,trashed:null})}return(e,h)=>(c(),p(f,null,[a(d(y),{title:"Groups"}),t("div",I,[t("div",L,[a(S,{modelValue:l.search,"onUpdate:modelValue":h[1]||(h[1]=s=>l.search=s),direction:"ltr",placeholder:"Search for groups.....",class:"mb-2 sm:mb-0 w-full",onReset:x},{default:i(()=>[U,v(t("select",{"onUpdate:modelValue":h[0]||(h[0]=s=>l.trashed=s),class:"mt-1 w-full form-select"},[A,t("option",D,o(e.trans("WithTrashed")),1),t("option",F,o(e.trans("OnlyTrashed")),1)],512),[[k,l.trashed]])]),_:1},8,["modelValue"]),a(d(n),{class:"w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium",href:e.route("groups.create")},{default:i(()=>[t("span",E,o(e.trans("Create")),1),t("span",G,o(e.trans("CreateANewGroup")),1)]),_:1},8,["href"])]),t("div",H,[t("table",P,[t("thead",R,[t("tr",q,[t("th",K,o(e.trans("Name")),1),t("th",M,o(e.trans("TestsCount")),1),t("th",W,o(e.trans("Created_at")),1),t("th",z,o(e.trans("Updated_at")),1),t("th",J,o(e.trans("Edit")),1)])]),(c(!0),p(f,null,b(r.groups.data,s=>(c(),p("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[t("td",Q,[a(d(n),{href:e.route("groups.edit",s.id),tabindex:"-1"},{default:i(()=>[t("div",X,[t("div",Y,[t("div",Z,[V(o(s.name)+" ",1),s.deleted_at?(c(),C(m,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):_("",!0)])])])]),_:2},1032,["href"])]),t("td",$,[a(d(n),{href:e.route("groups.edit",s.id),tabindex:"-1"},{default:i(()=>[t("div",tt,o(s.tests_count),1)]),_:2},1032,["href"])]),t("td",et,[a(d(n),{href:e.route("groups.edit",s.id),tabindex:"-1"},{default:i(()=>[t("div",st,o(s.created_at),1)]),_:2},1032,["href"])]),t("td",ot,[a(d(n),{href:e.route("groups.edit",s.id),tabindex:"-1"},{default:i(()=>[t("div",at,o(s.updated_at),1)]),_:2},1032,["href"])]),t("td",rt,[a(d(n),{class:"flex items-center text-right text-sm font-medium",href:e.route("groups.edit",s.id),tabindex:"-1"},{default:i(()=>[t("div",lt,[a(m,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),r.groups.data.length===0?(c(),p("tr",dt,nt)):_("",!0)]),ct,a(T,{class:"px-6 py-2 bg-white border-none border-t p-0",links:r.groups.links,from:r.groups.from,to:r.groups.to,total:r.groups.total},null,8,["links","from","to","total"])])])],64))}});export{xt as default};
