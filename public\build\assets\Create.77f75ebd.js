import{Q as b,o as _,c as y,b as a,u as e,a as r,B as n,d as i,t as d,E as x,z as w,F as V,H as h,L as C}from"./app.5bf25e6f.js";import{A as v}from"./AppLayout.14f8c8f6.js";import{_ as m}from"./TextInput.48e8e32c.js";import{_ as k}from"./SelectInput.16ffd220.js";import{L as c}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";const A={class:"p-2 sm:p-4"},L={class:"mb-2 font-bold text-2xl"},S=r("span",{class:"text-indigo-400 font-medium"}," /",-1),B={class:"bg-white rounded-md shadow overflow-hidden max-w-xl"},D=r("option",{value:null},null,-1),F=r("option",{value:"1"},"Male",-1),N=r("option",{value:"2"},"Female",-1),U={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},M={layout:v},O=Object.assign(M,{__name:"Create",props:{locale:String},setup(f){let t=b({createAnother:!1,name:null,gender:null,mobile:null,specialty:null}),u=()=>{t.createAnother=!0,p()},g=()=>{t.createAnother=!1,p()},p=()=>{t.post(route("doctors.store"),{preserveState:!0,onSuccess:()=>{t.reset()}})};return(s,o)=>(_(),y(V,null,[a(e(h),{title:"Create Doctor"}),r("div",A,[r("h2",L,[a(e(C),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("doctors")},{default:n(()=>[i(d(s.trans("DoctorsList")),1)]),_:1},8,["href"]),S,i(" "+d(s.trans("Create")),1)]),r("div",B,[r("form",{onSubmit:o[4]||(o[4]=x((...l)=>e(u)&&e(u)(...l),["prevent"]))},[r("div",{class:w(["p-8 -mr-6 -mb-8 flex flex-wrap",f.locale=="ar"?"rtl text-right":"ltr text-left"])},[a(m,{modelValue:e(t).name,"onUpdate:modelValue":o[0]||(o[0]=l=>e(t).name=l),error:e(t).errors.name,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),a(k,{modelValue:e(t).gender,"onUpdate:modelValue":o[1]||(o[1]=l=>e(t).gender=l),error:e(t).errors.gender,class:"pr-6 pb-8 w-full lg:w-1/2",label:"Gender"},{default:n(()=>[D,F,N]),_:1},8,["modelValue","error"]),a(m,{modelValue:e(t).mobile,"onUpdate:modelValue":o[2]||(o[2]=l=>e(t).mobile=l),error:e(t).errors.mobile,class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Mobile"},null,8,["modelValue","error"]),a(m,{modelValue:e(t).specialty,"onUpdate:modelValue":o[3]||(o[3]=l=>e(t).specialty=l),error:e(t).errors.specialty,direction:"rtl",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Specialty"},null,8,["modelValue","error"])],2),r("div",U,[a(c,{loading:e(t).processing,class:"mr-4 px-3 py-2 btn-green",onClick:e(u)},{default:n(()=>[i(d(s.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),a(c,{loading:e(t).processing,class:"px-3 py-2 btn-green",onClick:e(g)},{default:n(()=>[i(d(s.trans("Create")+" "+s.trans("Doctor")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{O as default};
