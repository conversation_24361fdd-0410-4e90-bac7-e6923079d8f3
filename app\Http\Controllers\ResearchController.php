<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use App\Models\Visit;
use App\Models\TrackingTag;
use App\Models\Test;

class ResearchController extends Controller
{

    public function VisitsIndex()
    {
        $per_page = 10;
        if (Request::filled('test_id')) {
            Request::validate([
                'test_min_value' => ['required'],
                'test_max_value'   => ['required'],
            ]);
        }
        if (Request::filled('age_min') || Request::filled('age_max')) {
            Request::validate([
                'age_min' => ['required', 'digits_between:1,3'],
                'age_max' => ['required', 'digits_between:1,3'],
            ]);
        }

        if (Request::filled('per_page')) {
            $per_page = Request::input('per_page');
        }

        return Inertia::render('Researchs/VisitsIndex', [
            'tests'           => Test::orderBy('sequence', 'asc')->get()->map->only('id', 'short_name'),
            'tracking_tags'   => TrackingTag::orderBy('id', 'desc')->get()->map->only('id', 'name'),
            'filters'         => Request::all('per_page', 'age_min', 'age_max', 'tracking_tag_id', 'test_id', 'test_min_value', 'test_max_value', 'gender', 'smoking', 'marital', 'start_date', 'end_date'),
            'visits'          => Visit::with('patient')->orderBy('patient_id', 'desc')
                ->filter(Request::only('per_page', 'age_min', 'age_max', 'tracking_tag_id', 'test_id', 'test_min_value', 'test_max_value', 'gender', 'smoking', 'marital', 'start_date', 'end_date'))
                ->paginate($per_page)
                ->withQueryString()
                ->through(fn ($visit) => [
                    'id'                => $visit->id,
                    'name'              => $visit->patient ? $visit->patient->name : 'Deleted Patient',
                    'patient_id'        => $visit->patient ? $visit->patient->id  : 'Deleted Patient',
                    'gender'            => $visit->patient ? $visit->patient->gender : 'Unknown',
                    'tracking_tags'     => $visit->patient ? $visit->patient->tracking_tags()->orderBy('name')->get()->map->only('id', 'name') : 'None',
                    'age'               => $visit->patient ? $visit->patient->age : 'Unknown',
                    'tests'             => $visit->tests()->orderBy('sequence')->get()->map->only('id', 'short_name', 'pivot'),
                    'created_at'        => $visit->created_at,
                    'created_at'        => $visit->created_at,
                ]),
        ]);
    }
}
