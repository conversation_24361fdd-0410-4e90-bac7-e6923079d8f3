<template>
<Head title="Edit Patient" />
<div class="p-4">
    <div class="mb-2 flex justify-start max-w-xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('patients')">{{ trans('PatientsList') }}</Link>
        <span class="text-indigo-400 font-medium"> /</span>
        {{ form.name }}
      </h2>
    </div>
    
    <div class="bg-white rounded-md shadow-sm  max-w-xl">
      <trashed-message v-if="patient.deleted_at" class="mb-6" @restore="restore">
        {{ trans('This') + ' ' +  trans('Patient') + ' ' + trans('HasBeenDeleted')}}
      </trashed-message>
      <form @submit.prevent="update">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" direction="rtl" class="pr-6 pb-4 w-full lg:w-1/2" type="text" label="Name" :autofocus="true" />
          <select-input v-model.number="form.gender" :error="form.errors.gender" class="pr-6 pb-4 w-full lg:w-1/2" label="Gender">
            <option :value="null" />
            <option value="1">{{trans('Male')}}</option>
            <option value="2">{{trans('Female')}}</option>
          </select-input>
          <text-input v-model="form.age" :error="form.errors.age" direction="ltr" class="pr-6 pb-4 w-full lg:w-1/2" type="text" label="Age" /> 
          <text-input v-model.number="form.mobile" :error="form.errors.mobile" direction="ltr"  class="pr-6 pb-4 w-full lg:w-1/2" type="text" label="Mobile" />
          <select-input v-model="form.marital" :error="errors.marital" class="pr-6 pb-4 w-full lg:w-1/2" label="MaritalStatus">
                <option :value="null" />
                <option value="1">{{trans('Single')}}</option>
                <option value="2">{{trans('Married')}}</option>
                <option value="3">{{trans('Divorced')}}</option>
                <option value="4">{{trans('Widowed')}}</option>
            </select-input>

            <select-input v-model="form.smoking" :error="errors.smoking" class="pr-6 pb-4 w-full lg:w-1/2" label="Smoking">
                <option :value="null" />
                <option value="1">{{trans('Smoker')}}</option>
                <option value="2">{{trans('NonSmoker')}}</option>
                <option value="3">{{trans('ExSmoker')}}</option>
            </select-input>
            <div class="mt-2 z-30 pr-6 pb-2 w-full">
            <dropdown-search class="w-full ltr" :options="ourTrackingTags" @select="addTrackingTag" 
              :CloseOnSelect="true" :fixedLabel="true" label="Select tracking tag" searchBy="name" :direction="'ltr'" placeholder="Search for Tracking Tags">
            </dropdown-search>
            </div>
            <div class="pr-6 pb-4 w-full">
          <div v-for="(tracking_tag, i) in ourPatientTrackingTags" :key="i" class="inline-block pr-2 pb-1">
            <div class="flex mt-1">
              <span class="text-gray-800 p-0.5 hover:bg-red-500 hover:text-red-50 text-md font-semibold shadow-md cursor-pointer"
                title="Click to Remove tracking tag" style="background-color:#dee2ed; color:#FF391F" @click="detachTrackingTag(tracking_tag)">{{ tracking_tag.name }}</span>
                <span title="Click to see details" class="px-0.5 py-0.5 bg-blue-400 hover:text-blue-50 text-md font-medium shadow-md cursor-pointer" style="background-color:#dee2ed; color:#FF391F" @click="getTrackingTagInfo(tracking_tag)">
                <icon name="info2" class="block h-5 w-5 fill-gray-100" /></span>
            </div>
          </div>
            </div>

        </div>
        <div class="px-4 py-2 bg-gray-100 border-t border-gray-100 flex items-center justify-between">
          <loading-button :loading="form.processing" class="btn-green" type="submit">{{trans('Update')}}</loading-button>
          <button v-if="!patient.deleted_at" class="text-red-600 hover:underline" tabindex="-1" type="button" @click="destroy">{{trans('Delete')}}</button>
        </div>
      </form>
    </div>

    <div class="shadow overflow-x-auto rounded-lg bg-white mt-4 max-w-7xl">
      <div class="flex justify-between py-2 px-4 items-center">
        <h1 class="text-left text-gray-900 font-semibold tracking-wider"> {{trans('RelatedVisits') }} </h1>
        <Link v-if="!patient.deleted_at" class="btn-green" :href="route('patientsVisits.createNew', patient.id)">
          <span>{{ trans('RegisterANewVisit') }}</span>
        </Link>
      </div>
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr class="bg-green-500">
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"># </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('TestsCount') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('OffersCount') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('TestsCost') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Discount') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('FinalCost') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('PaidAmount') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('RemainingAmount') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Created_at') }}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Updated_at') }}</th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap">{{ trans('Print') }} </th>
            <th scope="col" class="px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"> {{trans('Edit')}} </th>
            <!-- <th scope="col" class="px-2 py-4 text-sm font-medium text-gray-50 whitespace-nowrap">{{ trans('Delete') + ' / ' + trans('Restore')}} </th> -->
          </tr>
        </thead>
        <tr v-for="(visit, index) in patient.visits" :key="visit.id" class="">
          <td v-if="visit.deleted_at" class="border-t px-2 py-2 flex items-center text-red-500"> {{ index+1 }} this Visit has been deleted..!</td>
          <td v-else class="border-t px-2 py-2"> {{ index+1 }} </td>
          <td class="border-t px-2 py-2"> {{ visit.tests_count }} </td>
          <td class="border-t px-2 py-2"> {{ visit.offers_count }} </td>
          <td class="border-t px-2 py-2"> {{ visit.tests_cost }} </td>
          <td class="border-t px-2 py-2"> {{ visit.discount }} </td>
          <td class="border-t px-2 py-2"> {{ visit.final_cost }} </td>
          <td class="border-t px-2 py-2"> {{ visit.paid_amount }} </td>
          <td class="border-t px-2 py-2"> {{ visit.remaining_amount }} </td>
          <td class="border-t px-2 py-2"> {{ visit.created_at }} </td>
          <td class="border-t px-2 py-2"> {{ visit.updated_at }} </td>


          <td class="border-t px-2 py-2">
            <Link v-if="!visit.deleted_at && !patient.deleted_at" class="btn-indigo3 bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-300" :href="route('patientsVisits.print', visit.id)" tabindex="-1">
              {{trans('Print') }}
            </Link>
          </td>
          <td class="border-t px-2 py-2"> 
            <Link v-if="!visit.deleted_at && !patient.deleted_at" class="btn-indigo3 bg-green-600 hover:bg-green-700 focus:ring-green-400" :href="route('patientsVisits.editFull', visit.id)">
              <span class="">{{ trans('Edit')}}</span>
            </Link>          
          </td>
          <!-- <td v-if="!visit.deleted_at" class="border-t px-2 py-2">
            <button class="btn-indigo3 bg-red-600 hover:bg-red-700 focus:ring-red-400"  type="button" @click="destroyVisit(visit.id)">{{ trans('Delete') }} </button>
          </td>
          <td v-else class="border-t px-2 py-2">
            <button class=" text-green-600 hover:underline"  type="button" @click="restoreVisit(visit.id)">{{ trans('Restore') }} </button>
          </td> -->
        </tr>
        <tr v-if="patient.visits.length === 0">
          <td class="border-t p-0 px-6 py-2" colspan="4">No visits found.</td>
        </tr>
      </table>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
import Icon from '@/MyComponents/Icon.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import DropdownSearch from '@/MyComponents/DropdownSearch.vue' 
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'
import moment from 'moment'
import { reactive } from '@vue/reactivity'


const props = defineProps({
   errors: Object,
  locale: String,
  patient: Object,
  tracking_tags: Array,
  patientTrackingTags: Array,
});

let ourTrackingTags  = reactive(props.tracking_tags.slice());
let ourPatientTrackingTags  = reactive(props.patientTrackingTags.slice());

let addTrackingTag = (selectedTrackingTag) => {
  axios.post('/sync_tracking_tag', {
      tracking_tag_id: selectedTrackingTag.id,
      patient_id: props.patient.id
    })
    .then((response) => {
      ourPatientTrackingTags.push(selectedTrackingTag);
      ourTrackingTags.splice(ourTrackingTags.findIndex(tracking_tag => tracking_tag.id === selectedTrackingTag.id), 1);
    })
    .catch((error) => console.log(error))
};

let detachTrackingTag = (tracking_tag) =>{
  if (confirm('Are you sure you want to detach this tracking_tag.?')) {
  axios.post('/detach_tracking_tag', {
    tracking_tag_id: tracking_tag.id,
    patient_id: props.patient.id,
  })
  .then((response) => {
    console.log(response.data)
      ourTrackingTags.push(tracking_tag);
      ourPatientTrackingTags.splice(ourPatientTrackingTags.findIndex(ourPatientTrackingTag => ourPatientTrackingTag.id === tracking_tag.id), 1);
  })
  .catch((error) => {
    alert(error.response.data.message);
  })
  }
};

let getTrackingTagInfo = (tracking_tag) =>{
  axios.post('/get_tracking_tag_info', {
    tracking_tag_id: tracking_tag.id,
    patient_id: props.patient.id,
  })
  .then((response) => {

    alert(response.data)
  })
  .catch((error) => {
    alert(error.response.data.message);
  })
};

let form = useForm({
  // _token: document.head.querySelector('meta[name="csrf_token"]').content,
    _method: 'put',
    name: props.patient.name,
    gender: props.patient.gender,
    mobile: props.patient.mobile,
    age: props.patient.age,
    marital:  props.patient.marital,
    smoking: props.patient.smoking,
});

let update = () => {
    form.post(route('patients.update', props.patient.id))
};

let destroy = () => {
    if (confirm('Are you sure you want to delete this patient ?')) {
    Inertia.delete(route('patients.destroy', props.patient.id))
    }
};

let restore = () => {
    if (confirm('Are you sure you want to restore this patient ?')) {
    Inertia.put(route('patients.restore', props.patient.id))
    }
};

let destroyVisit = (visit_id) => {
    if (confirm('Are you sure you want to destroy this visit ?')) {
      Inertia.delete(route('visits.destroy', visit_id))
    }
};

let restoreVisit = (visit_id) => {
  if (confirm('Are you sure you want to restore this visit ? ')) {
    Inertia.put(route('visits.restore', visit_id))
  }
};
</script>