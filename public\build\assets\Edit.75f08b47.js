import{Q as x,o as u,c,b as d,u as e,a,B as f,d as m,t as l,y as w,C as b,E as _,z as v,F,H as k,L as B,U as y}from"./app.5bf25e6f.js";import{A as V}from"./AppLayout.14f8c8f6.js";import{_ as L}from"./TextInput.48e8e32c.js";import{L as C}from"./LoadingButton.c8fb65b2.js";import{T as N}from"./TrashedMessage.5487e7e2.js";import"./plugin-vue_export-helper.21dcd24c.js";const j={class:"p-2 sm:p-4"},A={class:"mb-2 flex justify-start max-w-xl"},E={class:"font-bold text-2xl"},S=a("span",{class:"text-indigo-400 font-medium"}," /",-1),T={class:"bg-white rounded-md shadow-sm overflow-hidden max-w-xl"},D={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},H={layout:V},Q=Object.assign(H,{__name:"Edit",props:{locale:String,flag:Object},setup(n){const i=n;let s=x({_method:"put",name:i.flag.name}),g=()=>{s.post(route("flags.update",i.flag.id),{onSuccess:()=>s.reset()})},p=()=>{confirm("Are you sure you want to delete this flag?")&&y.Inertia.delete(route("flags.destroy",i.flag.id))},h=()=>{confirm("Are you sure you want to restore this flag?")&&y.Inertia.put(route("flags.restore",i.flag.id))};return(t,o)=>(u(),c(F,null,[d(e(k),{title:"Edit Flag"}),a("div",j,[a("div",A,[a("h2",E,[d(e(B),{class:"text-indigo-400 hover:text-indigo-600",href:t.route("flags")},{default:f(()=>[m(l(t.trans("FlagsList")),1)]),_:1},8,["href"]),S,m(" "+l(e(s).name),1)])]),a("div",T,[n.flag.deleted_at?(u(),w(N,{key:0,class:"mb-6",onRestore:e(h)},{default:f(()=>[m(l(t.trans("This")+" "+t.trans("Flag")+" "+t.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):b("",!0),a("form",{onSubmit:o[2]||(o[2]=_((...r)=>e(g)&&e(g)(...r),["prevent"]))},[a("div",{class:v(["p-8 -mr-6 -mb-8 flex flex-wrap",n.locale=="ar"?"rtl text-right":"ltr text-left"])},[d(L,{modelValue:e(s).name,"onUpdate:modelValue":o[0]||(o[0]=r=>e(s).name=r),error:e(s).errors.name,type:"text",class:"pr-6 pb-8 w-full",direction:"ltr",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),a("div",D,[d(C,{loading:e(s).processing,class:"btn-green",type:"submit"},{default:f(()=>[m(l(t.trans("Update")+" "+t.trans("Flag")),1)]),_:1},8,["loading"]),n.flag.deleted_at?b("",!0):(u(),c("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:o[1]||(o[1]=(...r)=>e(p)&&e(p)(...r))},l(t.trans("Delete")+" "+t.trans("Flag")),1))])],32)])])],64))}});export{Q as default};
