import{r as n,o as i,c as a,a as r,C as m,b as l,B as p,d as o,t as h,E as c,F as w}from"./app.5bf25e6f.js";import{_ as b}from"./plugin-vue_export-helper.21dcd24c.js";const g={},V=o(" // "),v=o(" // "),y=["src"],U=o(" // "),k={class:"mb-8 flex justify-start max-w-3xl"},B=o(" // "),C={class:"font-bold text-3xl"},E=o(" // "),N=o("Users"),$=o(" // "),P=r("span",{class:"text-indigo-400 font-medium"},"/",-1),F=o(" // "),S=["src"],j=o(" // "),D=o(" //  // "),M={class:"bg-white rounded-md shadow overflow-hidden max-w-3xl"},T=o(" // "),q=o(" // "),x={class:"p-8 -mr-6 -mb-8 flex flex-wrap"},z=o(" // "),A=o(" // "),G=o(" // "),H=o(" //  // "),I=o(" // "),J=o(" // "),K={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center"},L=o(" //  // "),O=o("Update User"),Q=o(" // "),R=o(" // "),W=o(" // "),X=o(" // "),Y=o(" // ");function Z(e,s,oo,eo,so,to){const _=n("inertia-link"),d=n("text-input"),u=n("file-input"),f=n("loading-button");return i(),a(w,null,[V,r("div",null,[v,e.user.photo?(i(),a("img",{key:0,src:e.user.photo},null,8,y)):m("",!0),U,r("div",k,[B,r("h1",C,[E,l(_,{class:"text-indigo-400 hover:text-indigo-600",href:e.route("users")},{default:p(()=>[N]),_:1},8,["href"]),$,P,o(" // "+h(e.form.name)+" // ",1)]),F,e.user.photo?(i(),a("img",{key:0,class:"block w-8 h-8 rounded-full ml-4",src:e.user.photo},null,8,S)):m("",!0),j]),D,r("div",M,[T,r("form",{onSubmit:s[4]||(s[4]=c((...t)=>e.update&&e.update(...t),["prevent"]))},[q,r("div",x,[z,l(d,{modelValue:e.form.name,"onUpdate:modelValue":s[0]||(s[0]=t=>e.form.name=t),error:e.form.errors.name,class:"pr-6 pb-8 w-full lg:w-1/2",label:"User name"},null,8,["modelValue","error"]),A,l(d,{modelValue:e.form.email,"onUpdate:modelValue":s[1]||(s[1]=t=>e.form.email=t),error:e.form.errors.email,class:"pr-6 pb-8 w-full lg:w-1/2",label:"Email"},null,8,["modelValue","error"]),G,l(d,{modelValue:e.form.password,"onUpdate:modelValue":s[2]||(s[2]=t=>e.form.password=t),error:e.form.errors.password,class:"pr-6 pb-8 w-full lg:w-1/2",type:"password",autocomplete:"new-password",label:"Password"},null,8,["modelValue","error"]),H,l(u,{modelValue:e.form.photo,"onUpdate:modelValue":s[3]||(s[3]=t=>e.form.photo=t),error:e.form.errors.photo,class:"pr-6 pb-8 w-full lg:w-1/2",type:"file",accept:"image/*",label:"Photo"},null,8,["modelValue","error"]),I]),J,r("div",K,[L,l(f,{loading:e.form.processing,class:"btn-indigo ml-auto",type:"submit"},{default:p(()=>[O]),_:1},8,["loading"]),Q]),R],32),W]),X]),Y],64)}var no=b(g,[["render",Z]]);export{no as default};
