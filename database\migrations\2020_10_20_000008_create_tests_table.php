<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTestsTable extends Migration
{

    public function up()
    {
        Schema::create('tests', function (Blueprint $table) {
            $table->id();
            $table->string('short_name')->unique();
            $table->string('full_name')->unique()->nullable();
            $table->tinyInteger('result_type');
            $table->integer('lab_to_patient_price')->nullable();
            $table->integer('lab_to_lab_price')->nullable();
            $table->integer('sequence')->nullable();

            // $table->unsignedBigInteger('default_device_id');

            $table->foreignId('user_id')->nullable()->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('category_id')->nullable()->constrained()->onDelete('restrict')->onUpdate('cascade');

            $table->foreignId('default_device_id')->nullable()->constrained('devices')->onDelete('restrict')->onUpdate('cascade');

            // $table->foreign('default_device_id')->references('id')->on('devices')
            //     ->nullable()->onDelete('set null')->onUpdate('cascade');

            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('tests');
    }
}
