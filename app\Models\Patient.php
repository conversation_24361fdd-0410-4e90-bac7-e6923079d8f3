<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class Patient extends Model
{
    use HasFactory;
    use SoftDeletes;

    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? 'id', $value)->withTrashed()->firstOrFail();
    }

    protected $fillable = ['id', 'name', 'gender', 'user_id', 'age', 'age_type', 'mobile', 'marital', 'smoking', 'created_at', 'updated_at'];

    protected $dates = ['deleted_at'];

    public function scopeOrderByName($query)
    {
        $query->orderBy('name');
    }

    public function scopeFilter($query, array $filters)
    {
        $query

            ->when($filters['search'] ?? null, function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('id', '=', $search)
                        ->orWhere('name', 'like', $search . '%');
                });
            })

            ->when($filters['trashed'] ?? null, function ($query, $trashed) {
                if ($trashed === 'with') {
                    $query->withTrashed();
                } elseif ($trashed === 'only') {
                    $query->onlyTrashed();
                }
            })

            ->when($filters['sentToday'] ?? null, function ($query) {
                $query->whereHas('visits', function ($query) {
                    $query->whereDate('created_at', date('Y-m-d'));
                });
            })

            ->when($filters['gender'] ?? null, function ($query, $gender) {
                $query->where('gender', '=', $gender);
            })

            ->when($filters['start_date'] ?? null, function ($query, $start_date) {
                $query->whereHas('visits', function ($query) use ($start_date) {
                    $query->whereDate('created_at', '>=', $start_date . ' 00:00:00');
                });
            })

            ->when($filters['end_date'] ?? null, function ($query, $end_date) {
                $query->whereHas('visits', function ($query) use ($end_date) {
                    $query->whereDate('created_at', '<=', $end_date . ' 23:59:59');
                });
            });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function visits()
    {
        return $this->hasMany(Visit::class);
    }

    public function tracking_tags()
    {
        return $this->belongsToMany(TrackingTag::class)->withTimestamps();
    }
}
