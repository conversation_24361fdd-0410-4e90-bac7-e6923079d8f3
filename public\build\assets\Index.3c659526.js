import{v as b,q as w,o as n,c as f,b as a,u as d,a as e,B as l,t as o,F as u,D as g,C as m,H as y,K as v,R as k,L as c,d as V,y as N,U as j}from"./app.5bf25e6f.js";import{A as B,I as _}from"./AppLayout.14f8c8f6.js";import{t as C,S as D,p as S}from"./SearchFilter.f110f3d1.js";import{P as O}from"./Pagination.b9f6e44a.js";import"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const I={class:"p-4 max-w-7xl"},L={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},T=e("label",{class:"block text-gray-700"},"Trashed:",-1),U=e("option",{value:null},null,-1),A=e("option",{value:"with"},"With Trashed",-1),F=e("option",{value:"only"},"Only Trashed",-1),E=[U,A,F],H={class:"inline sm:hidden"},P={class:"hidden sm:flex w-48 text-center"},R={class:"shadow overflow-x-auto rounded-lg bg-white"},q={class:"min-w-full divide-y divide-gray-200"},K={class:"bg-gray-50"},M={class:"bg-green-500"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},G={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={class:"border-t p-0"},X={class:"px-6 py-2 flex items-center"},Y={class:"ml-4"},Z={class:"flex items-center text-sm font-medium text-gray-900"},$={class:"border-t p-0"},ee={class:"px-6 py-2"},te={class:"border-t p-0"},se={class:"px-6 py-2"},ae={class:"border-t p-0"},oe={class:"px-6 py-2"},re={key:0},ie=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No devices found.",-1),de=[ie],le=e("hr",{class:"bg-gray-300 pt-px"},null,-1),ne={layout:B},_e=Object.assign(ne,{__name:"Index",props:{filters:Object,devices:Object},setup(r){const p=r,i=b({search:p.filters.search,trashed:p.filters.trashed});w(i,C(function(){j.Inertia.get(route("devices"),S(i),{preserveState:!0,replace:!0})},300),{deep:!0});function x(){Object.assign(i,{search:null,trashed:""})}return(s,h)=>(n(),f(u,null,[a(d(y),{title:"Devices"}),e("div",I,[e("div",L,[a(D,{modelValue:i.search,"onUpdate:modelValue":h[1]||(h[1]=t=>i.search=t),direction:"ltr",placeholder:"Search Devices...",class:"mb-2 sm:mb-0 w-full",onReset:x},{default:l(()=>[T,v(e("select",{"onUpdate:modelValue":h[0]||(h[0]=t=>i.trashed=t),class:"mt-1 w-full form-select"},E,512),[[k,i.trashed]])]),_:1},8,["modelValue"]),a(d(c),{class:"w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium",href:s.route("devices.create")},{default:l(()=>[e("span",H,o(s.trans("Create")),1),e("span",P,o(s.trans("CreateANewDevice")),1)]),_:1},8,["href"])]),e("div",R,[e("table",q,[e("thead",K,[e("tr",M,[e("th",W,o(s.trans("Name")),1),e("th",z,o(s.trans("Created_at")),1),e("th",G,o(s.trans("Updated_at")),1),e("th",J,o(s.trans("Edit")),1)])]),(n(!0),f(u,null,g(r.devices.data,t=>(n(),f("tr",{key:t.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",Q,[a(d(c),{href:s.route("devices.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",X,[e("div",Y,[e("div",Z,[V(o(t.name)+" ",1),t.deleted_at?(n(),N(_,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):m("",!0)])])])]),_:2},1032,["href"])]),e("td",$,[a(d(c),{href:s.route("devices.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",ee,o(t.created_at),1)]),_:2},1032,["href"])]),e("td",te,[a(d(c),{href:s.route("devices.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",se,o(t.updated_at),1)]),_:2},1032,["href"])]),e("td",ae,[a(d(c),{class:"flex items-center text-right text-sm font-medium",href:s.route("devices.edit",t.id),tabindex:"-1"},{default:l(()=>[e("div",oe,[a(_,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),r.devices.data.length===0?(n(),f("tr",re,de)):m("",!0)]),le,a(O,{class:"px-6 py-2 bg-white border-none border-t p-0",links:r.devices.links,from:r.devices.from,to:r.devices.to,total:r.devices.total},null,8,["links","from","to","total"])])])],64))}});export{_e as default};
