import{Q as f,o as c,c as w,b as s,u as e,a as l,B as n,d as i,t as d,E as g,z as _,F as b,H as V,L as x}from"./app.5bf25e6f.js";import{A as y}from"./AppLayout.14f8c8f6.js";import{_ as m}from"./TextInput.48e8e32c.js";import{_ as h}from"./SelectInput.16ffd220.js";import{L as v}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";const U={class:"p-4"},C={class:"mb-2 font-bold text-2xl"},L=l("span",{class:"text-indigo-400 font-medium"},"/",-1),B={class:"bg-white rounded-md shadow overflow-hidden max-w-3xl"},A=l("option",{value:null},null,-1),S=l("option",{value:"Administrator"},"Administrator",-1),k=l("option",{value:"Reception_staff"},"Reception Staff",-1),E={class:"flex px-8 py-4 bg-gray-50 border-t border-gray-100 justify-start items-center"},F={layout:y},D=Object.assign(F,{__name:"Create",props:{locale:String},setup(p){let r=f({name:null,email:null,password:null,role:null,photo:null}),u=()=>{r.post(route("users.store"))};return(a,o)=>(c(),w(b,null,[s(e(V),{title:"Create User"}),l("div",U,[l("h2",C,[s(e(x),{class:"text-indigo-400 hover:text-indigo-600",href:a.route("users")},{default:n(()=>[i(d(a.trans("UsersList")),1)]),_:1},8,["href"]),L,i(" "+d(a.trans("Create")),1)]),l("div",B,[l("form",{onSubmit:o[4]||(o[4]=g((...t)=>e(u)&&e(u)(...t),["prevent"]))},[l("div",{class:_(["p-8 -mr-6 -mb-8 flex flex-wrap",p.locale=="ar"?"rtl text-right":"ltr text-left"])},[s(m,{modelValue:e(r).name,"onUpdate:modelValue":o[0]||(o[0]=t=>e(r).name=t),error:e(r).errors.name,direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",type:"text",label:"Username",autofocus:!0},null,8,["modelValue","error"]),s(m,{modelValue:e(r).email,"onUpdate:modelValue":o[1]||(o[1]=t=>e(r).email=t),error:e(r).errors.email,direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",type:"email",label:"Email"},null,8,["modelValue","error"]),s(m,{modelValue:e(r).password,"onUpdate:modelValue":o[2]||(o[2]=t=>e(r).password=t),error:e(r).errors.password,direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",type:"password",label:"Password",autocomplete:"new-password"},null,8,["modelValue","error"]),s(h,{modelValue:e(r).role,"onUpdate:modelValue":o[3]||(o[3]=t=>e(r).role=t),error:e(r).errors.role,direction:"ltr",class:"pr-6 pb-8 w-full lg:w-1/2",label:"Role"},{default:n(()=>[A,S,k]),_:1},8,["modelValue","error"])],2),l("div",E,[s(v,{loading:e(r).processing,class:"flex btn-green",type:"submit"},{default:n(()=>[i(d(a.trans("Create")+" "+a.trans("User")),1)]),_:1},8,["loading"])])],32)])])],64))}});export{D as default};
