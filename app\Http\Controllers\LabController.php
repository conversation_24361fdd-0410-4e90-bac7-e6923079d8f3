<?php

namespace App\Http\Controllers;

use App\Models\Lab;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Gate;

class LabController extends Controller
{
    public function index()
    {
        return Inertia::render('SideLabs/Index', [
            'filters' => Request::all('search', 'trashed'),
            'labs' => Lab::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($lab) => [
                    'id' => $lab->id,
                    'name' => $lab->name,
                    'owner' => $lab->owner,
                    'mobile' => $lab->mobile,
                    'created_at' => $lab->created_at->diffForHumans(),
                    'updated_at' => $lab->updated_at->diffForHumans(),
                    'deleted_at' => $lab->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('SideLabs/Create');
    }

    public function store()
    {
        Auth::user()->labs()->create(
            Request::validate([
                'name' => ['required', 'min:8', 'max:55'],
                'owner' => ['nullable', 'max:75'],
                'mobile' => ['nullable', 'min:11', 'max:11'],
            ])
        );
        if (Request::input('createAnother')) {
            return Redirect::back()->with('success', 'Lab created.');
        } else {
            return Redirect::route('labs')->with('success', 'Lab created.');
        }
    }

    public function edit(Lab $lab)
    {
        return Inertia::render('SideLabs/Edit', [
            'lab' => [
                'id' => $lab->id,
                'name' => $lab->name,
                'owner' => $lab->owner,
                'mobile' => $lab->mobile,
                'deleted_at' => $lab->deleted_at,
                'visits' => $lab->visits()->with('patient')->orderBy('created_at')->get()->map->only('id', 'created_at', 'name'),
            ],
        ]);
    }

    public function update(Lab $lab)
    {
        $lab->update(
            Request::validate([
                'name' => ['required', 'min:8', 'max:55'],
                'owner' => ['nullable', 'max:75'],
                'mobile' => ['nullable', 'min:11', 'max:11'],
            ])
        );
        return Redirect::route('labs')->with('success', 'Lab updated.');
    }

    public function destroy(Lab $lab)
    {
        $lab->delete();
        return Redirect::route('labs')->with('success', 'Lab deleted.');
    }

    public function restore(Lab $lab)
    {
        $lab->restore();
        return Redirect::back()->with('success', 'Lab restored.');
    }
}
