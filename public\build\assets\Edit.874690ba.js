import{Q as w,o as i,c as u,b as l,u as t,a as e,B as c,d as h,t as r,y as v,C as f,E as k,z as R,F as y,D as B,H as L,L as p,U as _}from"./app.5bf25e6f.js";import{A as V,I as N}from"./AppLayout.14f8c8f6.js";import{_ as C}from"./TextInput.48e8e32c.js";import{L as E}from"./LoadingButton.c8fb65b2.js";import{T}from"./TrashedMessage.5487e7e2.js";import"./plugin-vue_export-helper.21dcd24c.js";const j={class:"p-2 sm:p-4"},A={class:"mb-2 flex justify-start max-w-xl"},D={class:"font-bold text-2xl"},I=e("span",{class:"text-indigo-400 font-medium"}," /",-1),S={class:"bg-white rounded-md shadow-sm overflow-hidden max-w-xl"},F={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},H=e("hr",{class:"border-2"},null,-1),U={class:"shadow overflow-x-auto rounded-lg bg-white max-w-xl mt-4"},z={class:"w-full divide-y divide-gray-200"},M={class:"bg-gray-50"},O={class:"bg-green-500"},Q={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},$={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},q={class:"border-t p-0"},G={class:"px-6 py-2 flex items-center"},J={class:"border-t p-0"},K={class:"px-6 py-2"},P={key:0},W=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No tests found.",-1),X=[W],Y={layout:V},ae=Object.assign(Y,{__name:"Edit",props:{locale:String,result:Object},setup(n){const m=n;let a=w({_method:"put",name:m.result.name}),b=()=>{a.post(route("results.update",m.result.id),{onSuccess:()=>a.reset()})},g=()=>{confirm("Are you sure you want to delete this result?")&&_.Inertia.delete(route("results.destroy",m.result.id))},x=()=>{confirm("Are you sure you want to restore this result?")&&_.Inertia.put(route("results.restore",m.result.id))};return(s,d)=>(i(),u(y,null,[l(t(L),{title:"Edit Result"}),e("div",j,[e("div",A,[e("h2",D,[l(t(p),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("results")},{default:c(()=>[h(r(s.trans("ResultsList")),1)]),_:1},8,["href"]),I,h(" "+r(t(a).name),1)])]),e("div",S,[n.result.deleted_at?(i(),v(T,{key:0,class:"mb-6",onRestore:t(x)},{default:c(()=>[h(r(s.trans("This")+" "+s.trans("Result")+" "+s.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):f("",!0),e("form",{onSubmit:d[2]||(d[2]=k((...o)=>t(b)&&t(b)(...o),["prevent"]))},[e("div",{class:R(["p-8 -mr-6 -mb-8 flex flex-wrap",n.locale=="ar"?"rtl text-right":"ltr text-left"])},[l(C,{modelValue:t(a).name,"onUpdate:modelValue":d[0]||(d[0]=o=>t(a).name=o),error:t(a).errors.name,type:"text",class:"pr-6 pb-8 w-full",direction:"ltr",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),e("div",F,[l(E,{loading:t(a).processing,class:"btn-green",type:"submit"},{default:c(()=>[h(r(s.trans("Update")+" "+s.trans("Result")),1)]),_:1},8,["loading"]),n.result.deleted_at?f("",!0):(i(),u("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:d[1]||(d[1]=(...o)=>t(g)&&t(g)(...o))},r(s.trans("Delete")+" "+s.trans("Result")),1))])],32)]),H,e("div",U,[e("table",z,[e("thead",M,[e("tr",O,[e("th",Q,r(s.trans("RelatedTests")),1),e("th",$,r(s.trans("Edit")),1)])]),(i(!0),u(y,null,B(n.result.tests,o=>(i(),u("tr",{key:o.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",q,[l(t(p),{href:s.route("tests.edit",o.id),tabindex:"-1"},{default:c(()=>[e("div",G,r(o.short_name),1)]),_:2},1032,["href"])]),e("td",J,[l(t(p),{class:"flex items-center text-right text-sm font-medium",href:s.route("tests.edit",o.id),tabindex:"-1"},{default:c(()=>[e("div",K,[l(N,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),n.result.tests.length===0?(i(),u("tr",P,X)):f("",!0)])])])],64))}});export{ae as default};
