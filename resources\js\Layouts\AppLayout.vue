
<template>
  <div class="min-h-screen bg-gray-200" :class="locale == 'ar'?  'font-Tajawal' :  'font-Amiri'">
    <jet-banner />
    <TransitionRoot :show="sidebarOpened">
      <Dialog as="div" @close="sidebarOpened = false" class="fixed inset-0 z-40">
        <TransitionChild as="template" enter="transition ease-in-out duration-200 transform" enter-from="-translate-x-full" enter-to="translate-x-0" leave="transition ease-in-out duration-200 transform" leave-from="translate-x-0" leave-to="-translate-x-full">
          <div class="flex relative z-10 flex-col w-72 h-full bg-gray-50 border-r border-gray-200">
              <div class="flex px-2 py-3 my-2">
                <button type="button" value="Close sidebar" @click="sidebarOpened = false" class="h-8 w-8 mx-3 hover:ring-2 hover:ring-gray-300 flex justify-center items-center rounded-full focus:outline-none focus:ring-2 focus:ring-gray-600">
                    <XIcon class="h-5"/>
                </button>
                <Link href="/"> <ApplicationLogo class="h-9 mb-8 ml-4 w-auto"/> </Link>
              </div>
            
            <div class="overflow-y-auto flex-1" :class="locale == 'ar'?  'font-Tajawal rtl' :  'font-Amiri'">
              <div class="md:hidden mb-10 mt-4">
                <h3 class="mx-6 mb-2 text-xs tracking-widest text-gray-400 uppercase"> Top Navigation Bar </h3>
                <Link @click="sidebarOpened = false" v-for="(item, index) in topNavigation" :key="index" :href="route(item.route)" class="flex items-center px-6 py-3 text-blue-700 hover:text-orange-600 group
                  focus:outline-none transition duration-150 ease-in-out" :class="isUrl(item.url) ? 'text-yellow-400' : 'text-blue-700 hover:text-orange-500'">
                  <icon :name="item.icon" class="w-5 h-5 mx-2" :class="isUrl(item.url) ? 'fill-orange-600' : 'fill-green-700'" />{{ trans(item.label) }}
                </Link>
                <!-- <a v-for="(item, index) in topNavigation" :href="item.href" :key="index" class="flex items-center px-6 py-2.5 text-gray-500 hover:text-orange-600 group">
                  <component :is="item.icon" class="mr-2 w-5 h-5 text-gray-400 group-hover:text-orange-500"/> {{ item.label }}
                </a> -->
              </div>
              <div class="mb-10">
                <h3 class="mx-6 mb-2 text-xs tracking-widest text-gray-400 uppercase">  {{ trans('Laboratory') }} </h3> 
                <Link @click="sidebarOpened = false" v-for="(item, index) in laboratoryNavigation" :key="index" :href="route(item.route)" class="font-medium flex items-center px-6 py-3 text-blue-700 hover:text-orange-600 group
                  focus:outline-none transition duration-150 ease-in-out" :class="isUrl(item.url) ? 'text-yellow-400' : 'text-blue-700 hover:text-orange-500'">
                  <icon :name="item.icon" class="mx-2 w-5 h-5" :class="isUrl(item.url) ? 'fill-orange-600' : 'fill-green-700'" />{{ trans(item.label) }}
                </Link>
              </div>

              <div class="mb-10">
                <h3 class="mx-6 mb-2 text-xs tracking-widest text-gray-400 uppercase">  {{ trans('ManageUsers') }} </h3> 
                <Link @click="sidebarOpened = false" v-for="(item, index) in usersNavigation" :key="index" :href="route(item.route)" class="font-medium flex items-center px-6 py-3 text-blue-700 hover:text-orange-600 group
                  focus:outline-none transition duration-150 ease-in-out" :class="isUrl(item.url) ? 'text-yellow-400' : 'text-blue-700 hover:text-orange-500'">
                <component :is="item.icon" class="mx-2 w-5 h-5 text-gray-400 group-hover:text-orange-500"/> {{ trans(item.label)}} </Link>
              </div>

              
              <div class="mb-10">
                <h3 class="mx-6 mb-2 text-xs tracking-widest text-gray-400 uppercase"> {{ trans('Settings') }} </h3>
                <a @click="sidebarOpened = false" v-for="(item, index) in mainNavigation" :href="item.href" :key="index" class="font-medium flex items-center px-6 py-2.5 text-blue-600 hover:text-orange-600 group">
                  <component :is="item.icon" class="mx-2 w-5 h-5 text-gray-400 group-hover:text-orange-500"/> {{ trans(item.label) }}
                </a>
              </div>
              <!-- <div class="mb-10">
                <h3 class="mx-6 mb-2 text-xs tracking-widest text-gray-400 uppercase"> Following </h3>
                <a v-for="(item, index) in following" :href="item.href" :key="index" class="flex items-center px-6 py-2.5 text-gray-500 hover:text-orange-600 group">
                  <img :src="item.imageUrl" alt="" class="mr-2 w-7 h-7 rounded-full"> {{ trans(item.label) }}
                </a>
              </div> -->
            </div>
          </div>
        </TransitionChild>
        <TransitionChild as="template" enter="transition-opacity ease-linear duration-200" enter-from="opacity-0" enter-to="opacity-100" leave="transition-opacity ease-linear duration-200" leave-from="opacity-100" leave-to="opacity-0">
          <DialogOverlay class="fixed inset-0 bg-gray-600 bg-opacity-50"></DialogOverlay>
        </TransitionChild>
      </Dialog>
    </TransitionRoot>

    <div class="flex-1">
      <div class="flex justify-between py-1 px-3 md:px-6 space-x-3 md:space-x-6 border-b bg-orange-500" style="background-color: #009c34;">
        
        <div class="flex items-center flex-1">
          <button type="button" value="Open sidebar" @click="sidebarOpened = true" class="mr-3 flex-shrink-0 flex items-center justify-center w-8 h-8
           rounded-full text-gray-200 hover:ring-2 hover:ring-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-50">
            <MenuIcon class="h-6 w-6"/>
          </button>  
            <Link v-for="(item, index) in topNavigation" :key="index" :href="route(item.route)" class="hidden md:flex font-semibold items-center px-6 py-3 text-gray-200 hover:text-orange-600 group
              focus:outline-none transition duration-150 ease-in-out" :class="isUrl(item.url) ? 'text-yellow-400' : 'text-gray-200 hover:text-orange-500'">
              <icon :name="item.icon" class="w-5 h-5 mx-2" :class="isUrl(item.url) ? 'fill-orange-600' : 'fill-orange-600'" />{{ trans(item.label) }}
            </Link>
        </div>
        <a class="flex items-center px-2 py-2 font-medium text-gray-200 hover:text-orange-600 group md:justify-end" :href="`/locale/${locale_language}`">
            <icon name="language" class="w-5 h-5 mx-2 group-hover:text-orange-500" /> {{locale_language_lable}}
        </a>
        <Menu as="div" class="relative flex-shrink-0 py-2">
          <MenuButton v-if="$page.props.jetstream.managesProfilePhotos" class="rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-400">
            <img class="inline w-10 h-10 rounded-full" :src="$page.props.user.profile_photo_url" :alt="$page.props.user.name" >
          </MenuButton>

          <MenuButton v-else class="py-3">
            <div class="inline w-10 h-10 rounded-full  font-medium text-gray-200 focus:text-gray-100 hover:text-orange-500">{{ $page.props.user.name }}</div>
          </MenuButton>

          <transition enter-active-class="transition duration-100 ease-out transform" enter-from-class="opacity-0 scale-90" enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-100 ease-in transform" leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-90">
            <MenuItems class="z-50 py-2 overflow-hidden absolute right-0 mt-2 w-48 bg-white rounded-md border shadow-lg origin-top-right focus:outline-none">
              <MenuItem as="div">
                <a :href="route('profile.show')" class="flex w-full text-left py-1 px-4 text-sm text-gray-700 hover:text-gray-50 hover:bg-blue-600">My Profile</a>
              </MenuItem>

              <MenuItem as="div">
                <form method="POST" @submit.prevent="logout">
                    <button type="submit" class="w-full text-left py-1 px-4 text-sm text-gray-700 hover:text-gray-50 hover:bg-blue-600">Log Out</button>
                </form>
              </MenuItem>
            </MenuItems>
          </transition>
        </Menu>
      </div>
      <!-- Page Heading -->
        <header class="bg-white shadow" v-if="$slots.header">
            <div class="max-w-7xl mx-auto py-2 px-4 sm:px-6 lg:px-8">
                <slot name="header"></slot>
            </div>
        </header>

        <!-- Page Content -->
        <main>
            <slot></slot>
        </main>

        <div>
            <flash-messages />
        </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import {Dialog, DialogOverlay, Menu, MenuButton, MenuItem, MenuItems, TransitionChild, TransitionRoot} from '@headlessui/vue';
import {CogIcon, TagIcon, HomeIcon, MenuIcon, SearchIcon, XIcon, UsersIcon, DatabaseIcon} from '@heroicons/vue/outline';
import ApplicationLogo from '@/Jetstream/ApplicationLogo.vue';
import JetResponsiveNavLink from '@/Jetstream/ResponsiveNavLink.vue'
import FlashMessages from '@/MyComponents/FlashMessages.vue'
import Icon from '@/MyComponents/Icon.vue'
import { Head, Link } from '@inertiajs/inertia-vue3';

import JetBanner from '@/Jetstream/Banner.vue'
export default defineComponent({
    props: {
        title: String,
        locale: String,
    },
  components: {JetBanner, Link, FlashMessages, Icon, TagIcon, CogIcon, DatabaseIcon, JetResponsiveNavLink, MenuIcon, TransitionRoot, TransitionChild, Dialog, DialogOverlay, XIcon, HomeIcon, UsersIcon, Menu, MenuButton, MenuItems, MenuItem, SearchIcon, ApplicationLogo},
  data() {
    return {
      sidebarOpened: false,
      mainNavigation: [
        {href: '/tracking_tags', label: 'TrackingTags', icon: TagIcon},
        {href: '/settings/edit', label: 'Settings', icon: CogIcon},
        {href: '/buckup', label: 'DownloadABackup', icon: DatabaseIcon},
      ],
      laboratoryNavigation: [
        
        {route: 'devices', url:'devices', label: 'Devices', icon: 'device'},
        {route: 'results', url:'results', label: 'Results', icon: 'result'},
        {route: 'flags', url:'flags', label: 'Flags', icon: 'flag'},
        {route: 'categories', url:'categories', label: 'Categories', icon: 'category'},
        {route: 'tests', url:'tests', label: 'Tests', icon: 'test'},
        {route: 'groups', url:'groups', label: 'Groups', icon: 'group'},
        {route: 'offers', url:'offers', label: 'Offers', icon: 'offers'},
        {route: 'labs', url:'labs', label: 'SideLabs', icon: 'hospital'},
        {route: 'doctors', url:'doctors', label: 'Doctors', icon: 'physician'},
      ],
      usersNavigation: [
        {route: 'users', url:'users', label: 'UsersList', icon: 'UsersIcon'},
      ],
      topNavigation: [
        {route: 'patients', url:'patients', label: 'Reception', icon: 'id_card'},
        {route: 'income_report/index', url:'statistics', label: 'Statistics', icon: 'statistics'},
        {route: 'researchs.VisitsIndex', url:'researchs', label: 'Researchs', icon: 'research'},
      ],
      following: [
        {href: '/', label: 'Constantin Druc', imageUrl: 'https://pbs.twimg.com/profile_images/1333896976602193922/MtWztkxt_400x400.jpg'},
      ],
    };
  },

  computed: {
            locale_language() {
                if(this.$page.props.locale == 'ar') {
                    return 'en';
                }
                return 'ar'
            },

            locale_language_lable() {
                if(this.$page.props.locale == 'ar') {
                    return 'English';
                }
                return 'عربي'
            },
  },

  methods: {
    logout() {
        this.$inertia.post(route('logout'));
    },

    isUrl(...urls) {
      let currentUrl = this.$page.url.substr(1)
      if (urls[0] === '') {
        return currentUrl === ''
      }
      return urls.filter(url => currentUrl.startsWith(url)).length
    },
 }
})
</script>