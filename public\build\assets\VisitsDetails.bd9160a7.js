import{v as w,q as v,o as i,c as l,b as u,u as p,a as t,E as k,t as s,K as c,S as h,B as T,R as x,F as D,D as P,C as _,H as V,d as S,y as j,f as C,U as H}from"./app.5bf25e6f.js";import{_ as U}from"./StatisticsNav.0926be50.js";import{A as M,I as Y}from"./AppLayout.14f8c8f6.js";import{t as A,S as B,p as N}from"./SearchFilter.f110f3d1.js";import{P as O}from"./Pagination.b9f6e44a.js";import{h as y}from"./moment.9709ab41.js";/* empty css                                                                   */import{u as E,w as F,a as R}from"./xlsx.0799a57e.js";import"./plugin-vue_export-helper.21dcd24c.js";const I={class:"p-2 xl:p-3"},L={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4"},$={class:"flex flex-wrap"},q={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},G={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},K={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},W={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},z={disabled:"",type:"submit",class:"py-3 rounded bg-gray-300 text-gray-50 text-sm leading-4 font-bold whitespace-nowrap flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3 cursor-not-allowed"},J={class:"flex justify-between p-2 mb-4 bg-white shadow rounded items-center"},Q={class:"flex justify-start items-center w-full"},X=t("label",{class:"block text-gray-700"},"Trashed:",-1),Z=t("option",{value:null},null,-1),tt=t("option",{value:"with"},"With Trashed",-1),et=t("option",{value:"only"},"Only Trashed",-1),st=[Z,tt,et],ot=t("label",{class:"block text-gray-700"},"Visits date:",-1),at=t("option",{value:null},null,-1),rt=t("option",{value:"sentToday"},"Today's Visits",-1),nt=[at,rt],dt=t("label",{class:"block text-gray-700"},"Has Discount:",-1),it=t("option",{value:null},null,-1),lt=t("option",{value:"Has Discount"},"HasDiscount",-1),pt=[it,lt],ct=C('<option value="10"> 10 Per Page </option><option value="100"> 100 Per Page </option><option value="200"> 200 Per Page </option><option value="300"> 300 Per Page </option><option value="1000">1000 Per Page </option><option value="2000">2000 Per Page </option><option value="5000">5000 Per Page </option>',7),mt=[ct],ut={class:"flex items-center whitespace-nowrap px-4 h-9 rounded border border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"},xt={id:"reportToPrint",class:"shadow overflow-x-auto rounded-lg bg-white"},ft={id:"table",class:"min-w-full divide-y divide-gray-200"},ht={class:"bg-gray-50"},_t={class:"bg-indigo-600"},yt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},gt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},bt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},wt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},vt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},kt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},Tt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},Dt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},Pt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},Vt={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},St={class:"text-left px-3 py-4 text-sm font-medium text-gray-50 whitespace-nowrap"},jt={class:"border-t px-3 py-2"},Ct={class:"border-t px-3 py-2"},Ht={class:"border-t px-3 py-2"},Ut={class:"border-t px-3 py-2"},Mt={key:0,class:"border-t px-3 py-2"},Yt={class:"w-16 flex items-center justify-center text-sm font-medium py-1 px-1 bg-red-400 text-gray-50"},At={key:1,class:"border-t px-3 py-2"},Bt={class:"w-16 flex items-center justify-center text-sm font-medium py-1 px-1 bg-green-400 text-gray-50"},Nt={class:"border-t px-3 py-2"},Ot={class:"w-16 flex items-center justify-center text-sm font-medium py-1 px-1 bg-indigo-600 text-gray-50"},Et={class:"border-t px-3 py-2"},Ft={class:"border-t px-3 py-2"},Rt={class:"border-t px-3 py-2"},It={class:"border-t px-3 py-2"},Lt={class:"border-t px-3 py-2"},$t={key:0},qt=t("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No visits found.",-1),Gt=[qt],Kt={layout:M},oe=Object.assign(Kt,{__name:"VisitsDetails",props:{errors:Object,locale:String,ReportDateAndTime:String,filters:Object,visits:Object},setup(d){const n=d,r=w({search:n.filters.search,trashed:n.filters.trashed,start_date:n.filters.start_date,end_date:n.filters.end_date,sentToday:n.filters.sentToday,HasDiscount:n.filters.HasDiscount,gender:n.filters.gender,per_page:n.filters.per_page>10?n.filters.per_page:10});v(r,A(function(){H.Inertia.get(route("visitsDetails"),N(r),{preserveState:!0,replace:!0})},300),{deep:!0});function g(){Object.assign(r,{search:null,trashed:"",start_date:null,end_date:null,sentToday:null,HasDiscount:null})}let f=()=>{setTimeout(function(){window.print()},50)},b=(a,o)=>{var e=document.getElementById("table"),m=E.table_to_book(e,{sheet:"sheet1"});return o?F(m,{bookType:type,bookSST:!0,type:"base64"}):R(m,a||"MySheetName."+(type||"xlsx"))};return(a,o)=>(i(),l("div",I,[u(p(V),{title:"Visits Details"}),t("div",L,[t("form",{onSubmit:o[4]||(o[4]=k((...e)=>a.submit&&a.submit(...e),["prevent"]))},[t("div",$,[t("div",q,[t("span",G,s(a.trans("From"))+": ",1),c(t("input",{"onUpdate:modelValue":o[0]||(o[0]=e=>r.start_date=e),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,r.start_date]])]),t("div",K,[t("span",W,s(a.trans("To"))+": ",1),c(t("input",{"onUpdate:modelValue":o[1]||(o[1]=e=>r.end_date=e),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,r.end_date]])]),t("button",z,s(a.trans("Go")),1),t("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:o[2]||(o[2]=e=>p(b)("VisitsReport.xlsx"))},s(a.trans("ExportToExcel")),1),t("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:o[3]||(o[3]=(...e)=>p(f)&&p(f)(...e))},s(a.trans("Print")),1)])],32),u(U)]),t("section",J,[t("div",Q,[u(B,{modelValue:r.search,"onUpdate:modelValue":o[8]||(o[8]=e=>r.search=e),direction:"rtl",placeholder:"\u0628\u062D\u062B \u0639\u0646 \u0627\u0644\u0645\u0631\u0627\u062C\u0639\u064A\u0646",class:"mb-2 sm:mb-0 w-full",onReset:g},{default:T(()=>[X,c(t("select",{"onUpdate:modelValue":o[5]||(o[5]=e=>r.trashed=e),class:"mt-1 w-full form-select"},st,512),[[x,r.trashed]]),ot,c(t("select",{"onUpdate:modelValue":o[6]||(o[6]=e=>r.sentToday=e),class:"mt-1 w-full form-select"},nt,512),[[x,r.sentToday]]),dt,c(t("select",{"onUpdate:modelValue":o[7]||(o[7]=e=>r.HasDiscount=e),class:"mt-1 w-full form-select"},pt,512),[[x,r.HasDiscount]])]),_:1},8,["modelValue"])]),c(t("select",{"onUpdate:modelValue":o[9]||(o[9]=e=>r.per_page=e),class:"mr-1 block py-2 px-4 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm w-2/12"},mt,512),[[x,r.per_page,void 0,{number:!0}]]),t("div",ut,"Total: "+s(d.visits.total),1)]),t("div",xt,[t("table",ft,[t("thead",ht,[t("tr",_t,[t("th",yt,s(a.trans("Patient")),1),t("th",gt,s(a.trans("TestsCount")),1),t("th",bt,s(a.trans("OffersCount")),1),t("th",wt,s(a.trans("TestsCost")),1),t("th",vt,s(a.trans("Discount")),1),t("th",kt,s(a.trans("FinalCost")),1),t("th",Tt,s(a.trans("PaidAmount")),1),t("th",Dt,s(a.trans("RemainingAmount")),1),t("th",Pt,s(a.trans("Created_at")),1),t("th",Vt,s(a.trans("Updated_at")),1),t("th",St,s(a.trans("User")),1)])]),(i(!0),l(D,null,P(d.visits.data,(e,m)=>(i(),l("tr",{key:m,class:""},[t("td",jt,[S(s(e.name),1),e.deleted_at?(i(),j(Y,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-400 ml-2"})):_("",!0)]),t("td",Ct,s(e.tests_count),1),t("td",Ht,s(e.offers_count),1),t("td",Ut,s(e.tests_cost),1),e.discount>0?(i(),l("td",Mt,[t("span",Yt,s(e.discount),1)])):(i(),l("td",At,[t("span",Bt,s(e.discount),1)])),t("td",Nt,[t("span",Ot,s(e.final_cost),1)]),t("td",Et,s(e.paid_amount),1),t("td",Ft,s(e.remaining_amount),1),t("td",Rt,s(p(y)(e.created_at).format("YYYY-MM-DD h:m A")),1),t("td",It,s(p(y)(e.updated_at).format("YYYY-MM-DD h:m A")),1),t("td",Lt,s(e.user),1)]))),128)),d.visits.length===0?(i(),l("tr",$t,Gt)):_("",!0)]),u(O,{class:"mt-2 px-6 py-2 bg-white border-t p-0",links:d.visits.links,from:d.visits.from,to:d.visits.to,total:d.visits.total},null,8,["links","from","to","total"])])]))}});export{oe as default};
