import{k as c,l as m,o,c as r,a as i,t as n,z as f,C as g}from"./app.5bf25e6f.js";const b=["for"],S=["id","value","type","autocomplete","disabled","placeholder"],h={key:0,class:"form-error"},y={inheritAttrs:!0},k=Object.assign(y,{__name:"TextInput",props:{id:String,type:String,label:String,error:String,modelValue:[String,Number,Boolean],autocomplete:String,direction:String,disabled:String,placeholder:String,autofocus:Boolean},emits:["update:modelValue"],setup(e,{emit:v}){const u=e,t=c(null);m(()=>{u.autofocus&&d()});let d=()=>{t.value.focus()};return(l,a)=>(o(),r("div",null,[i("label",{class:"form-label",for:e.id},n(l.trans(e.label))+":",9,b),i("input",{id:e.id,ref_key:"input",ref:t,value:e.modelValue,type:e.type,autocomplete:e.autocomplete,disabled:e.disabled,onInput:a[0]||(a[0]=s=>l.$emit("update:modelValue",s.target.value)),class:f([{rtl:e.direction==="rtl",ltr:e.direction==="ltr",error:e.error},"form-input"]),placeholder:e.placeholder},null,42,S),e.error?(o(),r("div",h,n(e.error),1)):g("",!0)]))}});export{k as _};
