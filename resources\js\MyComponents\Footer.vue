
<template>
   <div class="bg-white mt-4 rounded-md shadow-md"   style="direction:ltr">
      <div class="flex p-4 m-auto text-gray-800 text-sm flex-col md:flex-row max-w-7xl">
         <!-- <div class="mt-2">
            © Copyright <span>{{ dateTime() }}</span>. All Rights Reserved to <a href="https://www.facebook.com/alebrahimyahmed1" class="w-6 mx-1 underline text-blue-600"><PERSON></a>.
         </div> -->

         <div class="mt-2">
            © Copyright <span>{{ dateTime() }}</span>. Developed with <span class="love"> ❤️ </span> by <a href="https://www.facebook.com/alebrahimyahmed1" class="w-6 mx-1 underline text-blue-600"><PERSON></a>.
         </div>	
         
         <div class="md:flex-auto md:flex-row-reverse mt-2 flex-row flex">
            <a href="https://www.twitter.com/alebrahimyahmed" class="w-6 mx-1"><icon name="twitter" /></a>
            <a href="https://www.facebook.com/alebrahimyahmed1" class="w-6 mx-1"><icon name="facebook" /></a>
            <a href="https://www.youtube.com/channel/UCKhqCVRoo8ndypZuZBxM6Wg" class="w-6 mx-1"><icon name="youtube" /></a>
            <a href="https://www.linkedin.com/in/alebrahimyahmed/" class="w-6 mx-1"><icon name="linkedin" /></a>
            <a href="https://www.instagram.com/ahmed_programmer/" class="w-6 mx-1"><icon name="instagram" /></a>
         </div>
      </div>
   </div>
</template>
<script>
import Icon from '@/MyComponents/Icon.vue'
import moment from "moment";
export default {
   components: {
      Icon,
   },

   methods: {
    dateTime(value) {
      return moment(value).format("YYYY");
    }

   //  dateTime(value) {
   //    return moment(value).format("YYYY-MM-DD");

   // const dateObject = new Date('2021-03-29T13:56:16.000000Z')
   // dateObject.getFullYear() // 2021
   // dateObject.getMonth() // 2
   // dateObject.getDate() // 29
   //  }

   
   }
   
}
</script>

<style scoped>
.love{
  font-weight: 100;
   font-size:  16px;
   color: rgb(253, 23, 69);
}
</style>