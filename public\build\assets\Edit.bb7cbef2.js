import{v as D,Q as v,o as i,c as l,b as n,u as o,a as e,B as u,d as p,t as a,y as q,C as h,E as N,z as B,F as y,D as w,H as L,L as f,K as T,S as F,U as k}from"./app.5bf25e6f.js";import{A as U,I as A}from"./AppLayout.14f8c8f6.js";import{_ as E}from"./TextInput.48e8e32c.js";import{_ as j}from"./SelectInput.16ffd220.js";import{L as I}from"./LoadingButton.c8fb65b2.js";import{T as H}from"./TrashedMessage.5487e7e2.js";import"./plugin-vue_export-helper.21dcd24c.js";const M={class:"p-2 sm:p-4"},R={class:"mb-2 flex justify-start max-w-2xl"},$={class:"font-bold text-2xl"},z=e("span",{class:"text-indigo-400 font-medium"}," /",-1),O={class:"bg-white rounded-md shadow overflow-hidden max-w-2xl"},K={value:"null",hidden:"",disabled:""},Q=["value"],G={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},J={class:"mt-8 shadow-sm bg-yellow-500 w-40 px-4 py-1 text-gray-50 max-w-sm rounded-sm"},P={class:"shadow overflow-x-auto rounded-lg bg-white max-w-2xl mt-2"},W={class:"w-full divide-y divide-gray-200"},X={class:"bg-gray-50"},Y={class:"bg-green-500"},Z={class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},ee={class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},te={class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},se={class:"px-6 py-4 text-left text-xs font-medium text-gray-50 uppercase tracking-wider"},oe={class:"border-t p-0 max-w-fit"},ae={class:"px-6 py-2 max-w-fit"},re=["onChange","onUpdate:modelValue"],de={class:"border-t p-0"},ne={class:"px-6 py-2"},ie={class:"border-t p-0"},le={class:"px-6 py-2"},ue={class:"border-t p-0"},ce={class:"px-6 py-2"},me={key:0},pe=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No tests found.",-1),fe=[pe],ge={layout:U},ke=Object.assign(ge,{__name:"Edit",props:{locale:String,category:Object,devices:Array},setup(c){const m=c;let C=D(m.devices),r=v({_method:"put",name:m.category.name,default_device:m.category.default_device}),g=v({_method:"put",sequence:null}),S=(s,d)=>{g.sequence=d,g.put(route("tests.updateTestSequence",s),{preserveScroll:!0,onSuccess:()=>g.reset()})},_=()=>{r.post(route("categories.update",m.category.id),{onSuccess:()=>r.reset("name")})},b=()=>{confirm("Are you sure you want to delete this category?")&&k.Inertia.delete(route("categories.destroy",m.category.id))},V=()=>{confirm("Are you sure you want to restore this category?")&&k.Inertia.put(route("categories.restore",m.category.id))};return(s,d)=>(i(),l(y,null,[n(o(L),{title:"Edit Category"}),e("div",M,[e("div",R,[e("h2",$,[n(o(f),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("categories")},{default:u(()=>[p(a(s.trans("CategoriesList")),1)]),_:1},8,["href"]),z,p(" "+a(o(r).name),1)])]),e("div",O,[c.category.deleted_at?(i(),q(H,{key:0,class:"mb-6",onRestore:o(V)},{default:u(()=>[p(a(s.trans("This")+" "+s.trans("Category")+" "+s.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):h("",!0),e("form",{onSubmit:d[3]||(d[3]=N((...t)=>o(_)&&o(_)(...t),["prevent"]))},[e("div",{class:B(["p-8 -mr-6 -mb-8 flex flex-wrap",c.locale=="ar"?"rtl text-right":"ltr text-left"])},[n(E,{modelValue:o(r).name,"onUpdate:modelValue":d[0]||(d[0]=t=>o(r).name=t),error:o(r).errors.name,type:"text",class:"pr-6 pb-8 w-full",direction:"ltr",label:"Name",autofocus:!0},null,8,["modelValue","error"]),n(j,{modelValue:o(r).default_device,"onUpdate:modelValue":d[1]||(d[1]=t=>o(r).default_device=t),error:o(r).errors.default_device,class:"pr-6 pb-4 w-full",direction:"ltr",label:"DefaultDevice"},{default:u(()=>[e("option",K,a(s.trans("SelectDefaultDevice")),1),(i(!0),l(y,null,w(o(C),t=>(i(),l("option",{key:t.id,value:t.name},a(t.name),9,Q))),128))]),_:1},8,["modelValue","error"])],2),e("div",G,[n(I,{loading:o(r).processing,class:"btn-green",type:"submit"},{default:u(()=>[p(a(s.trans("Update")+" "+s.trans("Category")),1)]),_:1},8,["loading"]),c.category.deleted_at?h("",!0):(i(),l("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:d[2]||(d[2]=(...t)=>o(b)&&o(b)(...t))},a(s.trans("Delete")+" "+s.trans("Category")),1))])],32)]),e("div",J,[e("span",null,a(s.trans("RelatedTests")),1)]),e("div",P,[e("table",W,[e("thead",X,[e("tr",Y,[e("th",Z,a(s.trans("Sequence")),1),e("th",ee,a(s.trans("ShortName")),1),e("th",te,a(s.trans("FullName")),1),e("th",se,a(s.trans("Edit")),1)])]),(i(!0),l(y,null,w(c.category.tests,t=>(i(),l("tr",{key:t.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",oe,[e("div",ae,[T(e("input",{class:"form-input",onChange:x=>o(S)(t.id,t.sequence),type:"number","onUpdate:modelValue":x=>t.sequence=x},null,40,re),[[F,t.sequence,void 0,{number:!0}]])])]),e("td",de,[n(o(f),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:u(()=>[e("div",ne,a(t.short_name),1)]),_:2},1032,["href"])]),e("td",ie,[n(o(f),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:u(()=>[e("div",le,a(t.full_name),1)]),_:2},1032,["href"])]),e("td",ue,[n(o(f),{href:s.route("tests.edit",t.id),tabindex:"-1"},{default:u(()=>[e("div",ce,[n(A,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),c.category.tests.length===0?(i(),l("tr",me,fe)):h("",!0)])])])],64))}});export{ke as default};
