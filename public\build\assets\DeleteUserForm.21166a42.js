import{n as p,r as s,o as f,y as h,B as o,a as r,b as n,X as y,z as w,d as t}from"./app.5bf25e6f.js";import{a as g,b as C}from"./DialogModal.3b39e601.js";import{_ as D}from"./DangerButton.177534fb.js";import{_ as $}from"./Input.f95445aa.js";import{_ as j}from"./InputError.2a9befad.js";import{_ as b}from"./SecondaryButton.fcd49457.js";import{_ as U}from"./plugin-vue_export-helper.21dcd24c.js";const k=p({components:{JetActionSection:g,JetDangerButton:D,JetDialogModal:C,JetInput:$,JetInputError:j,JetSecondaryButton:b},data(){return{confirmingUserDeletion:!1,form:this.$inertia.form({password:""})}},methods:{confirmUserDeletion(){this.confirmingUserDeletion=!0,setTimeout(()=>this.$refs.password.focus(),250)},deleteUser(){this.form.delete(route("current-user.destroy"),{preserveScroll:!0,onSuccess:()=>this.closeModal(),onError:()=>this.$refs.password.focus(),onFinish:()=>this.form.reset()})},closeModal(){this.confirmingUserDeletion=!1,this.form.reset()}}}),B=t(" Delete Account "),v=t(" Permanently delete your account. "),A=r("div",{class:"max-w-xl text-sm text-gray-600"}," Once your account is deleted, all of its resources and data will be permanently deleted. Before deleting your account, please download any data or information that you wish to retain. ",-1),J={class:"mt-5"},V=t(" Delete Account "),M=t(" Delete Account "),S=t(" Are you sure you want to delete your account? Once your account is deleted, all of its resources and data will be permanently deleted. Please enter your password to confirm you would like to permanently delete your account. "),K={class:"mt-4"},N=t(" Cancel "),P=t(" Delete Account ");function z(e,a,E,F,I,O){const i=s("jet-danger-button"),l=s("jet-input"),c=s("jet-input-error"),d=s("jet-secondary-button"),m=s("jet-dialog-modal"),u=s("jet-action-section");return f(),h(u,null,{title:o(()=>[B]),description:o(()=>[v]),content:o(()=>[A,r("div",J,[n(i,{onClick:e.confirmUserDeletion},{default:o(()=>[V]),_:1},8,["onClick"])]),n(m,{show:e.confirmingUserDeletion,onClose:e.closeModal},{title:o(()=>[M]),content:o(()=>[S,r("div",K,[n(l,{type:"password",class:"mt-1 block w-3/4",placeholder:"Password",ref:"password",modelValue:e.form.password,"onUpdate:modelValue":a[0]||(a[0]=_=>e.form.password=_),onKeyup:y(e.deleteUser,["enter"])},null,8,["modelValue","onKeyup"]),n(c,{message:e.form.errors.password,class:"mt-2"},null,8,["message"])])]),footer:o(()=>[n(d,{onClick:e.closeModal},{default:o(()=>[N]),_:1},8,["onClick"]),n(i,{class:w(["ml-3",{"opacity-25":e.form.processing}]),onClick:e.deleteUser,disabled:e.form.processing},{default:o(()=>[P]),_:1},8,["onClick","class","disabled"])]),_:1},8,["show","onClose"])]),_:1})}var R=U(k,[["render",z]]);export{R as default};
