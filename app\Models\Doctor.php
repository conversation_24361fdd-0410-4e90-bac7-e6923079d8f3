<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Doctor extends Model
{
    use HasFactory;
    use SoftDeletes;

    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? 'id', $value)->withTrashed()->firstOrFail();
    }

    protected $fillable = ['id', 'name', 'gender', 'user_id', 'specialty', 'mobile', 'created_at', 'updated_at'];

    protected $dates = ['deleted_at'];

        public function scopeFilter($query, array $filters)
        {
            $query
    
            ->when($filters['search'] ?? null, function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('id', '=', $search)
                        ->orWhere('name', 'like', $search.'%');
                });
            })
    
            ->when($filters['trashed'] ?? null, function ($query, $trashed) {
                if ($trashed === 'with') {
                    $query->withTrashed();
                } elseif ($trashed === 'only') {
                    $query->onlyTrashed();
                }
            });
        }
    
    
        public function user()
        {
            return $this->belongsTo(User::class);
        }
    
        public function visits()
        {
            return $this->hasMany(Visit::class);
        }
    
   
        
    

    
    
}
