import{Q as T,v as S,o as i,c as d,b as c,u as t,a as r,B as p,d as f,t as a,E as V,z as A,F as g,D as L,C as x,H as B,L as D}from"./app.5bf25e6f.js";import{A as N}from"./AppLayout.14f8c8f6.js";import{_ as v}from"./TextInput.48e8e32c.js";import{D as O}from"./DropdownSearch.7a091d54.js";import{L as y}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const j={class:"p-2 sm:p-4"},E={class:"mb-2 flex justify-start"},F={class:"font-bold text-2xl"},z=r("span",{class:"text-indigo-400 font-medium"}," /",-1),H={class:"bg-white rounded-md shadow max-w-2xl"},M={class:"pr-4 pb-0 w-full text-blue-600 font-semibold"},U={class:"p-1"},$={key:0,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},I={class:"w-full flex-row flex justify-start items-center flex-wrap"},P=["onClick","for"],Q={key:1,class:"ltr text-left w-full bg-indigo-300 py-1 px-2 mr-6 rounded h-auto mt-2"},R=r("div",{class:"flex flex-wrap"},[r("div",{class:"flex items-stretch p-1"},"There are no attached tests yet.")],-1),q=[R],G={key:2,class:"form-error"},J={class:"ltr text-left w-full mt-2"},K={key:0,class:"ml-6 pb-2 text-red-600"},W={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},X={layout:N},ae=Object.assign(X,{__name:"Create",props:{locale:String,tests:Array},setup(h){const _=h;let e=T({createAnother:!1,name:null,price:null,tests_count:null,offerTests:[]});const n=S(_.tests);let w=s=>{e.offerTests.push({id:s.id,short_name:s.short_name}),n.splice(n.findIndex(l=>l.id===s.id),1)},C=(s,l,o)=>{n.push({id:s,short_name:l}),e.offerTests.splice(o,1)},m=()=>{e.createAnother=!0,b()},k=()=>{e.createAnother=!1,b()},b=()=>{e.tests_count=e.offerTests.length,e.post(route("offers.store"),{preserveState:!0,onSuccess:()=>{e.reset(),n.splice(0,n.length),_.tests.forEach(s=>{n.push({id:s.id,short_name:s.short_name})})}})};return(s,l)=>(i(),d(g,null,[c(t(B),{title:"Create Offer"}),r("div",j,[r("div",E,[r("h2",F,[c(t(D),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("offers")},{default:p(()=>[f(a(s.trans("OffersList")),1)]),_:1},8,["href"]),z,f(" "+a(s.trans("Create")),1)])]),r("div",H,[r("form",{onSubmit:l[2]||(l[2]=V((...o)=>t(m)&&t(m)(...o),["prevent"]))},[r("div",{class:A(["p-5 -mr-6 -mb-5 flex flex-wrap",h.locale=="ar"?"rtl text-right":"ltr text-left"])},[c(v,{modelValue:t(e).name,"onUpdate:modelValue":l[0]||(l[0]=o=>t(e).name=o),error:t(e).errors.name,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"]),c(v,{modelValue:t(e).price,"onUpdate:modelValue":l[1]||(l[1]=o=>t(e).price=o),modelModifiers:{number:!0},error:t(e).errors.price,direction:"ltr",class:"pr-6 pb-4 w-full sm:w-1/2",type:"number",label:"Price"},null,8,["modelValue","error"]),r("div",M,[r("span",U,a(s.trans("Tests"))+":",1)]),t(e).offerTests.length>0?(i(),d("div",$,[r("div",I,[(i(!0),d(g,null,L(t(e).offerTests,(o,u)=>(i(),d("div",{class:"p-1 min-w-fit max-w-fit mt-1",key:u},[r("label",{title:"Remove Test",onClick:Y=>t(C)(o.id,o.short_name,u),class:"pt-0.5 btn-remove border border-green-400",for:o.short_name},a(u+1)+" - "+a(o.short_name),9,P)]))),128))])])):(i(),d("div",Q,q)),t(e).errors.tests_count?(i(),d("div",G,a(t(e).errors.tests_count),1)):x("",!0),r("div",J,[c(O,{class:"pr-6 pb-2 w-full sm:w-1/2",options:n,onSelect:t(w),CloseOnSelect:!1,fixedLabel:!0,label:"Select Test",searchBy:"short_name",placeholder:"Search for tests"},null,8,["options","onSelect"])])],2),t(e).isDirty?(i(),d("div",K,"There are unsaved form changes.")):x("",!0),r("div",W,[c(y,{loading:t(e).processing,class:"mr-4 px-3 py-2 btn-green",onClick:t(m)},{default:p(()=>[f(a(s.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),c(y,{loading:t(e).processing,class:"px-3 py-2 btn-green",onClick:t(k)},{default:p(()=>[f(a(s.trans("Create")+" "+s.trans("Offer")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{ae as default};
