/* @tailwind base;
@tailwind components;
@tailwind utilities; */


@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';




@import "buttons";
@import "form";
@import "table";
@import "fonts";

.rtl {
    direction: rtl;
}

.ltr {
    direction: ltr;
}

@media screen {
    header {
        display: none;
    }

    footer {
        display: none;
    }
}
/* @page {
    size: A4;
    margin: 8mm 8mm 8mm 8mm;
} */

@media print {
    #reportToPrint {
        position: absolute;
        left: 0;
        top: 0;
        min-height: 100%;
        min-width: 100% !important;
        font-size: 1.4rem /* 36px */;
        line-height: 1.4rem /* 40px */;
    }
    #reportToPrint,
    #reportToPrint * {
        visibility: visible;
    }

    body * {
        visibility: hidden;
    }
    #section-to-print,
    #section-to-print * {
        visibility: visible;
    }

    #section-to-print {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
    }

    header {
        width: 100%;
        max-height: 35%;
    }

    .content-block {
        page-break-inside: avoid !important;
    }

    footer {
        position: fixed;
        bottom: 0;
        width: 100%;
        max-height: 7%;
        overflow-y: hidden;
        /* opacity: 0.7; */
    }

    html,
    body {
        width: 210mm;
        height: 297mm;
    }
}

.backdrop {
    top: 0;
    right: 0;
    position: fixed;
    width: 100%;
    height: 100%;
}