import{Q as b,o as i,c as m,b as c,u as r,a as t,E as g,t as e,K as _,S as h,B as y,z as w,d,H as v}from"./app.5bf25e6f.js";import{_ as S}from"./StatisticsNav.0926be50.js";import{A as T}from"./AppLayout.14f8c8f6.js";import{L as k}from"./LoadingButton.c8fb65b2.js";import{u as V,w as D,a as R}from"./xlsx.0799a57e.js";import{h as A}from"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const B={class:"p-2 xl:p-3"},E={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4"},M={class:"flex flex-wrap"},N={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},C={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},F={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},Y={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},j={id:"reportToPrint",class:"flex overflow-hidden max-w-3xl"},L={class:"flex w-full"},z={id:"table",class:"w-full shadow-lg"},H={class:"ttr"},O={class:"tth text-center",colspan:"3"},P={class:"ttr"},U={class:"ttr"},$={colspan:"1",class:"tth text-center"},G={colspan:"1",class:"tth text-center"},I={key:0},K={class:"text-yellow-500"},Q={class:"text-yellow-500"},q=d(" )"),J={key:1},W=d("( "),X={class:"text-yellow-500"},Z=d(" )"),tt={class:"ttr"},et={class:"ttd text-yellow-500"},st={class:"ttd text-yellow-500"},ot={layout:T},ct=Object.assign(ot,{__name:"VisitsReport",props:{errors:Object,locale:String,ReportDateAndTime:String,start_date:String,end_date:String,todays_visits:Number,cumulative_visits:Number},setup(n){let l=b({start_date:null,end_date:null}),p=()=>{l.post(route("visits_report/fetch"),{preserveState:!0,onSuccess:()=>{}})},u=()=>{setTimeout(function(){window.print()},50)},f=(s,o)=>{var a=document.getElementById("table"),x=V.table_to_book(a,{sheet:"sheet1"});return o?D(x,{bookType:type,bookSST:!0,type:"base64"}):R(x,s||"MySheetName."+(type||"xlsx"))};return(s,o)=>(i(),m("div",B,[c(r(v),{title:"VisitsReport"}),t("div",E,[t("form",{onSubmit:o[4]||(o[4]=g((...a)=>r(p)&&r(p)(...a),["prevent"]))},[t("div",M,[t("div",N,[t("span",C,e(s.trans("From"))+": ",1),_(t("input",{"onUpdate:modelValue":o[0]||(o[0]=a=>r(l).start_date=a),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,r(l).start_date]])]),t("div",F,[t("span",Y,e(s.trans("To"))+": ",1),_(t("input",{"onUpdate:modelValue":o[1]||(o[1]=a=>r(l).end_date=a),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,r(l).end_date]])]),c(k,{loading:r(l).processing,type:"submit",class:"btn-indigo flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3"},{default:y(()=>[d(e(s.trans("Go")),1)]),_:1},8,["loading"]),t("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:o[2]||(o[2]=a=>r(f)("VisitsReport.xlsx"))},e(s.trans("ExportToExcel")),1),t("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:o[3]||(o[3]=(...a)=>r(u)&&r(u)(...a))},e(s.trans("Print")),1)])],32),c(S)]),t("div",j,[t("div",L,[t("table",z,[t("tr",H,[t("th",O,e(s.trans("VisitsReport")),1)]),t("tr",P,[t("th",{class:w(["tth",n.locale=="ar"?"rtl text-right":"ltr text-left"]),locale:"",colspan:"3"},e(s.trans("ReportDateAndTime"))+": "+e(r(A)(n.ReportDateAndTime).format("YYYY-MM-DD h:m A")),3)]),t("tr",U,[t("th",$,e(s.trans("Today")),1),t("th",G,[d(e(s.trans("Cumulative"))+" - ",1),n.start_date&&n.end_date?(i(),m("span",I,[d("( "+e(s.trans("From"))+" ",1),t("span",K,e(n.start_date),1),d(" "+e(s.trans("To"))+" ",1),t("span",Q,e(n.end_date),1),q])):(i(),m("span",J,[W,t("span",X,e(s.trans("ThisMonth")),1),Z]))])]),t("tr",tt,[t("td",et,e(n.todays_visits),1),t("td",st,e(n.cumulative_visits),1)])])])])]))}});export{ct as default};
