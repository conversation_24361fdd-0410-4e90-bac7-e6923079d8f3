{"The :attribute must contain at least one letter.": "The :attribute must contain at least one letter.", "The :attribute must contain at least one number.": "The :attribute must contain at least one number.", "The :attribute must contain at least one symbol.": "The :attribute must contain at least one symbol.", "The :attribute must contain at least one uppercase and one lowercase letter.": "The :attribute must contain at least one uppercase and one lowercase letter.", "The given :attribute has appeared in a data leak. Please choose a different :attribute.": "The given :attribute has appeared in a data leak. Please choose a different :attribute.", "ManageAccount": "Manage Account", "Logout": "Logout", "Reception": "Reception", "Laboratory": "Laboratory", "Reports": "Reports", "Statistics": "Statistics", "AllowTextWrap": "Allow Text Wrap", "Dashboard": "Dashboard22", "Devices": "Devices", "Groups": "Groups", "Offers": "Offers", "Flags": "Flags", "Categories": "Categories", "Results": "Results", "Tests": "Tests", "Doctors": "Doctors", "SideLabs": "Side Labs", "Visits": "Visits", "ManageUsers": "Manage Users", "UsersList": "Users List", "CategoriesList": "Categories List", "GroupsList": "Groups List", "ResultsList": "Results List", "OffersList": "Offers List", "FlagsList": "Flags List", "DevicesList": "Devices List", "DoctorsList": "Doctors List", "SideLabsList": "SideLabs List", "TestsList": "Tests List", "PatientsList": "Patients List", "Settings": "Settings", "LabName": "Laboratory Name", "ReportHeader": "Report Header", "ReportFooter": "Report Footer", "HeaderPhoto": "Header Photo", "FooterPhoto": "Footer Photo", "LogoPhoto": "Logo Photo", "Trashed": "Trashed", "WithTrashed": "With Trashed", "OnlyTrashed": "Only Trashed", "FullyPaid": "<PERSON>y Paid", "Patients": "Patients", "Patient": "Patient", "Visit": "Visit", "Device": "<PERSON><PERSON>", "Group": "Group", "Result": "Result", "Offer": "Offer", "Flag": "Flag", "Category": "Category", "RelatedTests": "Related Tests", "RelatedVisits": "Related Visits", "Test": "Test", "Doctor": "Doctor", "SideLab": "SideLab", "Price": "Price", "Specialty": "Specialty", "Error": "Error", "forceDelete": "forceDelete", "PatientforceDeleteMessage": "Are you sure you want to permanently delete this patient and all its visits and tests", "VisitforceDeleteMessage": "Are you sure you want to permanently delete this visit and all its tests", "Delete": "Delete", "DeleteMessage": "Are you sure you want to delete", "QM": "?", "This": "This", "HasBeenDeleted": "has been deleted.", "Restore": "Rest<PERSON>", "Create": "Create", "CreateAndCreateAnother": "Create and create another", "Edit": "Edit", "Update": "Update", "RegisterANewPatient": "Register a new patient", "RegisterANewVisit": "Register a new visit", "Print": "Print", "CreateANewCategory": "Create a new Category", "CreateANewDevice": "Create a new <PERSON><PERSON>", "CreateANewResult": "Create a new Result", "CreateANewUser": "Create a new User", "CreateANewTest": "Create a new Test", "CreateANewGroup": "Create a new Group", "CreateANewDoctor": "Create a new Doctor", "CreateANewSideLab": "Create a new SideLab", "CreateANewFlag": "Create a new Flag", "CreateANewOffer": "Create a new Offer", "CreateANewTrackingTag": "Create a new Research Tag", "TrackingTag": "Research Tag", "TrackingTags": "Research Tags", "TrackingTagsList": "Research Tags List", "Smoking": "Smoking", "MaritalStatus": "Marital Status", "Single": "Single", "Married": "Married", "Divorced": "Divorced", "Widowed": "Widowed", "Smoker": "Smoker", "NonSmoker": "Non-Smoker", "ExSmoker": "Ex-Smoker", "Save": "Save", "ID": "ID", "Name": "Name", "Gender": "Gender", "Male": "Male", "Female": "Female", "Age": "Age", "Mobile": "Mobile", "Created_at": "Create Date", "Updated_at": "Last Update", "ReferredBy": "ReferredBy", "OutPatient": "OutPatient", "TestsCount": "Tests Count", "TestsCost": "Tests Cost", "Discount": "Discount", "FinalCost": "Final Cost", "PaidAmount": "<PERSON><PERSON>", "RemainingAmount": "Remaining Amount", "TodaysVisit": "Today's Visit", "AllVisits": "All Visits", "TotalVisits": "Total Visits", "Missing": "Missing", "Existing": "Existing", "OffersCount": "Offers Count", "ShortName": "Short Name", "FullName": "Full Name", "LabToLabPrice": "Lab To Lab Price", "LabToPatientPrice": "Lab To Patient Price", "Sequence": "Sequence", "Unit": "Unit", "ResultsType": "Results Type", "DefaultDevice": "De<PERSON><PERSON>", "NumericResults": "Numeric Results", "PredefinedResults": "Predefined Results", "SelectDefaultDevice": "Select Default Device", "SelectCategory": "Select Category", "SelectResult": "Select Result", "SelectDevice": "Select Device", "User": "User", "Email": "Email", "Username": "Username", "Password": "Password", "Photo": "Photo", "Role": "User Role", "Owner": "Owner", "Available": "Available", "Requested": "Requested", "NoVisitsFound": "No visits found", "NoPatientsFound": "No patients found", "NoTestsFound": "No tests found", "NoGroupsFound": "No groups found", "NoDoctorsFound": "No doctors found", "NoSideLabsFound": "No sildelabs found", "NoOffersFound": "No Offers found", "NoFlagsFound": "No Flags found", "DownloadABackup": "Download a backup", "ReportDateAndTime": "Report Date & Time", "PatientsCount": "Patients Count", "Income": "Income", "All": "All", "Users": "Users", "Count": "Count", "OffersReport": "Offers Report", "TestsReport": "Tests Report", "VisitsReport": "Visits Report", "IncomeReport": "Income Report", "DoctorsReport": "Doctors Report", "SideLabsReport": "SideLabs Report", "Today": "Today", "Cumulative": "Cumulative", "From": "From", "To": "To", "ThisMonth": "This Month", "Go": "Go", "ExportToExcel": "Export To Excel", "NotTodysVisit": "Please note that this visit is not Today's V<PERSON><PERSON>, but a previous one.!", "Researchs": "Researchs"}