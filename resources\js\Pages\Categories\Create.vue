<template>
  <Head title="Create Category" />
  <div class="p-2 sm:p-4">
    <h2 class="mb-2 font-bold text-2xl">
      <Link class="text-indigo-400 hover:text-indigo-600" :href="route('categories')">{{ trans('CategoriesList') }}</Link>
      <span class="text-indigo-400 font-medium"> /</span> {{ trans('Create') }}
    </h2>
    <div class="bg-white rounded-md shadow overflow-hidden max-w-xl">
      <form @submit.prevent="store">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input v-model="form.name" :error="form.errors.name" class="pr-6 pb-8 w-full" direction="ltr" type="text" label="Name" :autofocus="true" />
          <select-input v-model="form.default_device" :error="form.errors.default_device" direction="ltr" class="pr-6 pb-4 w-full" label="DefaultDevice">
            <option value="null" hidden selected disabled>{{trans("SelectDefaultDevice")}}</option>
            <option v-for="device in ourDevices" :key="device.id" :value="device.name">{{ device.name }}</option>
          </select-input>
        </div>
        <div class="flex px-8 py-4 bg-gray-50 border-t border-gray-100  justify-start items-center">
          <loading-button :loading="form.processing" class="flex btn-green" type="submit">{{trans('Create') + ' ' + trans('Category')}}</loading-button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue';
import SelectInput from '@/MyComponents/SelectInput.vue';
import LoadingButton from '@/MyComponents/LoadingButton.vue';
import TrashedMessage from '@/MyComponents/TrashedMessage.vue';
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia';

import { reactive } from '@vue/reactivity'

const props = defineProps({
  locale: String,
  category: Object,
  devices: Array,
});

let form = useForm({
  name: null,
  default_device: null,
});

let ourDevices = reactive(props.devices);

let store = () => {
  form.post(route('categories.store'));
};

</script>
