<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTestVisitTable extends Migration
{
    public function up()
    {
        Schema::enableForeignKeyConstraints();
        Schema::create('test_visit', function (Blueprint $table) {
            $table->id();

            $table->foreignId('user_id')->constrained()->onDelete('restrict')->onUpdate('cascade');

            $table->foreignId('test_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('visit_id')->constrained()->onDelete('cascade')->onUpdate('cascade');

            $table->foreignId('device_id')->nullable()->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('flag_id')->nullable()->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('offer_id')->nullable()->constrained()->onDelete('restrict')->onUpdate('cascade');

            $table->double('value')->nullable();
            $table->string('result', 100)->nullable();

            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('test_visit');
    }
}
