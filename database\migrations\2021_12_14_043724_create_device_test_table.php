<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDeviceTestTable extends Migration
{

    public function up()
    {
        Schema::create('device_test', function (Blueprint $table) {
            $table->id();
            $table->text('normal_range');
            $table->foreignId('test_id')->constrained()->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('device_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
        });
    }

    public function down()
    {
        Schema::dropIfExists('device_test');
    }
}
