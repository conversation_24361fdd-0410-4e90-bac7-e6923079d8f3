import{Q as f,o as m,c,b as p,u as a,a as t,E as g,t as e,K as _,S as b,B as y,z as w,d,F as S,D as v,H as k}from"./app.5bf25e6f.js";import{_ as T}from"./StatisticsNav.0926be50.js";import{A as D}from"./AppLayout.14f8c8f6.js";import{L as A}from"./LoadingButton.c8fb65b2.js";import{u as L,w as R,a as B}from"./xlsx.0799a57e.js";import{h as E}from"./moment.9709ab41.js";import"./plugin-vue_export-helper.21dcd24c.js";const F={class:"p-2 xl:p-3"},M={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4"},C={class:"flex flex-wrap"},V={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},N={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},Y={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},j={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},I={id:"reportToPrint",class:"flex overflow-hidden max-w-3xl"},P={class:"flex w-full"},z={id:"table",class:"w-full shadow-lg"},H={class:"ttr"},O={class:"tth text-center",colspan:"3"},Q={class:"ttr"},U={class:"ttr"},$={colspan:"1",class:"tth text-center"},G={colspan:"1",class:"tth text-center"},K={colspan:"1",class:"tth text-center"},q={key:0},J={class:"text-yellow-500"},W={class:"text-yellow-500"},X=d(" )"),Z={key:1},tt=d("( "),et={class:"text-yellow-500"},st=d(" )"),ot={class:"ttd text-yellow-500 text-left"},rt={class:"ttd text-yellow-500 text-left"},at={class:"ttd text-yellow-500 text-center"},nt={layout:D},ut=Object.assign(nt,{__name:"SideLabsReport",props:{errors:Object,locale:String,ReportDateAndTime:String,start_date:String,end_date:String,side_labs_income:Array},setup(n){let l=f({start_date:null,end_date:null}),x=()=>{l.post(route("side_labs_report/fetch"),{preserveState:!0,onSuccess:()=>{}})},u=()=>{setTimeout(function(){window.print()},50)},h=(s,r)=>{var o=document.getElementById("table"),i=L.table_to_book(o,{sheet:"sheet1"});return r?R(i,{bookType:type,bookSST:!0,type:"base64"}):B(i,s||"MySheetName."+(type||"xlsx"))};return(s,r)=>(m(),c("div",F,[p(a(k),{title:"SidelabsReport"}),t("div",M,[t("form",{onSubmit:r[4]||(r[4]=g((...o)=>a(x)&&a(x)(...o),["prevent"]))},[t("div",C,[t("div",V,[t("span",N,e(s.trans("From"))+": ",1),_(t("input",{"onUpdate:modelValue":r[0]||(r[0]=o=>a(l).start_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[b,a(l).start_date]])]),t("div",Y,[t("span",j,e(s.trans("To"))+": ",1),_(t("input",{"onUpdate:modelValue":r[1]||(r[1]=o=>a(l).end_date=o),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[b,a(l).end_date]])]),p(A,{loading:a(l).processing,type:"submit",class:"btn-indigo flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3"},{default:y(()=>[d(e(s.trans("Go")),1)]),_:1},8,["loading"]),t("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:r[2]||(r[2]=o=>a(h)("SidelabsReport.xlsx"))},e(s.trans("ExportToExcel")),1),t("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:r[3]||(r[3]=(...o)=>a(u)&&a(u)(...o))},e(s.trans("Print")),1)])],32),p(T)]),t("div",I,[t("div",P,[t("table",z,[t("tr",H,[t("th",O,e(s.trans("SideLabsReport")),1)]),t("tr",Q,[t("th",{class:w(["tth",n.locale=="ar"?"rtl text-right":"ltr text-left"]),locale:"",colspan:"3"},e(s.trans("ReportDateAndTime"))+": "+e(a(E)(n.ReportDateAndTime).format("YYYY-MM-DD h:m A")),3)]),t("tr",U,[t("th",$,e(s.trans("SideLab")),1),t("th",G,e(s.trans("PatientsCount")),1),t("th",K,[d(e(s.trans("Income"))+" - ",1),n.start_date&&n.end_date?(m(),c("span",q,[d("( "+e(s.trans("From"))+" ",1),t("span",J,e(n.start_date),1),d(" "+e(s.trans("To"))+" ",1),t("span",W,e(n.end_date),1),X])):(m(),c("span",Z,[tt,t("span",et,e(s.trans("ThisMonth")),1),st]))])]),(m(!0),c(S,null,v(n.side_labs_income,(o,i)=>(m(),c("tr",{class:"ttr",key:i},[t("td",ot,e(i+1+" - "+o.side_lab_name),1),t("td",rt,e(o.visits_count),1),t("td",at,e(o.side_lab_income)+" IQD",1)]))),128))])])])]))}});export{ut as default};
