<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Artisan;

class BuckupController extends Controller
{
    public function download()
    {
        Artisan::call('backup:run');
        $path = storage_path('app/LaraLab_Backup/*');
        $latest_ctime = 0;
        $latest_filename = '';
        $files = glob($path);
        foreach ($files as $file) {
            if (is_file($file) && filectime($file) > $latest_ctime) {
                $latest_ctime = filectime($file);
                $latest_filename = $file;
            }
        }
        return response()->download($latest_filename);
    }
}
