import{Q as g,o as b,c as h,b as s,u as e,a,B as m,d as l,t as n,E as x,z as _,F as C,H as y,L as v}from"./app.5bf25e6f.js";import{A as w}from"./AppLayout.14f8c8f6.js";import{_ as k}from"./TextInput.48e8e32c.js";import{L as f}from"./LoadingButton.c8fb65b2.js";import"./plugin-vue_export-helper.21dcd24c.js";const A={class:"p-4"},F={class:"mb-2 font-bold text-2xl"},L=a("span",{class:"text-indigo-400 font-medium"},"/",-1),V={class:"bg-white rounded-md shadow overflow-hidden max-w-xl"},B={class:"flex px-4 py-2 bg-gray-100 border-t border-gray-100 justify-start items-center"},S={layout:w},D=Object.assign(S,{__name:"Create",props:{locale:String},setup(c){let t=g({createAnother:!1,name:null}),i=()=>{t.createAnother=!0,u()},p=()=>{t.createAnother=!1,u()},u=()=>{t.post(route("flags.store"),{preserveState:!0,onSuccess:()=>{t.reset()}})};return(r,o)=>(b(),h(C,null,[s(e(y),{title:"Create Flag"}),a("div",A,[a("h2",F,[s(e(v),{class:"text-indigo-400 hover:text-indigo-600",href:r.route("flags")},{default:m(()=>[l(n(r.trans("FlagsList")),1)]),_:1},8,["href"]),L,l(" "+n(r.trans("Create")),1)]),a("div",V,[a("form",{onSubmit:o[1]||(o[1]=x((...d)=>e(i)&&e(i)(...d),["prevent"]))},[a("div",{class:_(["p-8 -mr-6 -mb-8 flex flex-wrap",c.locale=="ar"?"rtl text-right":"ltr text-left"])},[s(k,{modelValue:e(t).name,"onUpdate:modelValue":o[0]||(o[0]=d=>e(t).name=d),error:e(t).errors.name,class:"pr-6 pb-8 w-full",direction:"ltr",type:"text",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),a("div",B,[s(f,{loading:e(t).processing,class:"mr-4 flex px-3 py-2 btn-green",onClick:e(i)},{default:m(()=>[l(n(r.trans("CreateAndCreateAnother")),1)]),_:1},8,["loading","onClick"]),s(f,{loading:e(t).processing,class:"px-3 py-2 flex btn-green",onClick:e(p)},{default:m(()=>[l(n(r.trans("Create")+" "+r.trans("Flag")),1)]),_:1},8,["loading","onClick"])])],32)])])],64))}});export{D as default};
