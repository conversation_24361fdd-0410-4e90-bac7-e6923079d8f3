<template>

  <div class="relative flex items-cente rounded font-maven">
    <div class="backdrop" v-if="show" @click.self="closeDropdown"></div>
      <div class="flex sm:bg-white rounded shadow-sm min-w-full">
        <button @click="show = !show" class="flex items-center justify-between p-2 h-9 w-full rounded border border-gray-300 shadow-sm
         focus:ring-blue-500 focus:border-blue-500">
            <span class="text-gray-700 whitespace-nowrap text-sm px-2">{{name}}</span>
            <svg class="w-2 h-2 fill-gray-700 mr-2 " viewBox="0 0 961.243 599.998"> <path d="M239.998 239.999L0 0h961.243L721.246 240c-131.999 132-240.28 240-240.624 239.999-.345-.001-108.625-108.001-240.624-240z" /> </svg>         
        </button>
        <transition enter-active-class="transition duration-100 ease-out transform" enter-from-class="opacity-0 scale-50" enter-to-class="opacity-100 scale-100" leave-active-class="transition duration-100 ease-in transform" leave-from-class="opacity-100 scale-100" leave-to-class="opacity-0 scale-50">
          <div v-show="show" class="z-50 px-2 pt-2 pb-4 overflow-y-auto absolute left-0 top-10 w-full bg-white rounded border shadow-md">
            <slot />
          </div>
        </transition>
      </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'

const props = defineProps({
  modelValue: String || Number,
  name:String,
});

const emits =  defineEmits(['update:modelValue']);

const show = ref(false)

const closeDropdown = () =>{
    show.value = false
}


</script>