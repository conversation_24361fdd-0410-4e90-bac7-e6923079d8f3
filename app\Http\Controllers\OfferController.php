<?php

namespace App\Http\Controllers;

use App\Models\Offer;
use App\Models\Test;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Request;
use Inertia\Inertia;
use Illuminate\Validation\Rule;

class OfferController extends Controller
{
    public function index()
    {
        return Inertia::render('Offers/Index', [
            'filters' => Request::all('search', 'trashed'),
            'offers' => Offer::orderBy('name')
                ->filter(Request::only('search', 'trashed'))
                ->paginate(10)
                ->withQueryString()
                ->through(fn ($offer) => [
                    'id' => $offer->id,
                    'name' => $offer->name,
                    'tests_count' => $offer->tests_count,
                    'price' => $offer->price,
                    'created_at' => $offer->created_at->diffForHumans(),
                    'updated_at' => $offer->updated_at->diffForHumans(),
                    'deleted_at' => $offer->deleted_at,
                ]),
        ]);
    }

    public function create()
    {
        return Inertia::render('Offers/Create', [
            'tests'    => Test::orderBy('id')->get()->map->only('id', 'short_name'),
        ]);
    }

    public function store()
    {
        \DB::transaction(function () {
            $offer = Auth::user()->offers()->create(
                Request::validate(
                    [
                        'name'        => ['required', 'max:100', 'unique:offers'],
                        'tests_count' => ['required', 'digits_between:1,3', 'numeric', 'min:1'],
                        'price'       => ['required', 'digits_between:4,8',
                            function ($attribute, $value, $fail) {
                                if ($value % 250 != 0) {
                                    $fail('The price must be divisible by 250.');
                                }
                            },
                        ],
                    ],
                    [
                        'tests_count.required' => 'At least one test must be attached.!'
                    ]
                )
            );
            foreach (Request::input('offerTests') as $test) {
                $offer->tests()->syncWithoutDetaching($test['id']);
            }
        });

        if (Request::input('createAnother')) {
            return Redirect::route('offers.create')->with('success', 'Offer created.');
        } else {
            return Redirect::route('offers')->with('success', 'Offer created.');
        }
    }

    public function edit(Offer $offer)
    {
        $offer_tests =  $offer->tests()->orderBy('id')->get()->map->only('id');

        return Inertia::render('Offers/Edit', [
            // 'tests'    => Test::orderBy('id')->get()->map->only('id', 'short_name'),
            'tests'    => Test::whereNotIn('id', $offer_tests)->orderBy('id')->get()->map->only('id', 'short_name'),
            'offer' => [
                'id' => $offer->id,
                'name' => $offer->name,
                'created_at' => $offer->created_at,
                'price' => $offer->price,
                'tests_count' => $offer->tests_count,
                'deleted_at' => $offer->deleted_at,
                'offerTests' => $offer->tests() ? $offer->tests()->orderBy('short_name')->get()->map->only('id', 'short_name') : null,
            ],
        ]);
    }

    public function update(Offer $offer)
    {
        \DB::transaction(function () use ($offer) {
            $offer->update(
                Request::validate(
                    [
                        'name' => ['required', 'max:100', Rule::unique('offers')->ignore($offer->id)],
                        'tests_count' => ['required', 'digits_between:1,3', 'numeric', 'min:1'],
                        'price'       => ['required', 'digits_between:4,8',
                        function ($attribute, $value, $fail) {
                            if ($value % 250 != 0) {
                                $fail('The price must be divisible by 250.');
                            }
                        },
                    ],
                    ],
                    [
                        'tests_count.required' => 'At least one test must be attached.!'
                    ]
                )
            );
            foreach ($offer->tests as $offer_test) {
                if (!in_array($offer_test->id, Request::input('offerTests'))) {
                    $offer->tests()->detach($offer_test->id);
                }
            }
            foreach (Request::input('offerTests') as $test) {
                $offer->tests()->syncWithoutDetaching($test['id']);
            }
        });
        return Redirect::route('offers')->with('success', 'Offer updated.');
    }

    public function destroy(Offer $offer)
    {
        $offer->delete();
        return Redirect::back()->with('success', 'Offer deleted.');
    }

    public function restore(Offer $offer)
    {
        $offer->restore();
        return Redirect::back()->with('success', 'Offer restored.');
    }
}
