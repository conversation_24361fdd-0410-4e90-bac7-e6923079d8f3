<template>
  <div class="p-2 xl:p-3">
   <Head title="TestsReport" />
   <div class="flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-4">
      <form @submit.prevent="submit">
        <div class="flex flex-wrap">
            <div class="flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2">
              <span class="inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"> {{trans('From')}}: </span>
              <input v-model="form.start_date" type="date" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"/>
            </div>
            <div class="flex rounded-md shadow-sm  mr-0.5 md:mr-1 xl:mr-2">
              <span class="inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"> {{trans('To')}}: </span>
              <input v-model="form.end_date" type="date" class="focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"/>
            </div>
            <loading-button :loading="form.processing" type="submit" class="btn-indigo flex items-center group  mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3">{{trans('Go')}}  </loading-button>
            <div class="btn-indigo  mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3" @click="tabletoExcel('TestsReport.xlsx')"> {{trans('ExportToExcel')}} </div> 
            <button type="button" class="btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2" @click="print">{{ trans("Print")}}</button> 
        </div>
      </form>
      <statistics-nav />
    </div>
    
    <div id="reportToPrint"  class="flex overflow-hidden max-w-3xl">
      <div class="flex w-full">
          <table id="table" class="w-full shadow-lg">
                <tr class="ttr"> 
                    <th colspan="6" class="tth text-center">{{trans('TestsReport')}} - <span v-if="start_date && end_date">( {{trans('From')}} <span class="text-yellow-500"> {{start_date}}</span> {{trans('To')}} <span class="text-yellow-500">{{end_date}}</span> )</span>
                  <span v-else>( <span class="text-yellow-500"> {{trans('ThisMonth')}} </span> )</span></th>
                </tr>

                <tr class="ttr">
                  <th class="tth" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'" locale colspan="3">{{trans('ReportDateAndTime')}}: {{moment(ReportDateAndTime).format('YYYY-MM-DD h:m A')}}</th>
                </tr>

                <tr class="ttr"> 
                    <th colspan="1" class="tth text-center">{{trans('Test')}}</th> 
                    <th colspan="1" class="tth text-center">{{trans('Count')}}</th> 
                </tr>
                <tr class="ttr" v-for="(test , index) in tests_array" :key="test.name">
                    <th class="tth text-left">{{index+1 + ' - ' + test.name}}</th>
                    <td class="ttd bg-gray-300" >{{test.count}}</td>
                </tr>
          </table>
      </div>
    </div>
  </div>
</template>

<script>
  export default {layout: AppLayout}
</script>
<script setup>
import StatisticsNav from '@/MyComponents/StatisticsNav.vue'
import Icon from '@/MyComponents/Icon.vue'
import JetNavLink from '@/Jetstream/NavLink.vue'
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia'
// import XLSX from 'xlsx';
import * as XLSX from 'xlsx/xlsx.mjs';
import moment from 'moment'

const props = defineProps({
    errors: Object,
    locale: String,
    ReportDateAndTime: String ,
    start_date: String,
    end_date: String,
    tests_array: Array,
});

let form = useForm({
  start_date: null,
  end_date: null,
});

let submit = () => {
  form.post(route('tests_report/fetch'),{
    preserveState: true,
    onSuccess: () => { 
        // form.reset();
      },
  })
};

let print = () => {
  setTimeout(function(){
    window.print();
  } , 50);
};
  
// this export using SheetJS (https://github.com/SheetJS/sheetjs)
// let tabletoExcel = (type, fn, dl) => {
let tabletoExcel = (fn, dl) => {
  var elt = document.getElementById('table');
  var wb = XLSX.utils.table_to_book(elt, { sheet: "sheet1" });
  return dl ? XLSX.write(wb, { bookType: type, bookSST: true, type: 'base64' }): XLSX.writeFile(wb, fn || ('MySheetName.' + (type || 'xlsx')));
}
</script>
