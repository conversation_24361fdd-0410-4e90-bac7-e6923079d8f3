<template>
  <div>
    <label class="form-label" :for="id">{{trans(label) }}:</label>
    <input :id="id" ref="input" :value="modelValue" :type="type" :autocomplete="autocomplete" :disabled="disabled"
    @input="$emit('update:modelValue', $event.target.value)" 
    :class="{rtl: direction === 'rtl', ltr: direction === 'ltr', error: error }" :placeholder="placeholder"
    class="form-input" />
    <div v-if="error" class="form-error">{{ error }}</div>
  </div>
</template>

<script setup>
import { ref, onMounted } from '@vue/runtime-core';
const props = defineProps({
  id: String,
  type: String,
  label: String,
  error: String,
  modelValue: [String, Number, Boolean],
  autocomplete: String,
  direction: String,
  disabled: String,
  placeholder: String,
  autofocus: Boolean,
})
const emits =  defineEmits(['update:modelValue']);

const input = ref(null);

//  function getAlert(){
//         emits("pressed", "some data");
//     };

onMounted(() => {
  if(props.autofocus){
    focus();
  }
});

let focus = () => {
  input.value.focus();
};
</script>

// The "script setup syntax provides the ability to express equivalent functionality of most 
existing Options API options except for a few:
// name
// inheritAttrs
// Custom options needed by plugins or libraries
// If you need to declare these options, use a separate normal script block with export default:
<script>
  export default {
    // name: 'CustomName',
    inheritAttrs: true,
    // customOptions: {},
  }
</script>