import{Q as x,o as l,c,b as i,u as t,a as e,B as u,d as h,t as a,y as w,C as f,E as k,z as D,F as g,D as B,H as L,L as p,U as y}from"./app.5bf25e6f.js";import{A as V,I as N}from"./AppLayout.14f8c8f6.js";import{_ as C}from"./TextInput.48e8e32c.js";import{L as E}from"./LoadingButton.c8fb65b2.js";import{T}from"./TrashedMessage.5487e7e2.js";import"./plugin-vue_export-helper.21dcd24c.js";const j={class:"p-4"},A={class:"mb-2 flex justify-start max-w-xl"},I={class:"font-bold text-2xl"},S=e("span",{class:"text-indigo-400 font-medium"}," /",-1),F={class:"bg-white rounded-md shadow-sm overflow-hidden max-w-xl"},H={class:"px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between"},R=e("hr",{class:"border-2"},null,-1),U={class:"shadow overflow-x-auto rounded-lg bg-white max-w-xl mt-4"},z={class:"w-full divide-y divide-gray-200"},M={class:"bg-gray-50"},O={class:"bg-green-500"},Q={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},$={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},q={class:"border-t p-0"},G={class:"px-6 py-2 flex items-center"},J={class:"border-t p-0"},K={class:"px-6 py-2"},P={key:0},W=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No tests found.",-1),X=[W],Y={layout:V},re=Object.assign(Y,{__name:"Edit",props:{device:Object,locale:String},setup(d){const m=d;let r=x({_method:"put",name:m.device.name}),v=()=>{r.post(route("devices.update",m.device.id),{onSuccess:()=>r.reset("name")})},b=()=>{confirm("Are you sure you want to delete this device?")&&y.Inertia.delete(route("devices.destroy",m.device.id))},_=()=>{confirm("Are you sure you want to restore this device?")&&y.Inertia.put(route("devices.restore",m.device.id))};return(s,n)=>(l(),c(g,null,[i(t(L),{title:"Edit Device"}),e("div",j,[e("div",A,[e("h2",I,[i(t(p),{class:"text-indigo-400 hover:text-indigo-600",href:s.route("devices")},{default:u(()=>[h(a(s.trans("DevicesList")),1)]),_:1},8,["href"]),S,h(" "+a(t(r).name),1)])]),e("div",F,[d.device.deleted_at?(l(),w(T,{key:0,class:"mb-6",onRestore:t(_)},{default:u(()=>[h(a(s.trans("This")+" "+s.trans("Device")+" "+s.trans("HasBeenDeleted")),1)]),_:1},8,["onRestore"])):f("",!0),e("form",{onSubmit:n[2]||(n[2]=k((...o)=>t(v)&&t(v)(...o),["prevent"]))},[e("div",{class:D(["p-8 -mr-6 -mb-8 flex flex-wrap",d.locale=="ar"?"rtl text-right":"ltr text-left"])},[i(C,{modelValue:t(r).name,"onUpdate:modelValue":n[0]||(n[0]=o=>t(r).name=o),error:t(r).errors.name,type:"text",class:"pr-6 pb-8 w-full",direction:"ltr",label:"Name",autofocus:!0},null,8,["modelValue","error"])],2),e("div",H,[i(E,{loading:t(r).processing,class:"btn-green",type:"submit"},{default:u(()=>[h(a(s.trans("Update")+" "+s.trans("Device")),1)]),_:1},8,["loading"]),d.device.deleted_at?f("",!0):(l(),c("button",{key:0,class:"text-red-600 hover:underline",tabindex:"-1",type:"button",onClick:n[1]||(n[1]=(...o)=>t(b)&&t(b)(...o))},a(s.trans("Delete")+" "+s.trans("Device")),1))])],32)]),R,e("div",U,[e("table",z,[e("thead",M,[e("tr",O,[e("th",Q,a(s.trans("RelatedTests")),1),e("th",$,a(s.trans("Edit")),1)])]),(l(!0),c(g,null,B(d.device.tests,o=>(l(),c("tr",{key:o.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",q,[i(t(p),{href:s.route("tests.edit",o.id),tabindex:"-1"},{default:u(()=>[e("div",G,a(o.short_name),1)]),_:2},1032,["href"])]),e("td",J,[i(t(p),{class:"flex items-center text-right text-sm font-medium",href:s.route("tests.edit",o.id),tabindex:"-1"},{default:u(()=>[e("div",K,[i(N,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),d.device.tests.length===0?(l(),c("tr",P,X)):f("",!0)])])])],64))}});export{re as default};
