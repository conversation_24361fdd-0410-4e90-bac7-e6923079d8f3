import{o as r,c as o,A as n}from"./app.5bf25e6f.js";const s=["type"],c={__name:"<PERSON><PERSON>",props:{type:{type:String,default:"submit"}},setup(e){return(t,a)=>(r(),o("button",{type:e.type,class:"inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring focus:ring-gray-300 disabled:opacity-25 transition"},[n(t.$slots,"default")],8,s))}};export{c as _};
