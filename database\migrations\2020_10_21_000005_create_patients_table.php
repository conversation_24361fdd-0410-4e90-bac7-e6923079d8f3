<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePatientsTable extends Migration
{
    public function up()
    {
        Schema::enableForeignKeyConstraints();
        Schema::create('patients', function (Blueprint $table) {

            $table->id();
            $table->string('name', 35)->unique();
            $table->integer('age')->nullable();
            $table->integer('age_type')->nullable();
            $table->tinyInteger('gender')->nullable();
            $table->tinyInteger('marital')->nullable();
            $table->tinyInteger('smoking')->nullable();
            $table->string('mobile', 16)->nullable();
            $table->foreignId('user_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down()
    {
        Schema::dropIfExists('patients');
    }
}
