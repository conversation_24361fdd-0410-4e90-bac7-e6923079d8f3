<template>
<Head title="Edit User" />
<div class="p-2 sm:p-4">
    <div class="mb-2 flex justify-start max-w-3xl">
      <h2 class="font-bold text-2xl">
        <Link class="text-indigo-400 hover:text-indigo-600" :href="route('users')">{{ trans('UsersList') }}</Link>
        <span class="text-indigo-400 font-medium"> / </span>
        {{ form.name }}
      </h2>
      <img v-if="userForEdit.photo" class="block w-10 h-10 rounded-full ml-4" :src="`/storage/${userForEdit.photo}`" />
    </div>
    <div class="bg-white rounded-md shadow overflow-hidden max-w-3xl">
      <trashed-message v-if="userForEdit.deleted_at" class="mb-6" @restore="restore">
      {{ trans('This') + ' ' +  trans('User') + ' ' + trans('HasBeenDeleted')}}
    </trashed-message>
      <form @submit.prevent="update">
        <div class="p-8 -mr-6 -mb-8 flex flex-wrap" :class="locale == 'ar'? 'rtl text-right' :  'ltr text-left'">
          <text-input   v-model="form.name"     :error="form.errors.name"     type="text"     direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" label="Username" />
          <text-input   v-model="form.email"    :error="form.errors.email"    type="email"    direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" label="Email" />
          <text-input   v-model="form.password" :error="form.errors.password" type="password" direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" autocomplete="new-password" label="Password" />
          <select-input v-model="form.role"     :error="form.errors.role"                     direction="ltr" class="pr-6 pb-8 w-full lg:w-1/2" label="Role">
            <option value="Administrator">Administrator</option>
            <option value="Reception_staff">Reception_Staff</option>
          </select-input>
        </div>
        <div class="px-8 py-4 bg-gray-50 border-t border-gray-100 flex items-center justify-between">
          <loading-button v-if="userForEdit.can.edit" :loading="form.processing" class="btn-green" type="submit">{{trans('Update') + ' ' + trans('User')}}</loading-button>
          <button v-if="!userForEdit.deleted_at && userForEdit.can.delete" class="text-red-600 hover:underline" tabindex="-1" type="button" @click="destroy">{{trans('Delete') + ' ' + trans('User')}}</button>
        </div>
      </form>
    </div>
</div>
</template>

<script>
  export default {layout: AppLayout}
</script>

<script setup>
import AppLayout from '@/Layouts/AppLayout.vue'
import TextInput from '@/MyComponents/TextInput.vue'
import FileInput from '@/MyComponents/FileInput.vue'
import SelectInput from '@/MyComponents/SelectInput.vue'
import LoadingButton from '@/MyComponents/LoadingButton.vue'
import TrashedMessage from '@/MyComponents/TrashedMessage.vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3';
import { reactive } from '@vue/reactivity'
import { Inertia } from '@inertiajs/inertia'

const props = defineProps({
  locale: String,
  userForEdit: Object,
});

let form = useForm({
    // _token: document.head.querySelector('meta[name="csrf_token"]').content,
    _method: 'put',
    name: props.userForEdit.name,
    email: props.userForEdit.email,
    password: null,
    role: props.userForEdit.role,
});

let update = () => {
    form.post(route('users.update', props.userForEdit.id), {
    onSuccess: () => form.reset('password', 'photo'),
    })
};

let destroy = () => {
    if (confirm('Are you sure you want to delete this user?')) {
    Inertia.delete(route('users.destroy', props.userForEdit.id))
    }
};

let restore = () => {
    if (confirm('Are you sure you want to restore this user?')) {
    Inertia.put(route('users.restore', props.userForEdit.id))
    }
};

</script>