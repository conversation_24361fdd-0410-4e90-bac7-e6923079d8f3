<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\User;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // Users Authorization
        Gate::define('manage-users',      fn (User $user) => $user->isAdmin || $user->role === 'Administrator');

        // Force Delete Authorization
        Gate::define('force-delete',      fn (User $user) => $user->isAdmin || $user->role === 'Administrator');

        // Settings Authorization
        Gate::define('manage-settings',   fn (User $user) => $user->isAdmin || $user->role === 'Administrator');

        // Reception Authorization
        Gate::define('manage-reception',  fn (User $user) => $user->isAdmin || $user->role === 'Administrator' || $user->role === 'Reception_staff');

        // Visits Authorization
        Gate::define('manage-visits',     fn (User $user) => $user->isAdmin || $user->role === 'Administrator');

        // Patient Authorization
        Gate::define('manage-patients',   fn (User $user) => $user->isAdmin || $user->role === 'Administrator');

        // Reports Authorization
        Gate::define('manage-reports',    fn (User $user) => $user->isAdmin || $user->role === 'Administrator');
    }
}
