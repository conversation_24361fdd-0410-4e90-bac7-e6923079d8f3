<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Test extends Model
{
    use HasFactory;
    use SoftDeletes;

    public function resolveRouteBinding($value, $field = null)
    {
        return $this->where($field ?? 'id', $value)->withTrashed()->firstOrFail();
    }

    protected $fillable = [
        'id', 'user_id', 'short_name', 'full_name', 'sequence', 'lab_to_patient_price', 'lab_to_lab_price',
        'category_id', 'default_device_id', 'result_type', 'created_at', 'updated_at', 'deleted_at'
    ];
    protected $hidden = ['pivot'];
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    public function visits()
    {
        return $this->belongsToMany(Visit::class)->withTimestamps()->withPivot('result', 'value', 'flag_id', 'device_id', 'offer_id');
    }

    public function groups()
    {
        return $this->belongsToMany(Group::class);
    }

    public function offers()
    {
        return $this->belongsToMany(Offer::class)->withTimestamps();
    }

    public function user()
    {
        return $this->BelongsTo(User::class);
    }

    public function devices()
    {
        return $this->belongsToMany(Device::class)->withPivot('normal_range');
    }

    public function results()
    {
        return $this->belongsToMany(Result::class);
    }

    public function scopeFilter($query, array $filters)
    {
        $query
            ->when($filters['search'] ?? null, function ($query, $search) {
                $query->where(function ($query) use ($search) {
                    $query->where('short_name', 'like', '%' . $search . '%')
                        ->orWhere('full_name', 'like', '%' . $search . '%');
                });
            })

            ->when($filters['requestedTestsIds'] ?? null, function ($query, $requestedTestsIds) {
                $query->where(function ($query) use ($requestedTestsIds) {
                    $query->whereNotIn('id', $requestedTestsIds);
                });
            })



            ->when($filters['category_id'] ?? null, function ($query, $category_id) {
                $query->whereHas('category', function ($query) use ($category_id) {
                    $query->where('id', '=', $category_id);
                });
            })



            ->when($filters['category_id'] ?? null, function ($query, $category_id) {
                $query->where('category_id', '=', $category_id);
            })

            ->when($filters['trashed'] ?? null, function ($query, $trashed) {
                if ($trashed === 'with') {
                    $query->withTrashed();
                } elseif ($trashed === 'only') {
                    $query->onlyTrashed();
                }
            });

        if (request()->has(['field', 'direction'])) {
            $query->orderBy(request('field'), request('direction'));
        } else {
            $query->orderBy('sequence', 'asc');
        }
    }
}
