import{o as r,c as i,F as w,D as y,y as I,B as x,b as m,z as M,d as N,t as a,u as s,L as j,k as L,E as B,C as v,a as e,K as u,M as F,A as O,J as A,v as $,Q as E,S as h,R as b,H as z,f as R}from"./app.5bf25e6f.js";import{P as Y}from"./Pagination.b9f6e44a.js";import{I as G,A as H}from"./AppLayout.14f8c8f6.js";import{D as C}from"./DropdownSearch.7a091d54.js";import{L as W}from"./LoadingButton.c8fb65b2.js";import{h as J}from"./moment.9709ab41.js";import{u as K,w as Q,a as q}from"./xlsx.0799a57e.js";import"./plugin-vue_export-helper.21dcd24c.js";/* empty css                                                                   */const X={class:"flex justify-start items-center"},Z={__name:"ResearchsNav",setup(p){let d=[{route:"researchs.VisitsIndex",url:"/researchs/VisitsIndex",label:"Visits",icon:"visit"}];return(c,_)=>(r(),i("nav",X,[(r(!0),i(w,null,y(s(d),(o,f)=>(r(),I(s(j),{onClick:_[0]||(_[0]=k=>c.sidebarOpened=!1),key:f,href:c.route(o.route),class:M(["flex items-center group py-3 mr-1 xl:mr-2",c.$page.url==o.url?"text-yellow-400":"text-indigo-200 group-hover:text-orange-500 hover:text-orange-500"])},{default:x(()=>[m(G,{name:o.icon,class:M(["w-5 h-5 mx-1",c.$page.url==o.url?"fill-white":"fill-indigo-400 group-hover:fill-white"])},null,8,["name","class"]),N(a(c.trans(o.label)),1)]),_:2},1032,["href","class"]))),128))]))}},ee={class:"relative flex items-cente rounded font-maven"},te=["onClick"],se={class:"flex sm:bg-white rounded shadow-sm min-w-full"},oe={class:"text-gray-700 whitespace-nowrap text-sm px-2"},ae=e("svg",{class:"w-2 h-2 fill-gray-700 mr-2",viewBox:"0 0 961.243 599.998"},[e("path",{d:"M239.998 239.999L0 0h961.243L721.246 240c-131.999 132-240.28 240-240.624 239.999-.345-.001-108.625-108.001-240.624-240z"})],-1),le={class:"z-50 px-2 pt-2 pb-4 overflow-y-auto absolute left-0 top-10 w-full bg-white rounded border shadow-md"},S={__name:"ResearchFilter",props:{modelValue:String||Number,name:String},emits:["update:modelValue"],setup(p,{emit:d}){const c=L(!1),_=()=>{c.value=!1};return(o,f)=>(r(),i("div",ee,[c.value?(r(),i("div",{key:0,class:"backdrop",onClick:B(_,["self"])},null,8,te)):v("",!0),e("div",se,[e("button",{onClick:f[0]||(f[0]=k=>c.value=!c.value),class:"flex items-center justify-between p-2 h-9 w-full rounded border border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"},[e("span",oe,a(p.name),1),ae]),m(A,{"enter-active-class":"transition duration-100 ease-out transform","enter-from-class":"opacity-0 scale-50","enter-to-class":"opacity-100 scale-100","leave-active-class":"transition duration-100 ease-in transform","leave-from-class":"opacity-100 scale-100","leave-to-class":"opacity-0 scale-50"},{default:x(()=>[u(e("div",le,[O(o.$slots,"default")],512),[[F,c.value]])]),_:3})])]))}},ne={class:"p-2 xl:p-3"},re={class:"flex justify-start items-center flex-wrap bg-green-600 shadow px-1 md:px-2 xl:px-3 py-2 rounded-md mb-2"},ie={class:"flex flex-wrap"},de={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},ce={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},pe={class:"flex rounded-md shadow-sm mr-0.5 md:mr-1 xl:mr-2"},ue={class:"inline-flex items-center px-1 md:px-2 xl:px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-700 text-sm"},me={class:"flex justify-between p-2 mb-2 bg-white shadow rounded items-center"},_e={class:"flex justify-start items-center w-full"},ge={class:"overflow-y-auto max-h-96 pr-2 pl-1"},fe=e("label",{class:"form-label min-w-full pt-2"},"Age Between:",-1),he={class:"flex"},xe=e("label",{class:"block text-gray-700 pt-2"},"Gender:",-1),be=e("option",{value:null},null,-1),we=e("option",{value:"1"},"Male",-1),ye=e("option",{value:"2"},"Female",-1),ve=[be,we,ye],ke=e("label",{class:"block text-gray-700"},"Smoking:",-1),Se=e("option",{value:null},null,-1),Te=e("option",{value:"1"},"Smoker",-1),Ve=e("option",{value:"2"},"Non-Smoker",-1),Pe=e("option",{value:"3"},"Ex-Smoker",-1),Me=[Se,Te,Ve,Pe],$e=e("label",{class:"block text-gray-700"},"Marital Status:",-1),Ce=e("option",{value:null},null,-1),Ne=e("option",{value:"1"},"Single",-1),Be=e("option",{value:"2"},"Married",-1),De=e("option",{value:"3"},"Divorced",-1),Ue=e("option",{value:"4"},"Widowed",-1),Ie=[Ce,Ne,Be,De,Ue],je=e("label",{class:"form-label min-w-full"},"Between:",-1),Le={class:"flex"},Fe=R('<option value="10"> 10 Per Page </option><option value="100"> 100 Per Page </option><option value="200"> 200 Per Page </option><option value="300"> 300 Per Page </option><option value="1000">1000 Per Page </option><option value="2000">2000 Per Page </option><option value="5000">5000 Per Page </option>',7),Oe=[Fe],Ae={class:"flex items-center whitespace-nowrap px-4 h-9 rounded border border-gray-300 shadow-sm focus:ring-blue-500 focus:border-blue-500"},Ee={id:"reportToPrint",class:"flex overflow-hidden"},ze={class:"shadow overflow-x-auto rounded-lg bg-white min-w-full"},Re={id:"table",class:"min-w-full divide-y divide-gray-200"},Ye={class:"bg-gray-50"},Ge={class:"bg-green-500"},He={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},We={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Je={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Ke={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Qe={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},qe={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Xe={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Ze={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},et={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},tt={class:"border-t px-8 py-1"},st={class:"border-t px-8 py-1 whitespace-nowrap"},ot={class:"border-t px-8 py-1"},at={class:"border-t px-8 py-1"},lt={class:"border-t px-2 py-1"},nt={key:0},rt={key:1},it={key:2},dt={key:3},ct={key:4},pt={class:"border-t px-2 py-1"},ut={key:0},mt={key:1},_t={key:2},gt={key:3},ft={key:0,class:"border-t px-8"},ht={class:"border-t px-8"},xt={key:0},bt={class:"border-t px-8 whitespace-nowrap"},wt={key:0},yt=e("td",{class:"border-t px-8 py-4 min-w-full",colspan:"14"},"No visits found.",-1),vt=[yt],kt=e("hr",{class:"bg-gray-300 pt-px"},null,-1),St={layout:H},Ut=Object.assign(St,{__name:"VisitsIndex",props:{errors:Object,locale:String,visits:Object,filters:Object,tracking_tags:Array,tests:Array},setup(p){const d=p;let c=$(d.tracking_tags.slice()),_=$(d.tests.slice()),o=E({start_date:d.filters.start_date,end_date:d.filters.end_date,test_id:d.filters.test_id,test_min_value:d.filters.test_min_value,test_max_value:d.filters.test_max_value,tracking_tag_id:d.filters.tracking_tag_id,gender:d.filters.gender?d.filters.gender:null,smoking:d.filters.smoking,marital:d.filters.marital,age_min:d.filters.age_min,age_max:d.filters.age_max,per_page:d.filters.per_page>10?d.filters.per_page:10}),f=l=>{o.tracking_tag_id=l.id},k=l=>{o.test_id=l.id},T=()=>{Object.assign(o,{start_date:null,end_date:null,test_id:null,test_min_value:null,test_max_value:null,tracking_tag_id:null,gender:null,smoking:null,marital:null,age_min:null,age_max:null,per_page:null}),o.get(route("researchs.VisitsIndex"))},V=()=>{o.get(route("researchs.VisitsIndex"),{preserveState:!0})},P=()=>{setTimeout(function(){window.print()},50)},D=(l,n)=>{var t=document.getElementById("table"),g=K.table_to_book(t,{sheet:"sheet1"});return n?Q(g,{bookType:type,bookSST:!0,type:"base64"}):q(g,l||"MySheetName."+(type||"xlsx"))};return(l,n)=>(r(),i("div",ne,[m(s(z),{title:"Visits"}),e("div",re,[e("form",{onSubmit:n[4]||(n[4]=B((...t)=>s(V)&&s(V)(...t),["prevent"]))},[e("div",ie,[e("div",de,[e("span",ce,a(l.trans("From"))+": ",1),u(e("input",{"onUpdate:modelValue":n[0]||(n[0]=t=>s(o).start_date=t),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,s(o).start_date]])]),e("div",pe,[e("span",ue,a(l.trans("To"))+": ",1),u(e("input",{"onUpdate:modelValue":n[1]||(n[1]=t=>s(o).end_date=t),type:"date",class:"focus:ring-indigo-500 focus:border-indigo-500 flex-1 block w-full rounded-none rounded-r-md sm:text-sm border-gray-300"},null,512),[[h,s(o).end_date]])]),m(W,{loading:s(o).processing,type:"submit",class:"btn-indigo flex items-center group mr-0.5 md:mr-1 xl:mr-2 lg:mr-1 px-1 md:px-2 xl:px-3"},{default:x(()=>[N(a(l.trans("Go")),1)]),_:1},8,["loading"]),e("div",{class:"btn-indigo mr-0.5 md:mr-1 xl:mr-2 cursor-pointer px-1 md:px-2 xl:px-3",onClick:n[2]||(n[2]=t=>s(D)("visits.xlsx"))},a(l.trans("ExportThisPage")),1),e("button",{type:"button",class:"btn-indigo cursor-pointer px-1 md:px-2 xl:px-3 mr-0.5 md:mr-1 xl:mr-2",onClick:n[3]||(n[3]=(...t)=>s(P)&&s(P)(...t))},a(l.trans("Print")),1)])],32),m(Z)]),e("section",me,[e("div",_e,[m(S,{class:"mr-1 w-3/12",name:"Filters"},{default:x(()=>[e("div",ge,[fe,e("div",he,[u(e("input",{"onUpdate:modelValue":n[5]||(n[5]=t=>s(o).age_min=t),type:"number",placeholder:"Min",class:"form-input w-1/2 mr-1"},null,512),[[h,s(o).age_min]]),u(e("input",{"onUpdate:modelValue":n[6]||(n[6]=t=>s(o).age_max=t),type:"number",placeholder:"Max",class:"form-input w-1/2"},null,512),[[h,s(o).age_max]])]),xe,u(e("select",{"onUpdate:modelValue":n[7]||(n[7]=t=>s(o).gender=t),class:"mt-1 w-full form-select"},ve,512),[[b,s(o).gender,void 0,{number:!0}]]),ke,u(e("select",{"onUpdate:modelValue":n[8]||(n[8]=t=>s(o).smoking=t),class:"mt-1 w-full form-select"},Me,512),[[b,s(o).smoking]]),$e,u(e("select",{"onUpdate:modelValue":n[9]||(n[9]=t=>s(o).marital=t),class:"mt-1 w-full form-select"},Ie,512),[[b,s(o).marital]])])]),_:1}),m(S,{class:"mr-1 w-3/12",name:s(o).tracking_tag_id?s(c)[s(c).findIndex(t=>t.id==s(o).tracking_tag_id)].name:"Select Tracking Tag"},{default:x(()=>[m(C,{class:"w-full ltr pt-1 h-32",options:s(c),onSelect:s(f),CloseOnSelect:!0,fixedLabel:!1,label:"Select Tracking Tag",searchBy:"name",direction:"ltr",placeholder:"Search for tracking tags"},null,8,["options","onSelect"])]),_:1},8,["name"]),m(S,{class:"mr-1 w-3/12",name:s(o).test_id?s(_)[s(_).findIndex(t=>t.id==s(o).test_id)].short_name:"Select Test"},{default:x(()=>[m(C,{class:"w-full ltr pt-2 h-32",options:s(_),onSelect:s(k),CloseOnSelect:!0,fixedLabel:!1,label:"Select Test",searchBy:"short_name",direction:"ltr",placeholder:"Search for tests"},null,8,["options","onSelect"]),je,e("div",Le,[u(e("input",{"onUpdate:modelValue":n[10]||(n[10]=t=>s(o).test_min_value=t),type:"number",placeholder:"Min",class:"form-input w-1/2 mr-1"},null,512),[[h,s(o).test_min_value]]),u(e("input",{"onUpdate:modelValue":n[11]||(n[11]=t=>s(o).test_max_value=t),type:"number",placeholder:"Max",class:"form-input w-1/2"},null,512),[[h,s(o).test_max_value]])])]),_:1},8,["name"]),e("button",{class:"mx-2 text-sm text-gray-500 hover:text-gray-700 focus:text-indigo-500",onClick:n[12]||(n[12]=(...t)=>s(T)&&s(T)(...t))},"Reset")]),u(e("select",{"onUpdate:modelValue":n[13]||(n[13]=t=>s(o).per_page=t),class:"mr-1 block py-2 px-4 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm w-2/12"},Oe,512),[[b,s(o).per_page,void 0,{number:!0}]]),e("div",Ae,"Total: "+a(p.visits.total),1)]),e("div",Ee,[e("div",ze,[e("table",Re,[e("thead",Ye,[e("tr",Ge,[e("th",He,a(l.trans("ID")),1),e("th",We,a(l.trans("Name")),1),e("th",Je,a(l.trans("Age")),1),e("th",Ke,a(l.trans("Gender")),1),e("th",Qe,a(l.trans("MaritalStatus")),1),e("th",qe,a(l.trans("Smoking")),1),e("th",Xe,a(l.trans("Tests")),1),e("th",Ze,a(l.trans("TrackingTags")),1),e("th",et,a(l.trans("Date")),1)])]),(r(!0),i(w,null,y(p.visits.data,t=>(r(),i("tr",{key:t.id},[e("td",tt,a(t.patient_id),1),e("td",st,a(t.name),1),e("td",ot,a(t.age),1),e("td",at,a(t.gender===1?l.trans("Male"):l.trans("Female")),1),e("td",lt,[t.marital===1?(r(),i("span",nt,a(l.trans("Single")),1)):t.marital===2?(r(),i("span",rt,a(l.trans("Married")),1)):t.marital===3?(r(),i("span",it,a(l.trans("Divorced")),1)):t.marital===4?(r(),i("span",dt,a(l.trans("Widowed")),1)):(r(),i("span",ct,"Unknown"))]),e("td",pt,[t.smoking===1?(r(),i("span",ut,a(l.trans("Smoker")),1)):t.smoking===2?(r(),i("span",mt,a(l.trans("NonSmoker")),1)):t.smoking===3?(r(),i("span",_t,a(l.trans("ExSmoker")),1)):(r(),i("span",gt,a(l.trans("Unknown")),1))]),t.tests?(r(),i("td",ft,[(r(!0),i(w,null,y(t.tests,g=>(r(),i("span",{key:g.id},a(g.short_name)+"=>"+a(g.pivot.value)+", ",1))),128))])):v("",!0),e("td",ht,[t.tracking_tags?(r(),i("span",xt,[(r(!0),i(w,null,y(t.tracking_tags,(g,U)=>(r(),i("span",{key:U,class:"inline-flex text-red-500"},a(g.name)+" ,",1))),128))])):v("",!0)]),e("td",bt,a(s(J)(t.created_at).format("YYYY-MM-DD")),1)]))),128)),p.visits.data.length===0?(r(),i("tr",wt,vt)):v("",!0)]),kt,m(Y,{class:"px-3 py-2 bg-white border-none border-t p-0",links:p.visits.links,from:p.visits.from,to:p.visits.to,total:p.visits.total},null,8,["links","from","to","total"])])])]))}});export{Ut as default};
