import{v as w,q as b,o as c,c as f,b as a,u as r,a as e,B as d,t as o,F as u,D as g,C as _,H as y,K as v,R as k,L as n,d as V,y as C,U as N}from"./app.5bf25e6f.js";import{A as O,I as m}from"./AppLayout.14f8c8f6.js";import{t as j,S as B,p as S}from"./SearchFilter.f110f3d1.js";import{P as T}from"./Pagination.b9f6e44a.js";import"./plugin-vue_export-helper.21dcd24c.js";const I={class:"p-4 max-w-7xl"},L={class:"flex flex-col sm:flex-row mb-3 justify-between items-center"},U=e("label",{class:"block text-gray-700"},"Trashed:",-1),A=e("option",{value:null},null,-1),D={value:"with"},F={value:"only"},P={class:"inline sm:hidden"},E={class:"hidden sm:flex w-48 text-center"},H={class:"shadow overflow-x-auto rounded-lg bg-white"},R={class:"min-w-full divide-y divide-gray-200"},q={class:"bg-gray-50"},K={class:"bg-green-500"},M={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},W={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},z={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},G={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},J={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},Q={scope:"col",class:"px-4 py-3 text-left text-sm font-bold text-white tracking-wide whitespace-nowrap"},X={class:"border-t p-0"},Y={class:"px-6 py-2 flex items-center"},Z={class:"ml-4"},$={class:"flex items-center text-sm font-medium text-gray-900"},ee={class:"border-t p-0"},te={class:"px-6 py-2"},se={class:"border-t p-0"},oe={class:"px-6 py-2"},ae={class:"border-t p-0"},re={class:"px-6 py-2"},de={class:"border-t p-0"},ie={class:"px-6 py-2"},le={class:"border-t p-0"},ne={class:"px-6 py-2"},ce={key:0},he=e("td",{class:"border-t p-0 px-6 py-2",colspan:"4"},"No offers found.",-1),fe=[he],pe=e("hr",{class:"bg-gray-300 pt-px"},null,-1),ue={layout:O},ge=Object.assign(ue,{__name:"Index",props:{filters:Object,offers:Object},setup(i){const p=i,l=w({search:p.filters.search,trashed:p.filters.trashed});b(l,j(function(){N.Inertia.get(route("offers"),S(l),{preserveState:!0,replace:!0})},300),{deep:!0});function x(){Object.assign(l,{search:null,trashed:null})}return(t,h)=>(c(),f(u,null,[a(r(y),{title:"Offers"}),e("div",I,[e("div",L,[a(B,{modelValue:l.search,"onUpdate:modelValue":h[1]||(h[1]=s=>l.search=s),direction:"ltr",placeholder:"Search for offers.....",class:"mb-2 sm:mb-0 w-full",onReset:x},{default:d(()=>[U,v(e("select",{"onUpdate:modelValue":h[0]||(h[0]=s=>l.trashed=s),class:"mt-1 w-full form-select"},[A,e("option",D,o(t.trans("WithTrashed")),1),e("option",F,o(t.trans("OnlyTrashed")),1)],512),[[k,l.trashed]])]),_:1},8,["modelValue"]),a(r(n),{class:"w-full sm:w-48 flex justify-center text-center bg-green-600 hover:bg-green-700 text-gray-50 py-2 px-4 rounded-md font-medium",href:t.route("offers.create")},{default:d(()=>[e("span",P,o(t.trans("Create")),1),e("span",E,o(t.trans("CreateANewOffer")),1)]),_:1},8,["href"])]),e("div",H,[e("table",R,[e("thead",q,[e("tr",K,[e("th",M,o(t.trans("Name")),1),e("th",W,o(t.trans("TestsCount")),1),e("th",z,o(t.trans("Price")),1),e("th",G,o(t.trans("Created_at")),1),e("th",J,o(t.trans("Updated_at")),1),e("th",Q,o(t.trans("Edit")),1)])]),(c(!0),f(u,null,g(i.offers.data,s=>(c(),f("tr",{key:s.id,class:"hover:bg-gray-100 focus-within:bg-gray-100"},[e("td",X,[a(r(n),{href:t.route("offers.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",Y,[e("div",Z,[e("div",$,[V(o(s.name)+" ",1),s.deleted_at?(c(),C(m,{key:0,name:"trash",class:"flex-shrink-0 w-3 h-3 fill-gray-200 ml-2"})):_("",!0)])])])]),_:2},1032,["href"])]),e("td",ee,[a(r(n),{href:t.route("offers.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",te,o(s.tests_count),1)]),_:2},1032,["href"])]),e("td",se,[a(r(n),{href:t.route("offers.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",oe,o(s.price),1)]),_:2},1032,["href"])]),e("td",ae,[a(r(n),{href:t.route("offers.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",re,o(s.created_at),1)]),_:2},1032,["href"])]),e("td",de,[a(r(n),{href:t.route("offers.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",ie,o(s.updated_at),1)]),_:2},1032,["href"])]),e("td",le,[a(r(n),{class:"flex items-center text-right text-sm font-medium",href:t.route("offers.edit",s.id),tabindex:"-1"},{default:d(()=>[e("div",ne,[a(m,{name:"cheveron-right",class:"block w-6 h-6 fill-gray-400"})])]),_:2},1032,["href"])])]))),128)),i.offers.data.length===0?(c(),f("tr",ce,fe)):_("",!0)]),pe,a(T,{class:"px-6 py-2 bg-white border-none border-t p-0",links:i.offers.links,from:i.offers.from,to:i.offers.to,total:i.offers.total},null,8,["links","from","to","total"])])])],64))}});export{ge as default};
