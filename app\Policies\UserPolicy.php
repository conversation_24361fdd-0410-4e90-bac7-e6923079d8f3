<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    public function create(User $user)
    {
        return $user->isAdmin || $user->role === "Administrator";
    }

    public function edit(User $user, User $userFroEdit)
    {
        return ($user->isAdmin || $user->role === "Administrator") && ($user->id != $userFroEdit->id) && (!$userFroEdit->isAdmin);
    }

    public function delete(User $user, User $userFroDelete)
    {
        return ($user->isAdmin || $user->role === "Administrator") && ($user->id != $userFroDelete->id) && (!$userFroDelete->isAdmin);
    }
}
