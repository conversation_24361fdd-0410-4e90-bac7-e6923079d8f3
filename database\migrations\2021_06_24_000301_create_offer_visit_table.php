<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOfferVisitTable extends Migration
{
    public function up()
    {
        Schema::create('offer_visit', function (Blueprint $table) {
            $table->id();
            $table->foreignId('offer_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->foreignId('visit_id')->constrained()->onDelete('cascade')->onUpdate('cascade');

            $table->foreignId('user_id')->constrained()->onDelete('restrict')->onUpdate('cascade');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('offer_visit');
    }
}
